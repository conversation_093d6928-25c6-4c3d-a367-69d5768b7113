<?php

namespace App\Observers;

use App\Models\Enrollment;
use App\Models\Course;
use App\Models\User;
use Illuminate\Support\Facades\Mail;
use App\Mail\EnrollmentConfirmation;
use App\Events\CourseEnrolled;
use Exception;
use Illuminate\Support\Facades\Log;

class EnrollmentObserver
{
    /**
     * Handle the Enrollment "created" event.
     *
     * @param  \App\Models\Enrollment  $enrollment
     * @return void
     */
    public function created(Enrollment $enrollment)
    {
        Log::info('EnrollmentObserver created method called', [
            'enrollment_id' => $enrollment->id,
            'course_id' => $enrollment->course_id,
            'user_id' => $enrollment->user_id,
            'trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 5)
        ]);

        // Get course and user information
        $course = Course::find($enrollment->course_id);
        $user = User::find($enrollment->user_id);

        if ($user && $course) {
            try {
                // Send confirmation email
                Log::info('Sending enrollment confirmation email', [
                    'user_email' => $user->email,
                    'course_name' => $course->title
                ]);
                Mail::to($user->email)->send(new EnrollmentConfirmation($user, $course));
                Log::info('Email sent successfully');

            } catch (Exception $e) {
                // Log the error instead of silently ignoring
                Log::error('Failed to send enrollment confirmation email', [
                    'error' => $e->getMessage(),
                    'enrollment_id' => $enrollment->id
                ]);
            }

            // Trigger CRM update event
            try {
                $enrollmentValue = $course->price ?? 0;
                event(new CourseEnrolled($enrollment, $enrollmentValue));
                Log::info('CourseEnrolled event triggered successfully', [
                    'enrollment_id' => $enrollment->id,
                    'enrollment_value' => $enrollmentValue
                ]);
            } catch (Exception $e) {
                Log::error('Failed to trigger CourseEnrolled event', [
                    'error' => $e->getMessage(),
                    'enrollment_id' => $enrollment->id
                ]);
            }
        }
    }
}

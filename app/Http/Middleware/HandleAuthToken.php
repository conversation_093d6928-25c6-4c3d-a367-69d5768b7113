<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class HandleAuthToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        if ($request->has('auth_token')) {

            $token = $request->query('auth_token');
            $userId = Cache::get('auth_token_'.$token);


            if ($userId) {
                $user = User::find($userId);
                Auth::login($user);
                Cache::forget('auth_token_'.$token);
                // Chuyển hướng để loại bỏ token khỏi URL
                return redirect()->to($request->url());
            }
        }
        if (!Auth::check()) {
            return redirect()->route('login');
        }
        return $next($request);
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AllowEmbedding
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Xóa header X-Frame-Options
        $response->headers->remove('X-Frame-Options');

        // Thêm Content-Security-Policy để cho phép tất cả domain
        $response->header('Content-Security-Policy', "frame-ancestors *");

        return $response;
    }
}

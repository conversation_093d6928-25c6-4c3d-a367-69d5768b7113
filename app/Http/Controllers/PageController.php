<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Illuminate\Http\Request;

class PageController extends Controller
{
    /**
     * <PERSON><PERSON>n thị trang theo slug
     */
    public function show($slug)
    {
        $page = Page::where('slug', $slug)
                   ->where('status', 'published')
                   ->firstOrFail();

        // Tăng view count nếu cần
        // $page->increment('view_count');

        // Nếu template là blank, trả về HTML thuần túy
        if ($page->template === 'blank') {
            return response($page->content)
                ->header('Content-Type', 'text/html; charset=UTF-8');
        }

        return view('frontend.page.show', compact('page'));
    }

    /**
     * Lấy danh sách pages cho menu
     */
    public function getMenuPages()
    {
        return Page::published()
                  ->inMenu()
                  ->get();
    }
}

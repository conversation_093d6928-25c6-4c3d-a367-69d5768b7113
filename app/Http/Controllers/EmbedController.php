<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class EmbedController extends Controller
{
    /**
     * Constructor to apply middleware
     */
    public function __construct()
    {
        $this->middleware('cors');
    }

    /**
     * Show the embeddable registration form
     */
    public function showEmbedRegistrationForm(Request $request, $course_slug)
    {
        // Check if the addon is enabled, if not - return error page
        if (!addon_check('my.embed_registration')) {
            return response()->view('errors.feature_disabled', [
                'feature_name' => 'Nhúng Form Đăng Ký',
                'addon_name' => 'my.embed_registration'
            ], 403);
        }
        
        // Set CORS headers for embedding on external domains
        $response = response()->view('embed.registration', [
            'course_slug' => $course_slug,
            'redirect_url' => $request->input('redirect_url', ''),
            'main_title' => $request->input('main_title', 'Lộ trình khóa học'),
            'highlight_title' => $request->input('highlight_title', 'E-learning Master'),
            'subtitle' => $request->input('subtitle', 'Đăng ký nhận ưu đãi ngay!'),
            'button_text' => $request->input('button_text', 'ĐĂNG KÝ NGAY'),
            'utm_source' => $request->input('utm_source', '')
        ]);

        // Allow embedding from any domain
        $response->header('Access-Control-Allow-Origin', '*');
        $response->header('Access-Control-Allow-Methods', 'GET, POST');
        $response->header('Access-Control-Allow-Headers', 'Content-Type');

        return $response;
    }

    /**
     * Process the embedded registration form
     */
    public function processEmbedRegistration(Request $request, $course_slug)
    {
        // Check if the addon is enabled, if not - return error
        if (!addon_check('my.embed_registration')) {
            return response()->json([
                'success' => false,
                'message' => 'Tính năng nhúng form đăng ký chưa được kích hoạt'
            ], 403);
        }
        
        // For debugging
        Log::info('Registration data received:', $request->all());
        
        // Set CORS headers for API requests
        $headers = [
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'POST, GET, OPTIONS',
            'Access-Control-Allow-Headers' => 'Content-Type, X-Requested-With'
        ];

        // Check reCAPTCHA if enabled
        if (get_frontend_settings('recaptcha_status') == true && check_recaptcha($request->input('g-recaptcha-response'))== false) {
            return response()->json([
                'success' => false,
                'message' => 'Xác thực Recaptcha thất bại'
            ], 422, $headers);
        }

        // Validate input
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email'],
            'password' => ['required', 'min:6'],
            'phone' => ['required', 'numeric'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ], 422, $headers);
        }

        // Find the course
        $course = Course::where('slug', $course_slug)->first();
        if (!$course) {
            return response()->json([
                'success' => false,
                'message' => 'Khóa học không tồn tại'
            ], 404, $headers);
        }

        // Create user
        $user_data = [
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'role' => 'student',
            'status' => 1,
            'password' => Hash::make($request->password),
        ];

        // Add UTM source if provided
        if ($request->has('utm_source') && !empty($request->utm_source)) {
            $user_data['source'] = $request->utm_source;
            // For debugging
            Log::info('Saving UTM source: ' . $request->utm_source);
        }

        if (get_settings('student_email_verification') != 1) {
            $user_data['email_verified_at'] = Carbon::now();
        }

        $user = User::create($user_data);

        try {
            event(new Registered($user));
        } catch (\Exception $e) {
            // Silent fail for SMTP issues
        }
        $token = Str::random(60);
        Cache::put('auth_token_'.$token, $user->id, now()->addMinutes(5));
        // Generate the redirect URL to the course
        $course_url = route('course.player', [
            'slug' => $course_slug,
            'auth_token' => $token
        ]);

        // If there's a custom redirect URL from the embedding site
        $redirect_url = $request->input('redirect_url', '');
        if (!empty($redirect_url)) {
            // Append course slug as query parameter to the redirect URL
            $redirect_url = rtrim($redirect_url, '/') . '?course=' . $course_slug;
        } else {
            $redirect_url = $course_url;
        }

        return response()->json([
            'success' => true,
            'message' => 'Đăng ký thành công',
            'redirect_url' => $redirect_url
        ], 200, $headers);
    }
}

<?php

namespace App\Http\Controllers;

use Anand\LaravelPaytmWallet\Facades\PaytmWallet;
use App\Models\Bootcamp;
use App\Models\BootcampPurchase;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\FileUploader;
use App\Models\OfflinePayment;
use App\Models\payment_gateway\Paystack;
use App\Models\payment_gateway\Ccavenue;
use App\Models\payment_gateway\Pagseguro;
use App\Models\payment_gateway\Paytm;
use App\Models\payment_gateway\Xendit;
use App\Models\payment_gateway\Doku;
use App\Models\payment_gateway\Skrill;
use App\Models\Payment_history;
use App\Models\TeamPackagePurchase;
use App\Models\TeamTrainingPackage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use paytm\paytmchecksum\PaytmChecksum;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{

    public function index()
    {

        $payment_details = session('payment_details');

        if (!$payment_details || !is_array($payment_details) || count($payment_details) <= 0) {
            Session::flash('error', get_phrase('Payment not configured yet'));
            return redirect()->back();
        }
        if ($payment_details['payable_amount'] <= 0) {
            Session::flash('error', get_phrase("Payable amount cannot be less than 1"));
            return redirect()->to($payment_details['cancel_url']);
        }

        $page_data['payment_details'] = $payment_details;
        $page_data['payment_gateways'] = DB::table('payment_gateways')->where('status', 1)->get();
        return view('payment.index', $page_data);
    }

    public function show_payment_gateway_by_ajax($identifier)
    {
        $page_data['payment_details'] = session('payment_details');
        $page_data['payment_gateway'] = DB::table('payment_gateways')->where('identifier', $identifier)->first();
        return view('payment.' . $identifier . '.index', $page_data);
    }

    public function payment_success($identifier, Request $request)
    {

        $payment_details = session('payment_details');
        $payment_gateway = DB::table('payment_gateways')->where('identifier', $identifier)->first();
        $model_name = $payment_gateway->model_name;
        $model_full_path = str_replace(' ', '', 'App\Models\payment_gateway\ ' . $model_name);

        $status = $model_full_path::payment_status($identifier, $request->all());
        if ($status === true) {
            $success_model = $payment_details['success_method']['model_name'];
            $success_function = $payment_details['success_method']['function_name'];

            $model_full_path = str_replace(' ', '', 'App\Models\ ' . $success_model);
            return $model_full_path::$success_function($identifier);
        } else {
            Session::flash('error', get_phrase('Payment failed! Please try again.'));
            return redirect()->to($payment_details['cancel_url']);
        }
    }


    public function payment_create($identifier)
    {
        $payment_details = session('payment_details');
        $payment_gateway = DB::table('payment_gateways')->where('identifier', $identifier)->first();
        $model_name = $payment_gateway->model_name;
        $model_full_path = str_replace(' ', '', 'App\Models\payment_gateway\ ' . $model_name);
        $created_payment_link = $model_full_path::payment_create($identifier);

        return redirect()->to($created_payment_link);
    }

    public function payment_sepay($identifier)
    {

        $payment_details = session('payment_details');
        $payment_gateway = DB::table('payment_gateways')->where('identifier', $identifier)->first();
        $model_name = $payment_gateway->model_name;
        $model_full_path = str_replace(' ', '', 'App\Models\payment_gateway\ ' . $model_name);
        $data = $model_full_path::payment_create($identifier);

        return view('payment.sepay.payment', compact('data'));
    }

    public function sepay_paymentCallback(Request $request)
    {

        $authHeader = $request->header('Authorization');
        $apiKey = str_replace('Apikey ', '', $authHeader);


        $gateway = DB::table('payment_gateways')->where('identifier', 'sepay')->first();
        $keys = json_decode($gateway->keys);


        if ($keys->api_key !== $apiKey) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Invalid API key.'
            ], 401);
        }

        $data = $request->all();
        $transferAmount = $data["transferAmount"];
        $content = $data["content"];
        $codeOnly = $content;
 
        $strictPattern = '/\b(?=.*[A-Z])(?=.*[0-9])[A-Z0-9]{12}\b/';
        if (preg_match($strictPattern, $content, $strictMatch)) {
                $codeOnly =  trim($strictMatch[0]);
        }
        $payment_details = OfflinePayment::where('transaction_content', 'LIKE', '%' . $codeOnly . '%')
            ->where('status', 0)
            ->first();
  
        if ($payment_details) {
            if ($payment_details->total_amount == $transferAmount) {

                if ($payment_details->item_type == 'course') {

                    $items = json_decode($payment_details->items);
                    foreach ($items as $item) {
                        $accept_payment = null;
                        if ($payment_details->item_type == 'course') {
                            $course = Course::where('id', $item)->first();

                            $payment['course_id'] = $course->id;
                            $payment['amount'] = $course->discount_flag == 1 ? $course->discounted_price : $course->price;
                            $payment['tax'] = $payment['amount'] * (get_settings('course_selling_tax') / 100);

                            if (get_course_creator_id($course->id)->role == 'admin') {
                                $payment['admin_revenue'] = $payment['amount'];
                            } else {
                                $payment['instructor_revenue'] = $payment['amount'] * (get_settings('instructor_revenue') / 100);
                                $payment['admin_revenue'] = $payment['amount'] - $payment['instructor_revenue'];
                            }
                            $accept_payment = Payment_history::insert($payment);
                            // start enroll user
                            if ($accept_payment) {
                                $enroll['course_id'] = $course->id;
                                $enroll['user_id'] = $payment_details['user_id'];
                                $enroll['enrollment_type'] = "paid";
                                $enroll['entry_date'] = time();
                                $enroll['created_at'] = date('Y-m-d H:i:s');
                                $enroll['updated_at'] = date('Y-m-d H:i:s');

                                // insert a new enrollment
                                Enrollment::create($enroll);

                            }
                        }
                    }
                } elseif ($payment_details->item_type == 'bootcamp') {
                    $bootcamps = Bootcamp::whereIn('id', json_decode($payment_details->items, true))->get();
                    foreach ($bootcamps as $bootcamp) {
                        $bootcamp_payment['invoice'] = '#' . Str::random(20);
                        $bootcamp_payment['user_id'] = $payment_details['user_id'];
                        $bootcamp_payment['bootcamp_id'] = $bootcamp->id;
                        $bootcamp_payment['price'] = $bootcamp->discount_flag == 1 ? $bootcamp->price - $bootcamp->discounted_price : $bootcamp->price;
                        $bootcamp_payment['tax'] = 0;
                        $bootcamp_payment['payment_method'] = 'offline';
                        $bootcamp_payment['status'] = 1;

                        // insert bootcamp purchase
                        BootcampPurchase::insert($bootcamp_payment);
                    }
                } elseif ($payment_details->item_type == 'package') {
                    $packages = TeamTrainingPackage::whereIn('id', json_decode($payment_details->items, true))->get();
                    foreach ($packages as $package) {
                        $package_payment['invoice'] = '#' . Str::random(20);
                        $package_payment['user_id'] = $payment_details['user_id'];
                        $package_payment['package_id'] = $package->id;
                        $package_payment['price'] = $package->price;
                        $package_payment['tax'] = 0;
                        $package_payment['payment_method'] = 'offline';
                        $package_payment['status'] = 1;

                        // insert package purchase
                        TeamPackagePurchase::insert($package_payment);
                    }
                }
                OfflinePayment::where('id', $payment_details->id)->update(['status' => 1]);
                return true;
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment amount does not match invoice amount'
                ]);
            }
        } else {
            Log::warning('Invoice not found: ' . $codeOnly);
            return response()->json([
                'success' => false,
                'message' => 'Invoice not found'
            ]);
        }
    }

    public function payment_razorpay($identifier)
    {

        $payment_details = session('payment_details');
        $payment_gateway = DB::table('payment_gateways')->where('identifier', $identifier)->first();
        $model_name = $payment_gateway->model_name;
        $model_full_path = str_replace(' ', '', 'App\Models\payment_gateway\ ' . $model_name);
        $data = $model_full_path::payment_create($identifier);

        return view('payment.razorpay.payment', compact('data'));
    }


    public function make_paytm_order(Request $request)
    {


        $identifier = 'paytm';
        $payment_details = session('payment_details');
        $model = $payment_details['success_method']['model_name'];
        $payment_gateway = DB::table('payment_gateways')->where('identifier', $identifier)->first();
        $user = auth()->user();

        $paytm_merchant_key = $paytm_merchant_mid = $paytm_merchant_website = $industry_type_id = $channel_id = '';
        if ($model == 'InstructorPayment') {
            $instructor_payment_keys = DB::table('users')
                ->where('id', $payment_details['items'][0]['id'])
                ->value('paymentkeys');
            $keys = isset($instructor_payment_keys) ? json_decode($instructor_payment_keys) : null;
            if ($keys) {
                $paytm_merchant_key = $keys->paytm->paytm_merchant_key;
                $paytm_merchant_mid = $keys->paytm->paytm_merchant_mid;
                $paytm_merchant_website = $keys->paytm->paytm_merchant_website;
                $industry_type_id = $keys->paytm->industry_type_id;
                $channel_id = $keys->paytm->channel_id;
            }
        } else {
            $keys = json_decode($payment_gateway->keys);
            $paytm_merchant_key = $keys->paytm_merchant_key;
            $paytm_merchant_mid = $keys->paytm_merchant_mid;
            $paytm_merchant_website = $keys->paytm_merchant_website;
            $industry_type_id = $keys->industry_type_id;
            $channel_id = $keys->channel_id;
        }

        if ($payment_gateway->test_mode == 1) {
            $PAYTM_STATUS_QUERY_NEW_URL = 'https://securegw-stage.paytm.in/merchant-status/getTxnStatus';
            $PAYTM_TXN_URL = 'https://securegw-stage.paytm.in/theia/processTransaction';
        } else {
            define('PAYTM_ENVIRONMENT', 'PROD'); // PROD or TEST
            $PAYTM_STATUS_QUERY_NEW_URL = 'https://securegw.paytm.in/merchant-status/getTxnStatus';
            $PAYTM_TXN_URL = 'https://securegw.paytm.in/theia/processTransaction';
        }
        $paramList = [];
        $paramList['MID'] = $paytm_merchant_mid;
        $paramList['ORDER_ID'] = 'ORDS2123' . $user->id;
        $paramList['CUST_ID'] = 'CUST' . $user->id;
        $paramList['INDUSTRY_TYPE_ID'] = $industry_type_id;
        $paramList['CHANNEL_ID'] = $channel_id;
        $paramList['TXN_AMOUNT'] = $payment_details['payable_amount'];
        $paramList['WEBSITE'] = $paytm_merchant_website;
        $paramList['CALLBACK_URL'] = $payment_details['success_url'] . '/' . $identifier;


        $paytmParams = array();

        $paytmParams["body"] = array(
            "requestType" => "Payment",
            "mid" => $paytm_merchant_mid,
            "websiteName" => $paytm_merchant_website,
            "orderId" => 'ORDS2123' . $user->id,
            "callbackUrl" => $payment_details['success_url'] . '/' . $identifier,
            "txnAmount" => array(
                "value" => round($payment_details['payable_amount'], 2),
                "currency" => "INR",
            ),
            "userInfo" => array(
                "custId" => "CUST_" . $user->id,
            ),
        );


        $checksum = PaytmChecksum::generateSignature(json_encode($paramList, JSON_UNESCAPED_SLASHES), $paytm_merchant_key);
        echo PaytmChecksum::verifySignature($paramList, $paytm_merchant_key, $checksum);

        // $checksum = str_replace('/', '', $checksum);
        // $checksum = str_replace('=', '', $checksum);

        $paytmParams["head"] = array(
            "signature" => $checksum,
            "channelId" => $channel_id
        );

        $post_data = json_encode($paytmParams, JSON_UNESCAPED_SLASHES);

        /* for Staging */
        $url = "https://securegw-stage.paytm.in/theia/api/v1/initiateTransaction?mid=$paytm_merchant_mid&orderId=ORDS2123" . $user->id;

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array("Content-Type: application/json"));
        $response = curl_exec($ch);
        print_r($response);


        return view('payment.paytm.paytm_merchant_checkout');
    }

    public function paytm_paymentCallback()
    {
        $transaction = PaytmWallet::with('receive');
        $response = $transaction->response();
        $order_id = $transaction->getOrderId(); // return a order id
        $transaction->getTransactionId(); // return a transaction id

        // update the db data as per result from api call
        if ($transaction->isSuccessful()) {
            Paytm::where('order_id', $order_id)->update(['status' => 1, 'transaction_id' => $transaction->getTransactionId()]);
            return redirect(route('initiate.payment'))->with('message', "Your payment is successfull.");
        } else if ($transaction->isFailed()) {
            Paytm::where('order_id', $order_id)->update(['status' => 0, 'transaction_id' => $transaction->getTransactionId()]);
            return redirect(route('initiate.payment'))->with('message', "Your payment is failed.");
        } else if ($transaction->isOpen()) {
            Paytm::where('order_id', $order_id)->update(['status' => 2, 'transaction_id' => $transaction->getTransactionId()]);
            return redirect(route('initiate.payment'))->with('message', "Your payment is processing.");
        }
        $transaction->getResponseMessage(); //Get Response Message If Available

    }

    public function webRedirectToPayFee(Request $request)
    {
        // Check if the 'auth' query parameter is present
        if (!$request->has('auth')) {
            return redirect()->route('login')->withErrors([
                'email' => 'Authentication token is missing.',
            ]);
        }

        // Remove the 'Basic ' prefix
        // $base64Credentials = $request->query('auth');
        // Remove the 'Basic ' prefix
        $base64Credentials = substr($request->query('auth'), 6);

        // Decode the base64-encoded string
        $credentials = base64_decode($base64Credentials);

        // Split the decoded string into email, password, and timestamp
        list($email, $password, $timestamp) = explode(':', $credentials);

        // Get the current timestamp
        $timestamp1 = strtotime(date('Y-m-d'));

        // Calculate the difference
        $difference = $timestamp1 - $timestamp;

        if ($difference < 86400) {
            if (auth()->attempt(['email' => $email, 'password' => $password])) {
                // Authentication passed...
                return redirect(route('cart'));
            }

            return redirect()->route('login')->withErrors([
                'email' => 'Invalid email or password',
            ]);
        } else {
            return redirect()->route('login')->withErrors([
                'email' => 'Token expired!',
            ]);
        }
    }

    public function payment_check_ajax(Request $request)
    {
        $invoice = $request->invoice;
        $offline_paypment = OfflinePayment::where('invoice', $invoice)->where('status', 1)->first();
        if (is_object($offline_paypment)) {
            return response()->json([
                'status' => true,
                'msg'=> 'Thanh toán thành công'
            ]);
        }
        return response()->json([
            'status' => false,
            'msg'=> ''
        ]);
    }
}

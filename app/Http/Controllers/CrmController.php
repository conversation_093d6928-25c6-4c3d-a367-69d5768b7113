<?php

namespace App\Http\Controllers;

use App\Models\CrmActivity;
use App\Models\LeadOpportunity;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;

class CrmController extends Controller
{
    // Thêm hoạt động CRM mới
    public function storeActivity(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'activity_type' => 'required|in:call,email,message,meeting,note',
            'description' => 'required|string',
            'result' => 'required|in:success,pending,failed,no_answer',
            'lead_status' => 'required|in:new,contacted,nurturing,qualified,proposal,closed_won,closed_lost',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $activity = new CrmActivity();
        $activity->user_id = $request->user_id;
        $activity->sale_id = Auth::id();
        $activity->activity_type = $request->activity_type;
        $activity->description = $request->description;
        $activity->result = $request->result;
        $activity->lead_status = $request->lead_status;

        if ($request->next_follow_up) {
            $activity->next_follow_up = Carbon::parse($request->next_follow_up);
        }

        $activity->save();

        return response()->json([
            'success' => true,
            'message' => 'Hoạt động đã được lưu thành công',
            'activity' => $activity
        ]);
    }

    // Lấy lịch sử hoạt động CRM của học viên
    public function getActivities(Request $request, $userId)
    {
        $activities = CrmActivity::where('user_id', $userId)
            ->with('sale:id,name,photo')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'activities' => $activities
        ]);
    }

    // Thêm cơ hội mới
    public function storeOpportunity(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'name' => 'required|string',
            'potential_value' => 'required|numeric',
            'notes' => 'nullable|string',
            'probability' => 'required|numeric|min:0|max:100',
            'expected_close_date' => 'required|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $opportunity = new LeadOpportunity();
        $opportunity->user_id = $request->user_id;
        $opportunity->sale_id = Auth::id();
        $opportunity->name = $request->name;
        $opportunity->potential_value = $request->potential_value;
        $opportunity->status = 'open';
        $opportunity->notes = $request->notes;
        $opportunity->probability = $request->probability;
        $opportunity->expected_close_date = Carbon::parse($request->expected_close_date);
        $opportunity->save();

        return response()->json([
            'success' => true,
            'message' => 'Cơ hội kinh doanh đã được tạo thành công',
            'opportunity' => $opportunity
        ]);
    }

    // Lấy danh sách cơ hội của học viên
    public function getOpportunities(Request $request, $userId)
    {
        $opportunities = LeadOpportunity::where('user_id', $userId)
            ->with('sale:id,name,photo')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'opportunities' => $opportunities
        ]);
    }

    // Cập nhật trạng thái cơ hội
    public function updateOpportunityStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:open,won,lost,postponed',
            'lost_reason' => 'required_if:status,lost',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $opportunity = LeadOpportunity::findOrFail($id);
        $opportunity->status = $request->status;

        if ($request->status == 'lost' && $request->lost_reason) {
            $opportunity->lost_reason = $request->lost_reason;
        }

        $opportunity->save();

        // Nếu cơ hội thành công, cập nhật trạng thái lead thành closed_won
        if ($request->status == 'won') {
            CrmActivity::create([
                'user_id' => $opportunity->user_id,
                'sale_id' => Auth::id(),
                'activity_type' => 'note',
                'description' => "Cơ hội \"$opportunity->name\" đã chốt thành công với giá trị " . number_format($opportunity->potential_value, 0, ',', '.') . " VNĐ",
                'result' => 'success',
                'lead_status' => 'closed_won'
            ]);
        }

        // Nếu cơ hội thất bại, cập nhật trạng thái lead thành closed_lost
        if ($request->status == 'lost') {
            CrmActivity::create([
                'user_id' => $opportunity->user_id,
                'sale_id' => Auth::id(),
                'activity_type' => 'note',
                'description' => "Cơ hội \"$opportunity->name\" đã thất bại. Lý do: $opportunity->lost_reason",
                'result' => 'failed',
                'lead_status' => 'closed_lost'
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Trạng thái cơ hội đã được cập nhật',
            'opportunity' => $opportunity
        ]);
    }

    // Báo cáo hiệu suất bán hàng
    public function salesPerformance()
    {

        $salespeople = User::where('role', 'admin')
            ->withCount(['salesActivities', 'salesOpportunities'])
            ->withSum(['salesOpportunities as won_value' => function ($query) {
                $query->where('status', 'won');
            }], 'potential_value')
            ->withCount(['salesOpportunities as won_count' => function ($query) {
                $query->where('status', 'won');
            }])
            ->withCount(['salesOpportunities as total_count'])
            ->get()
            ->map(function ($user) {

                $user->conversion_rate = $user->total_count > 0 ? round(($user->won_count / $user->total_count) * 100, 2) : 0;
                $user->formatted_won_value = number_format($user->won_value, 0, ',', '.') . ' VNĐ';
                return $user;
            });

        return view('admin.crm.performance', ['salespeople' => $salespeople]);
    }

    // Báo cáo phễu bán hàng
    public function salesFunnel()
    {
        $leadStatusCounts = User::where('role', 'student')
            ->leftJoin('crm_activities', function ($join) {
                $join->on('users.id', '=', 'crm_activities.user_id')
                    ->whereIn('crm_activities.id', function ($query) {
                        $query->selectRaw('MAX(id)')->from('crm_activities')->groupBy('user_id');
                    });
            })
            ->selectRaw('IFNULL(crm_activities.lead_status, "new") as status, COUNT(*) as count')
            ->groupBy('status')
            ->get()
            ->mapWithKeys(function ($item) {
                $statusLabels = CrmActivity::getLeadStatusOptions();
                $label = $statusLabels[$item->status] ?? $item->status;
                return [$label => $item->count];
            })
            ->toArray();

        $opportunityStatusCounts = LeadOpportunity::selectRaw('status, COUNT(*) as count, SUM(potential_value) as value')
            ->groupBy('status')
            ->get()
            ->mapWithKeys(function ($item) {
                $statusLabels = LeadOpportunity::getStatusOptions();
                $label = $statusLabels[$item->status] ?? $item->status;
                return [
                    $label => [
                        'count' => $item->count,
                        'value' => number_format($item->value, 0, ',', '.') . ' VNĐ'
                    ]
                ];
            })
            ->toArray();

        return view('admin.crm.funnel', [
            'leadStatusCounts' => $leadStatusCounts,
            'opportunityStatusCounts' => $opportunityStatusCounts
        ]);
    }
}

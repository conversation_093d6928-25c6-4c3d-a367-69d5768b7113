<?php

namespace App\Http\Controllers\student;

use App\Http\Controllers\Controller;
use App\Models\FileUploader;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class MyProfileController extends Controller
{
    public function index()
    {
        $page_data['user_details'] = User::find(auth()->user()->id);
        $view_path                 = 'frontend.' . get_frontend_settings('theme') . '.student.my_profile.index';
        return view($view_path, $page_data);
    }

    public function update(Request $request, $user_id)
    {
        // Security check: Ensure the user can only update their own profile
        if (auth()->user()->id != $user_id) {
            Session::flash('error', 'Hành động không được phép.');
            return redirect()->back();
        }

        // Enhanced validation rules to prevent SQL injection and XSS attacks
        $rules = [
            'name'                 => ['required', 'string', 'max:255', 'regex:/^[^<>]*$/'],
            'email'                => ['required', 'email', Rule::unique('users')->ignore($user_id)],
            'phone'                => ['nullable', 'string', 'max:20', 'regex:/^((\+|)84|0)[3|5|7|8|9]+([0-9]{8})$/'],
            'website'              => ['nullable', 'url', 'max:255', 'regex:/^[^<>]*$/'],
            'facebook'             => ['nullable', 'string', 'max:255', 'regex:/^[^<>]*$/'],
            'twitter'              => ['nullable', 'string', 'max:255', 'regex:/^[^<>]*$/'],
            'linkedin'             => ['nullable', 'string', 'max:255', 'regex:/^[^<>]*$/'],
            'skills'               => ['nullable', 'string', 'regex:/^[^<>]*$/'],
            'biography'            => ['nullable', 'string', 'regex:/^[^<>]*$/'],
            'bank_name'            => ['nullable', 'string', 'max:255', 'regex:/^[^<>]*$/'],
            'bank_account_number'  => ['nullable', 'string', 'max:50', 'regex:/^[^<>]*$/'],
            'bank_account_name'    => ['nullable', 'string', 'max:255', 'regex:/^[^<>]*$/'],
            'old_password'         => ['nullable', 'string'],
            'new_password'         => ['nullable', 'string', 'min:6', 'required_with:old_password'],
        ];
        
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Only collect validated data to ensure no malicious inputs
        $validated = $validator->validated();
        
        // Extra sanitization for XSS protection
        foreach ($validated as $key => $value) {
            if (is_string($value)) {
                $validated[$key] = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            }
        }

        // Handle password change if old password is provided
        if (!empty($validated['old_password'])) {
            // Check if old password is correct
            if (!auth()->attempt(['email' => auth()->user()->email, 'password' => $validated['old_password']])) {
                Session::flash('error', 'Mật khẩu hiện tại không chính xác.');
                return redirect()->back();
            }

            // Update password
            $validated['password'] = bcrypt($validated['new_password']);
        }

        // Remove password fields from validated data before update
        unset($validated['old_password']);
        unset($validated['new_password']);
        
        User::where('id', $user_id)->update($validated);
        Session::flash('success', 'Cập nhật thông tin thành công.');
        return redirect()->back();
    }

    public function update_profile_picture(Request $request)
    {
        $request->validate([
            'photo' => 'required|image|mimes:jpeg,png,jpg,webp,tiff|max:3072',
        ]);

        // process file
        $file      = $request->photo;
        $file_name = Str::random(20) . '.' . $file->extension();
        $path      = 'uploads/users/' . auth()->user()->role . '/' . $file_name;
        FileUploader::upload($file, $path, null, null, 300);

        User::where('id', auth()->user()->id)->update(['photo' => $path]);
        Session::flash('success', get_phrase('Profile picture updated.'));
        return redirect()->back();
    }
}

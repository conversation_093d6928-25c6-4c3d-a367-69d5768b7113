<?php

namespace App\Http\Controllers\student;

use App\Http\Controllers\Controller;
use App\Models\Coupon;
use App\Models\Course;
use App\Models\Enrollment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ActivationController extends Controller
{
    public function index()
    {
        return view('frontend.default.student.activate_course');
    }

    public function activate(Request $request)
    {
        try {
            // Validate input
            $request->validate([
                'activation_code' => 'required|string|max:20'
            ]);

            $activationCode = strtoupper(trim($request->activation_code));
            $userId = Auth::id();

            // Tìm coupon với mã kích hoạt (không kiểm tra discount trước)
            $coupon = Coupon::where('code', $activationCode)
                ->where('expiry', '>=', time()) // Chưa hết hạn
                ->whereIn('status', [1, 2]) // Trạng thái hoạt động
                ->where(function($query) {
                    $query->where('quantity', '>', 0)
                          ->orWhereNull('quantity'); // Cho phép quantity null (không giới hạn)
                })
                ->first();

            if (!$coupon) {
                return response()->json([
                    'success' => false,
                    'message' => 'Mã kích hoạt không hợp lệ hoặc đã hết hạn'
                ]);
            }

            // Kiểm tra riêng discount phải là 100%
            if ($coupon->discount != 100) {
                return response()->json([
                    'success' => false,
                    'message' => 'Mã kích hoạt khóa học không hợp lệ'
                ]);
            }

            // Kiểm tra khóa học tồn tại
            if (!$coupon->course_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Mã kích hoạt không được liên kết với khóa học nào'
                ]);
            }

            $course = Course::find($coupon->course_id);
            if (!$course) {
                return response()->json([
                    'success' => false,
                    'message' => 'Khóa học không tồn tại'
                ]);
            }

            // Kiểm tra người dùng đã đăng ký khóa học chưa
            $existingEnrollment = Enrollment::where('user_id', $userId)
                ->where('course_id', $course->id)
                ->exists();

            if ($existingEnrollment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bạn đã đăng ký khóa học này rồi'
                ]);
            }

            // Bắt đầu transaction
            DB::beginTransaction();

            try {
                // Tạo enrollment mới
                $enrollmentData = [
                    'course_id' => $course->id,
                    'user_id' => $userId,
                    'enrollment_type' => 'paid',
                    'entry_date' => time(),
                    'created_at' => now(),
                    'updated_at' => now()
                ];

                // Xử lý thời gian hết hạn khóa học
                if ($course->expiry_period > 0) {
                    $days = $course->expiry_period * 30;
                    $enrollmentData['expiry_date'] = strtotime("+" . $days . " days");
                } else {
                    $enrollmentData['expiry_date'] = null;
                }

                // Tạo enrollment
                Enrollment::create($enrollmentData);

                // Giảm số lượng coupon (chỉ khi quantity không null)
                if ($coupon->quantity !== null) {
                    $coupon->decrement('quantity');
                }

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'Đã kích hoạt thành công khóa học "' . $course->title . '"! Bạn có thể bắt đầu học ngay.',
                    'course' => [
                        'title' => $course->title,
                        'url' => route('my.courses')
                    ]
                ]);

            } catch (\Exception $e) {
                DB::rollback();
                return response()->json([
                    'success' => false,
                    'message' => 'Có lỗi xảy ra khi kích hoạt khóa học. Vui lòng thử lại.'
                ]);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Vui lòng nhập mã kích hoạt hợp lệ'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra. Vui lòng thử lại sau.'
            ]);
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Lesson;
use App\Models\OfflinePayment;
use App\Models\Payment_history;
use App\Models\User;
use App\Models\Withdraw;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DashboardController extends Controller
{
    public function index()
    {
        // Lấy số liệu thống kê tổng quan
        $statistics = $this->getStatistics();
        
        // Lấy doanh thu theo tháng
        $monthly_amount = $this->getMonthlyRevenue();

        // L<PERSON>y danh sách rút tiền đang chờ
        $pending_withdraws = $this->getPendingWithdraws();
        
        // L<PERSON>y thông tin khóa học theo trạng thái
        $course_statistics = $this->getCourseStatistics();
        
        // Lấy top affiliate theo doanh thu và số đơn hàng
        $top_affiliates = [];
        if (function_exists('addon_check') && addon_check('my.affiliate')) {
            $top_affiliates = $this->getTopAffiliates();
        }
        
        $page_data = [
            'statistics' => $statistics,
            'monthly_amount' => $monthly_amount,
            'pending_withdraws' => $pending_withdraws,
            'course_statistics' => $course_statistics,
            'top_affiliates_amount' => $top_affiliates['by_amount'] ?? collect(),
            'top_affiliates_orders' => $top_affiliates['by_orders'] ?? collect(),
        ];
        
        return view('admin.dashboard.index', $page_data);
    }
    

    private function getStatistics(): array
    {
     
        return Cache::remember('dashboard_statistics', 3600, function () {
            return [
                'course_count' => Course::count(),
                'lesson_count' => Lesson::count(),
                'enrollment_count' => Enrollment::count(),
                'student_count' => User::where('role', 'student')->count(),
                'instructor_count' => User::where('role', 'instructor')->count(),
            ];
        });
    }
    

    private function getMonthlyRevenue(): array
    {
        // Lấy năm hiện tại
        $current_year = date('Y');
        
        // Tạo mảng chứa các tháng trong năm để query một lần
        $months = [];
        for ($i = 1; $i <= 12; $i++) {
            $month = str_pad($i, 2, '0', STR_PAD_LEFT);
            $start_date = "$current_year-$month-01 00:00:00";
            $end_date = date('Y-m-t 23:59:59', strtotime($start_date));
            
            $months[$i] = [
                'start' => $start_date,
                'end' => $end_date
            ];
        }
        
        // Mảng kết quả với cấu trúc mới
        $result = [
            'labels' => ["", "Tháng 1", "Tháng 2", "Tháng 3", "Tháng 4", "Tháng 5", "Tháng 6", 
                         "Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12"],
            'total_revenue' => [0], // Bắt đầu với index 0 trống
            'affiliate_amount' => [0], // Bắt đầu với index 0 trống
            'actual_revenue' => [0]  // Doanh thu thực tế = total_revenue - affiliate_amount
        ];
        
        // Query tổng doanh thu theo từng tháng
        foreach ($months as $month_num => $period) {
            // Query các giao dịch trong tháng
            $transactions = OfflinePayment::whereBetween('created_at', [$period['start'], $period['end']])
                ->where('status', 1)
                ->select('total_amount', 'affiliate_amount')
                ->get();
                
            // Tính tổng doanh thu và hoa hồng affiliate
            $total_revenue = 0;
            $total_affiliate = 0;
            
            foreach ($transactions as $transaction) {
                $total_revenue += (float) $transaction->total_amount;
                $total_affiliate += (float) ($transaction->affiliate_amount ?: 0);
            }
            
            // Tính doanh thu thực (sau khi trừ hoa hồng)
            $actual_revenue = $total_revenue - $total_affiliate;
            
            // Lưu vào mảng kết quả
            $result['total_revenue'][$month_num] = $total_revenue;
            $result['affiliate_amount'][$month_num] = $total_affiliate;
            $result['actual_revenue'][$month_num] = $actual_revenue;
        }
        
        return $result;
    }
    

    private function getPendingWithdraws()
    {
        return Withdraw::where('status', 0)
            ->with(['user:id,name,email,photo'])
            ->orderBy('id', 'DESC')
            ->paginate(10);
    }
    
  
    private function getCourseStatistics(): array
    {
 
        return Cache::remember('course_statistics', 3600, function () {
            $courses = Course::select('status')
                ->get()
                ->groupBy('status');
                
            return [
                'active' => $courses->get('active')?->count() ?? 0,
                'upcoming' => $courses->get('upcoming')?->count() ?? 0,
                'pending' => $courses->get('pending')?->count() ?? 0,
                'private' => $courses->get('private')?->count() ?? 0,
                'draft' => $courses->get('draft')?->count() ?? 0,
                'inactive' => $courses->get('inactive')?->count() ?? 0,
            ];
        });
    }
    
 
    private function getTopAffiliates(): array
    {

        $by_amount = OfflinePayment::select('affiliate_id')
            ->selectRaw('SUM(affiliate_amount) as total_amount, COUNT(id) as total_orders')
            ->where('status', 1)
            ->whereNotNull('affiliate_id')
            ->groupBy('affiliate_id')
            ->orderByDesc('total_amount')
            ->limit(10)
            ->get();
            
        // Lấy top 10 affiliate theo số đơn hàng
        $by_orders = OfflinePayment::select('affiliate_id')
            ->selectRaw('COUNT(id) as total_orders, SUM(affiliate_amount) as total_amount')
            ->where('status', 1)
            ->whereNotNull('affiliate_id')
            ->groupBy('affiliate_id')
            ->orderByDesc('total_orders')
            ->limit(10)
            ->get();
            
        // Lấy thông tin người dùng cho các affiliate
        $affiliate_ids = $by_amount->pluck('affiliate_id')
            ->merge($by_orders->pluck('affiliate_id'))
            ->unique()
            ->values()
            ->toArray();
            
        $users = User::whereIn('id', $affiliate_ids)
            ->select('id', 'name')
            ->get()
            ->keyBy('id');
            

        $by_amount->each(function ($item) use ($users) {
            $item->user = $users->get($item->affiliate_id);
        });
        
        $by_orders->each(function ($item) use ($users) {
            $item->user = $users->get($item->affiliate_id);
        });
        
        return [
            'by_amount' => $by_amount,
            'by_orders' => $by_orders
        ];
    }

    /**
     * API endpoint for real-time dashboard stats
     */
    public function getStatsApi()
    {
        try {
            $statistics = $this->getStatistics();
            $monthly_amount = $this->getMonthlyRevenue();
            $course_statistics = $this->getCourseStatistics();

            // Get recent activities (last 10)
            $recent_activities = [];

            // Get notifications
            $notifications = [];

            return response()->json([
                'success' => true,
                'data' => [
                    'statistics' => $statistics,
                    'chartData' => [
                        'monthly_amount' => $monthly_amount
                    ],
                    'course_statistics' => $course_statistics,
                    'activities' => $recent_activities,
                    'notifications' => $notifications,
                    'timestamp' => now()->toISOString()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Không thể lấy dữ liệu dashboard',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('queue:work --stop-when-empty')->everyMinute();
        // $schedule->command('inspire')->hourly();
        $schedule->command('courses:update-prices --force')
            ->dailyAt('00:01');
        $schedule->command('app:benchmark-source-analytics')
            ->hourly();
        $schedule->command('payments:delete-old-sepay')->everyFiveMinutes();

        $schedule->command('app:update-balance-affiliate')->daily();

        // Check for users who violate simultaneous login limit
        $schedule->command('users:deactivate-violating')
            ->daily()
            ->appendOutputTo(storage_path('logs/deactivate-users.log'));
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}

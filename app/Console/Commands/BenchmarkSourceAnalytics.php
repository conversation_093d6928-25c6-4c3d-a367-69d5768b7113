<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class BenchmarkSourceAnalytics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:benchmark-source-analytics {iterations=5}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Benchmarks the performance of source analytics queries';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $iterations = (int) $this->argument('iterations');
        
        if ($iterations <= 0) {
            $this->error('Iterations must be greater than 0');
            return 1;
        }
        
        // Clear cache to ensure accurate measurements
        Cache::forget('source_stats_' . date('Y-m-d'));
        
        $this->info('Benchmarking source analytics performance...');
        $this->info('Measuring execution time for ' . $iterations . ' iterations');
        
        $this->info('Running optimized query (with caching and indexing)...');
        $times = [];
        
        for ($i = 1; $i <= $iterations; $i++) {
            $this->info("Iteration {$i}...");
            
            // Clear the cache for each iteration
            Cache::forget('source_stats_' . date('Y-m-d'));
            
            $startTime = microtime(true);
            
            // Run the optimized query
            $sourceStats = Cache::remember('source_stats_' . date('Y-m-d'), now()->addHours(6), function () {
                $sourceStats = DB::table('users')
                    ->select('source', DB::raw('count(*) as total'))
                    ->where('role', 'student')
                    ->whereNotNull('source')
                    ->groupBy('source')
                    ->orderBy('total', 'desc')
                    ->limit(10)
                    ->get();
                
                $conversionData = DB::table('users')
                    ->select('users.source', DB::raw('COUNT(DISTINCT lead_opportunities.id) as converted'))
                    ->leftJoin('lead_opportunities', function ($join) {
                        $join->on('users.id', '=', 'lead_opportunities.user_id')
                            ->where('lead_opportunities.status', '=', 'won');
                    })
                    ->whereIn('users.source', $sourceStats->pluck('source'))
                    ->where('users.role', 'student')
                    ->groupBy('users.source')
                    ->get()
                    ->keyBy('source');
                
                $sourceConversion = [];
                foreach ($sourceStats as $source) {
                    $sourceName = $source->source ?: 'Không xác định';
                    $totalFromSource = $source->total;
                    $wonDealsFromSource = isset($conversionData[$source->source]) 
                        ? $conversionData[$source->source]->converted : 0;
                    
                    $conversionRate = $totalFromSource > 0 
                        ? round(($wonDealsFromSource / $totalFromSource) * 100, 2) : 0;
                    
                    $sourceConversion[] = [
                        'source' => $sourceName,
                        'total' => $totalFromSource,
                        'converted' => $wonDealsFromSource,
                        'conversion_rate' => $conversionRate
                    ];
                }
                
                usort($sourceConversion, function($a, $b) {
                    return $b['conversion_rate'] <=> $a['conversion_rate'];
                });
                
                return $sourceConversion;
            });
            
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds
            $times[] = $executionTime;
            
            $this->info("   Execution time: {$executionTime} ms");
            $this->info("   Sources found: " . count($sourceStats));
        }
        
        // Calculate statistics
        $avgTime = round(array_sum($times) / count($times), 2);
        $minTime = round(min($times), 2);
        $maxTime = round(max($times), 2);
        
        $this->info('');
        $this->info('Performance Results:');
        $this->info("Average execution time: {$avgTime} ms");
        $this->info("Minimum execution time: {$minTime} ms");
        $this->info("Maximum execution time: {$maxTime} ms");
        
        // Now run unoptimized query (similar to the original implementation)
        $this->info('');
        $this->info('Running unoptimized query (without index optimization, no batching)...');
        $uTimes = [];
        
        for ($i = 1; $i <= min(3, $iterations); $i++) {
            $this->info("Iteration {$i}...");
            
            $startTime = microtime(true);
            
            // Run the unoptimized query (similar to what was in place before)
            $sourceStats = DB::table('users')
                ->select('source', DB::raw('count(*) as total'))
                ->where('role', 'student')
                ->whereNotNull('source')
                ->groupBy('source')
                ->get();
            
            $sourceConversion = [];
            foreach ($sourceStats as $source) {
                $totalFromSource = $source->total;
                
                // Individual query for each source - this is the inefficient part
                $wonDealsFromSource = DB::table('users')
                    ->join('lead_opportunities', 'users.id', '=', 'lead_opportunities.user_id')
                    ->where('users.source', $source->source)
                    ->where('lead_opportunities.status', 'won')
                    ->count();
                
                $conversionRate = $totalFromSource > 0 ? 
                    round(($wonDealsFromSource / $totalFromSource) * 100, 2) : 0;
                
                $sourceConversion[] = [
                    'source' => $source->source ?: 'Không xác định',
                    'total' => $totalFromSource,
                    'converted' => $wonDealsFromSource,
                    'conversion_rate' => $conversionRate
                ];
            }
            
            usort($sourceConversion, function($a, $b) {
                return $b['conversion_rate'] <=> $a['conversion_rate'];
            });
            
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2); // Convert to milliseconds
            $uTimes[] = $executionTime;
            
            $this->info("   Execution time: {$executionTime} ms");
            $this->info("   Sources found: " . count($sourceConversion));
        }
        
        // Calculate statistics for unoptimized version
        $uAvgTime = round(array_sum($uTimes) / count($uTimes), 2);
        $uMinTime = round(min($uTimes), 2);
        $uMaxTime = round(max($uTimes), 2);
        
        $this->info('');
        $this->info('Unoptimized Performance Results:');
        $this->info("Average execution time: {$uAvgTime} ms");
        $this->info("Minimum execution time: {$uMinTime} ms");
        $this->info("Maximum execution time: {$uMaxTime} ms");
        
        // Compare results
        $improvement = round((($uAvgTime - $avgTime) / $uAvgTime) * 100, 2);
        $this->info('');
        $this->info("Performance improvement: {$improvement}%");
        
        return 0;
    }
}

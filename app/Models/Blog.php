<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Blog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'category_id', 'title', 'slug', 'description', 'excerpt', 'thumbnail', 'keywords', 'is_popular', 'status', 'publish_date',
    ];

    protected $casts = [
        'publish_date' => 'datetime',
    ];


    public function user() {
        return $this->belongsTo(User::class);
    }
}

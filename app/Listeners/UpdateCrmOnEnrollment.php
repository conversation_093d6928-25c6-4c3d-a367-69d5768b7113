<?php

namespace App\Listeners;

use App\Events\CourseEnrolled;
use App\Models\CrmActivity;
use App\Models\LeadOpportunity;
use Carbon\Carbon;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class UpdateCrmOnEnrollment implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(CourseEnrolled $event): void
    {
        $enrollment = $event->enrollment;
        $course = $event->course;
        $user = $event->user;
        $enrollmentValue = $event->enrollmentValue;

        // 1. Tự động tạo/cập nhật Lead Opportunity thành "won"
        $this->createOrUpdateOpportunity($user, $course, $enrollmentValue);

        // 2. Tự động tạo CRM Activity ghi nhận việc chốt đơn
        $this->createSuccessActivity($user, $course, $enrollmentValue);

        // 3. <PERSON><PERSON><PERSON> nhật trạng thái lead thành "closed_won"
        $this->updateLeadStatus($user);
    }

    private function createOrUpdateOpportunity($user, $course, $enrollmentValue)
    {
        // Tìm opportunity đang mở cho user này và course này
        $opportunity = LeadOpportunity::where('user_id', $user->id)
            ->where('status', 'open')
            ->where('name', 'like', '%' . $course->title . '%')
            ->first();

        if ($opportunity) {
            // Cập nhật opportunity hiện có
            $opportunity->update([
                'status' => 'won',
                'potential_value' => $enrollmentValue,
                'probability' => 100,
                'notes' => ($opportunity->notes ?? '') . "\n\n[AUTO] Đã chốt đơn thành công vào " . now()->format('d/m/Y H:i')
            ]);
        } else {
            // Tạo opportunity mới
            LeadOpportunity::create([
                'user_id' => $user->id,
                'sale_id' => 1, // Admin mặc định, có thể cải thiện sau
                'name' => "Khóa học: {$course->title}",
                'potential_value' => $enrollmentValue,
                'status' => 'won',
                'probability' => 100,
                'expected_close_date' => now(),
                'notes' => "[AUTO] Tự động tạo khi học viên đăng ký khóa học thành công"
            ]);
        }
    }

    private function createSuccessActivity($user, $course, $enrollmentValue)
    {
        CrmActivity::create([
            'user_id' => $user->id,
            'sale_id' => 1, // Admin mặc định
            'activity_type' => 'note',
            'description' => "🎉 CHỐT ĐÔN THÀNH CÔNG!\n\n" .
                           "Khóa học: {$course->title}\n" .
                           "Giá trị: " . number_format($enrollmentValue, 0, ',', '.') . " VNĐ\n" .
                           "Thời gian: " . now()->format('d/m/Y H:i') . "\n" .
                           "Loại đăng ký: Tự động từ hệ thống\n\n" .
                           "✅ Đã cập nhật doanh thu vào CRM",
            'result' => 'success',
            'lead_status' => 'closed_won',
            'next_follow_up' => null
        ]);
    }

    private function updateLeadStatus($user)
    {
        // Đảm bảo trạng thái lead mới nhất là "closed_won"
        $latestActivity = $user->crmActivities()->latest()->first();
        if (!$latestActivity || $latestActivity->lead_status !== 'closed_won') {
            CrmActivity::create([
                'user_id' => $user->id,
                'sale_id' => 1,
                'activity_type' => 'note',
                'description' => "[AUTO] Cập nhật trạng thái lead thành 'Chuyển đổi thành công' do có enrollment mới",
                'result' => 'success',
                'lead_status' => 'closed_won'
            ]);
        }
    }
}

<?php

namespace App\Events;

use App\Models\Enrollment;
use App\Models\Course;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CourseEnrolled
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $enrollment;
    public $course;
    public $user;
    public $enrollmentValue;

    /**
     * Create a new event instance.
     */
    public function __construct(Enrollment $enrollment, $enrollmentValue = null)
    {
        $this->enrollment = $enrollment;
        $this->course = $enrollment->course;
        $this->user = $enrollment->users;
        $this->enrollmentValue = $enrollmentValue ?? $this->course->price ?? 0;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('channel-name'),
        ];
    }
}

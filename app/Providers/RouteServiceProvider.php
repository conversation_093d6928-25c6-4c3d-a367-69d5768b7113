<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/dashboard';

    /**
     * Define your route model bindings, pattern filters, and other route configuration.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            Route::middleware(['api', 'webConfig'])
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware(['web', 'webConfig'])
                ->group(base_path('routes/auth.php'));

            Route::middleware(['web', 'webConfig'])
                ->group(base_path('routes/admin.php'));

            Route::middleware(['web', 'webConfig', 'verified'])
                ->group(base_path('routes/custom_route.php'));

            Route::middleware([ 'web','webConfig',])
                ->group(base_path('routes/payment.php'));

            Route::middleware(['web', 'webConfig', 'verified'])
                ->group(base_path('routes/instructor.php'));

            Route::middleware(['web', 'webConfig', 'verified'])
                ->group(base_path('routes/chat.php'));

            Route::middleware(['web', 'webConfig'])
                ->namespace($this->namespace)
                ->group(base_path('routes/guest.php'));

            Route::middleware(['web', 'webConfig', 'verified'])
                ->namespace($this->namespace)
                ->group(base_path('routes/student.php'));

            Route::middleware(['web', 'webConfig'])
                ->namespace($this->namespace)
                ->group(base_path('routes/player.php'));

            Route::middleware(['web'])
                ->group(base_path('routes/web.php'));
        });
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }
}

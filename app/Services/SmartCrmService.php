<?php

namespace App\Services;

use App\Models\User;
use App\Models\CrmActivity;
use App\Models\LeadOpportunity;
use App\Models\Enrollment;
use App\Models\Course;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class SmartCrmService
{
    /**
     * Tính toán doanh thu tự động từ enrollments
     */
    public function calculateAutoRevenue()
    {
        return DB::table('enrollments')
            ->join('courses', 'enrollments.course_id', '=', 'courses.id')
            ->where('enrollments.enrollment_type', 'paid')
            ->sum('courses.price');
    }

    /**
     * Lấy thống kê CRM thông minh
     */
    public function getSmartStats()
    {
        $totalLeads = User::where('role', 'student')->count();
        $totalRevenue = $this->calculateAutoRevenue();
        $wonDeals = LeadOpportunity::where('status', 'won')->count();
        $conversionRate = $totalLeads > 0 ? round(($wonDeals / $totalLeads) * 100, 2) : 0;
        $openOpportunities = LeadOpportunity::where('status', 'open')->count();

        return [
            'total_leads' => $totalLeads,
            'total_revenue' => $totalRevenue,
            'won_deals' => $wonDeals,
            'conversion_rate' => $conversionRate,
            'opportunities' => $openOpportunities,
            'avg_deal_value' => $wonDeals > 0 ? round($totalRevenue / $wonDeals, 0) : 0
        ];
    }

    /**
     * Lấy dữ liệu pipeline bán hàng
     */
    public function getPipelineData()
    {
        $pipeline = [];
        $statuses = ['new', 'contacted', 'nurturing', 'qualified', 'proposal', 'closed_won', 'closed_lost'];

        foreach ($statuses as $status) {
            $count = User::whereHas('crmActivities', function($q) use ($status) {
                $q->where('lead_status', $status)
                  ->whereIn('id', function($sub) {
                      $sub->selectRaw('MAX(id)')
                          ->from('crm_activities')
                          ->groupBy('user_id');
                  });
            })->count();

            $pipeline[$status] = $count;
        }

        return $pipeline;
    }

    /**
     * Gợi ý AI cho CRM
     */
    public function getAiSuggestions()
    {
        $suggestions = [];

        // 1. Khách hàng cần follow-up hôm nay
        $todayFollowUps = CrmActivity::whereDate('next_follow_up', today())
            ->whereNotNull('next_follow_up')
            ->count();

        if ($todayFollowUps > 0) {
            $suggestions[] = [
                'type' => 'follow_up',
                'icon' => 'fi-rr-phone-call',
                'color' => 'primary',
                'message' => "Có {$todayFollowUps} khách hàng cần follow-up hôm nay",
                'action' => 'view_follow_ups'
            ];
        }

        // 2. Cơ hội sắp hết hạn
        $expiringSoon = LeadOpportunity::where('status', 'open')
            ->whereBetween('expected_close_date', [today(), today()->addDays(3)])
            ->count();

        if ($expiringSoon > 0) {
            $suggestions[] = [
                'type' => 'expiring_opportunities',
                'icon' => 'fi-rr-time-fast',
                'color' => 'warning',
                'message' => "{$expiringSoon} cơ hội sắp hết hạn, cần chốt gấp",
                'action' => 'view_expiring_opportunities'
            ];
        }

        // 3. Khách hàng tiềm năng cao (chưa có activity gần đây)
        $highPotential = User::where('role', 'student')
            ->whereDoesntHave('crmActivities', function($q) {
                $q->where('created_at', '>=', now()->subDays(7));
            })
            ->whereHas('crmActivities', function($q) {
                $q->where('lead_status', 'qualified');
            })
            ->count();

        if ($highPotential > 0) {
            $suggestions[] = [
                'type' => 'high_potential',
                'icon' => 'fi-rr-star',
                'color' => 'success',
                'message' => "{$highPotential} khách hàng tiềm năng cao cần chăm sóc",
                'action' => 'view_high_potential'
            ];
        }

        return $suggestions;
    }

    /**
     * Tự động tạo opportunity từ lead qualified
     */
    public function autoCreateOpportunityFromQualifiedLead($userId, $courseId = null)
    {
        $user = User::find($userId);
        if (!$user) return false;

        // Kiểm tra xem đã có opportunity chưa
        $existingOpportunity = LeadOpportunity::where('user_id', $userId)
            ->where('status', 'open')
            ->first();

        if ($existingOpportunity) {
            return $existingOpportunity;
        }

        // Tạo opportunity mới
        $course = $courseId ? Course::find($courseId) : Course::orderBy('price', 'desc')->first();
        $potentialValue = $course ? $course->price : 1000000; // Default 1M VNĐ

        $opportunity = LeadOpportunity::create([
            'user_id' => $userId,
            'sale_id' => 1, // Admin mặc định
            'name' => $course ? "Khóa học: {$course->title}" : "Cơ hội bán hàng cho {$user->name}",
            'potential_value' => $potentialValue,
            'status' => 'open',
            'probability' => 60, // 60% mặc định cho qualified lead
            'expected_close_date' => now()->addDays(14),
            'notes' => "[AUTO] Tự động tạo từ qualified lead"
        ]);

        // Tạo activity ghi nhận việc tạo opportunity
        CrmActivity::create([
            'user_id' => $userId,
            'sale_id' => 1,
            'activity_type' => 'note',
            'description' => "🎯 Tự động tạo cơ hội kinh doanh\n\nGiá trị dự kiến: " . number_format($potentialValue, 0, ',', '.') . " VNĐ\nXác suất: 60%\nDự kiến chốt: " . now()->addDays(14)->format('d/m/Y'),
            'result' => 'success',
            'lead_status' => 'proposal'
        ]);

        return $opportunity;
    }

    /**
     * Phân tích xu hướng doanh thu theo thời gian
     */
    public function getRevenueAnalytics($days = 30)
    {
        $analytics = [];
        
        for ($i = $days; $i >= 0; $i--) {
            $date = now()->subDays($i);
            
            $revenue = DB::table('enrollments')
                ->join('courses', 'enrollments.course_id', '=', 'courses.id')
                ->whereDate('enrollments.created_at', $date)
                ->where('enrollments.enrollment_type', 'paid')
                ->sum('courses.price');

            $analytics[] = [
                'date' => $date->format('Y-m-d'),
                'revenue' => $revenue,
                'formatted_date' => $date->format('d/m')
            ];
        }

        return $analytics;
    }

    /**
     * Tính điểm lead scoring
     */
    public function calculateLeadScore($userId)
    {
        $user = User::find($userId);
        if (!$user) return 0;

        $score = 0;

        // Điểm cơ bản
        $score += 10;

        // Có email verified
        if ($user->email_verified_at) {
            $score += 15;
        }

        // Có số điện thoại
        if ($user->phone) {
            $score += 10;
        }

        // Số lần tương tác CRM
        $activityCount = $user->crmActivities()->count();
        $score += min($activityCount * 5, 25); // Tối đa 25 điểm

        // Trạng thái lead hiện tại
        $latestStatus = $user->getLatestLeadStatus();
        switch ($latestStatus) {
            case 'contacted':
                $score += 20;
                break;
            case 'nurturing':
                $score += 30;
                break;
            case 'qualified':
                $score += 50;
                break;
            case 'proposal':
                $score += 70;
                break;
        }

        // Đã có enrollment
        if ($user->enrollments()->exists()) {
            $score += 100;
        }

        return min($score, 100); // Tối đa 100 điểm
    }

    /**
     * Gợi ý hành động tiếp theo cho lead
     */
    public function suggestNextAction($userId)
    {
        $user = User::find($userId);
        if (!$user) return null;

        $latestActivity = $user->crmActivities()->latest()->first();
        $leadStatus = $user->getLatestLeadStatus();
        $daysSinceLastContact = $latestActivity ? 
            now()->diffInDays($latestActivity->created_at) : 999;

        $suggestions = [];

        switch ($leadStatus) {
            case 'new':
                $suggestions[] = [
                    'action' => 'first_contact',
                    'priority' => 'high',
                    'message' => 'Thực hiện liên hệ đầu tiên',
                    'suggested_method' => 'call'
                ];
                break;

            case 'contacted':
                if ($daysSinceLastContact > 3) {
                    $suggestions[] = [
                        'action' => 'follow_up',
                        'priority' => 'medium',
                        'message' => 'Follow-up sau lần liên hệ đầu',
                        'suggested_method' => 'email'
                    ];
                }
                break;

            case 'nurturing':
                $suggestions[] = [
                    'action' => 'qualify_lead',
                    'priority' => 'medium',
                    'message' => 'Đánh giá và phân loại lead',
                    'suggested_method' => 'meeting'
                ];
                break;

            case 'qualified':
                $suggestions[] = [
                    'action' => 'create_proposal',
                    'priority' => 'high',
                    'message' => 'Tạo đề xuất/báo giá',
                    'suggested_method' => 'email'
                ];
                break;

            case 'proposal':
                $suggestions[] = [
                    'action' => 'close_deal',
                    'priority' => 'urgent',
                    'message' => 'Chốt đơn hàng',
                    'suggested_method' => 'call'
                ];
                break;
        }

        return $suggestions;
    }
}

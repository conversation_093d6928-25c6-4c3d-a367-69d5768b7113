# Nhúng Form Đăng Ký MSTs Academy

Tài liệu này hướng dẫn cách nhúng form đăng ký MSTs Academy vào website bên ngoài.

## Cách nhúng form

Đ<PERSON> nhúng form đăng ký MSTs Academy vào website của bạn, sử dụng mã HTML iframe sau:

```html
<iframe 
  src="https://yourdomain.com/embed/register/your-course-slug?redirect_url=https://your-website.com/thank-you-page" 
  width="100%" 
  height="700" 
  style="border:none;">
</iframe>
```

### Tham số

- `your-course-slug`: Slug của khóa học bạn muốn người dùng đăng ký
- `redirect_url`: URL của trang bạn muốn chuyển hướng sau khi đăng ký thành công

## Xử lý sau khi đăng ký thành công

Có hai cách để xử lý sau khi người dùng đăng ký thành công:

### 1. Chuyển hướng tự động

Nếu bạn chỉ định tham số `redirect_url`, người dùng sẽ tự động được chuyển hướng đến URL đó sau khi đăng ký thành công.

### 2. Lắng nghe sự kiện thông qua JavaScript

Bạn có thể lắng nghe sự kiện khi người dùng đăng ký thành công để thực hiện các hành động tùy chỉnh trên trang của bạn:

```javascript
window.addEventListener('message', function(event) {
  // Kiểm tra nguồn tin nhắn để đảm bảo an toàn
  if (event.origin !== 'https://yourdomain.com') return;
  
  // Xử lý thông điệp
  if (event.data && event.data.type === 'registration_success') {
    // Người dùng đã đăng ký thành công
    console.log('Đăng ký thành công! Chuyển hướng đến: ' + event.data.redirectUrl);
    
    // Có thể chuyển hướng người dùng
    window.location.href = event.data.redirectUrl;
    
    // Hoặc thực hiện các hành động khác trên trang của bạn
    // document.getElementById('successMessage').classList.remove('hidden');
  }
});
```

## Demo

Bạn có thể xem demo nhúng form tại trang `registration-embed.html` trong thư mục này.

## Bảo mật

Khi triển khai trong môi trường sản xuất, hãy đảm bảo:

1. Kiểm tra nguồn gốc của thông điệp từ iframe
2. Sử dụng HTTPS cho cả website chính và website nhúng
3. Xem xét cấu hình CORS phù hợp nếu cần

## Hỗ trợ

Nếu bạn cần hỗ trợ, vui lòng liên hệ với đội ngũ hỗ trợ kỹ thuật của MSTs Academy. 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Viewer</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        .pdf-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .toolbar {
            background-color: #333;
            color: white;
            padding: 8px 5px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex-wrap: wrap;
            min-height: 50px;
        }
        
        .toolbar-left, .toolbar-right {
            display: flex;
            align-items: center;
            gap: 5px;
            flex-wrap: wrap;
        }
        
        .btn {
            background-color: #555;
            color: white;
            border: none;
            padding: 10px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            white-space: nowrap;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .btn:hover {
            background-color: #666;
        }
        
        .btn:disabled {
            background-color: #888;
            cursor: not-allowed;
        }
        
        .page-info {
            color: white;
            font-size: 13px;
            white-space: nowrap;
            padding: 0 5px;
        }
        
        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 3px;
            flex-wrap: wrap;
        }
        
        .zoom-level {
            color: white;
            font-size: 13px;
            min-width: 45px;
            text-align: center;
            padding: 0 3px;
        }
        
        .viewer-container {
            flex: 1;
            overflow: auto;
            background-color: #e0e0e0;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 10px;
        }
        
        .page-container {
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            background-color: white;
            max-width: 100%;
        }
        
        .page-canvas {
            display: block;
            max-width: 100%;
            height: auto;
        }
        
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            font-size: 16px;
            color: #666;
            padding: 20px;
            text-align: center;
        }
        
        .mobile-hint {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
            z-index: 1000;
            display: none;
        }
        
        .mobile-hint.show {
            opacity: 1;
        }
        
        .fullscreen-btn {
            background-color: #007bff;
        }
        
        .fullscreen-btn:hover {
            background-color: #0056b3;
        }
        
        /* Mobile Responsive Design */
        @media (max-width: 768px) {
            .toolbar {
                padding: 8px 10px;
                gap: 10px;
                min-height: auto;
                justify-content: center;
            }
            
            /* Hide navigation buttons on mobile */
            .toolbar-left {
                display: none;
            }
            
            /* Hide zoom controls on mobile */
            .zoom-controls {
                display: none;
            }
            
            .toolbar-right {
                gap: 10px;
                flex: none;
                justify-content: center;
            }
            
            .page-info {
                font-size: 14px;
                order: 1;
                padding: 8px 15px;
                background-color: #444;
                border-radius: 6px;
                margin: 0 10px;
            }
            
            .fullscreen-btn {
                order: 2;
                padding: 10px 15px;
                font-size: 14px;
                min-height: 40px;
            }
            
            .viewer-container {
                padding: 0;
            }
            
            /* Simplify toolbar layout */
            .toolbar {
                flex-direction: row;
                align-items: center;
            }
        }
        
        @media (max-width: 480px) {
            .toolbar {
                padding: 5px;
            }
            
            .page-info {
                font-size: 12px;
                padding: 6px 12px;
                margin: 0 5px;
            }
            
            .fullscreen-btn {
                padding: 8px 12px;
                font-size: 12px;
                min-height: 36px;
            }
        }
        
        /* Fullscreen styles */
        .pdf-container.fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 9999;
            background-color: #333;
        }
        
        .pdf-container.fullscreen .viewer-container {
            background-color: #333;
        }
        
        /* Touch improvements */
        @media (hover: none) and (pointer: coarse) {
            .btn {
                min-height: 44px;
                min-width: 44px;
                padding: 10px;
            }
            
            .btn:hover {
                background-color: #555;
            }
            
            .btn:active {
                background-color: #777;
            }
        }
    </style>
</head>
<body>
    <div class="pdf-container" id="pdfContainer">
        <div class="toolbar">
            <div class="toolbar-left">
                <button class="btn" id="prevPage" disabled>‹ Trang trước</button>
                <span class="page-info">
                    Trang <span id="currentPage">1</span> / <span id="totalPages">-</span>
                </span>
                <button class="btn" id="nextPage" disabled>Trang sau ›</button>
            </div>
            
            <div class="toolbar-right">
                <div class="zoom-controls">
                    <button class="btn" id="zoomOut">-</button>
                    <span class="zoom-level" id="zoomLevel">100%</span>
                    <button class="btn" id="zoomIn">+</button>
                    <button class="btn" id="fitWidth">Vừa màn hình</button>
                </div>
                <button class="btn fullscreen-btn" id="fullscreenBtn">⛶ Toàn màn hình</button>
            </div>
        </div>
        
        <div class="viewer-container" id="viewerContainer">
            <div class="loading" id="loading">Đang tải PDF...</div>
            <div class="mobile-hint" id="mobileHint">
                Vuốt lên/xuống để chuyển trang
            </div>
        </div>
    </div>

    <script>
        let pdfDoc = null;
        let currentPage = 1;
        let currentZoom = 1.0;
        let isFullscreen = false;
        
        const loading = document.getElementById('loading');
        const viewerContainer = document.getElementById('viewerContainer');
        const currentPageSpan = document.getElementById('currentPage');
        const totalPagesSpan = document.getElementById('totalPages');
        const zoomLevelSpan = document.getElementById('zoomLevel');
        const prevPageBtn = document.getElementById('prevPage');
        const nextPageBtn = document.getElementById('nextPage');
        const zoomInBtn = document.getElementById('zoomIn');
        const zoomOutBtn = document.getElementById('zoomOut');
        const fitWidthBtn = document.getElementById('fitWidth');
        const fullscreenBtn = document.getElementById('fullscreenBtn');
        const pdfContainer = document.getElementById('pdfContainer');
        
        // Get PDF URL from query parameter
        const urlParams = new URLSearchParams(window.location.search);
        const pdfUrl = urlParams.get('file');
        
        if (!pdfUrl) {
            loading.textContent = 'Không tìm thấy file PDF';
            throw new Error('No PDF URL provided');
        }
        
        // Load PDF
        pdfjsLib.getDocument(pdfUrl).promise.then(function(pdf) {
            pdfDoc = pdf;
            totalPagesSpan.textContent = pdf.numPages;
            loading.style.display = 'none';
            
            // Auto fit width when PDF loads instead of default 100% zoom
            setTimeout(() => {
                renderPage(1);
                updateNavigation();
                // Auto fit to width after initial render
                setTimeout(() => {
                    fitWidthBtn.click();
                    
                    // Show mobile hint on mobile devices
                    if (window.innerWidth <= 768) {
                        showMobileHint();
                    }
                }, 100);
            }, 50);
        }).catch(function(error) {
            console.error('Error loading PDF:', error);
            loading.textContent = 'Lỗi khi tải PDF';
        });
        
        function renderPage(pageNumber) {
            pdfDoc.getPage(pageNumber).then(function(page) {
                const viewport = page.getViewport({ scale: currentZoom });
                
                // Clear previous content
                viewerContainer.innerHTML = '';
                
                // Create canvas
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                canvas.className = 'page-canvas';
                
                // Create container for page
                const pageContainer = document.createElement('div');
                pageContainer.className = 'page-container';
                pageContainer.appendChild(canvas);
                viewerContainer.appendChild(pageContainer);
                
                // Render page
                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };
                
                page.render(renderContext);
                
                currentPage = pageNumber;
                currentPageSpan.textContent = pageNumber;
                updateNavigation();
            });
        }
        
        function updateNavigation() {
            prevPageBtn.disabled = currentPage <= 1;
            nextPageBtn.disabled = currentPage >= pdfDoc.numPages;
        }
        
        function updateZoom() {
            zoomLevelSpan.textContent = Math.round(currentZoom * 100) + '%';
            renderPage(currentPage);
        }
        
        // Event listeners
        prevPageBtn.addEventListener('click', function() {
            if (currentPage > 1) {
                renderPage(currentPage - 1);
            }
        });
        
        nextPageBtn.addEventListener('click', function() {
            if (currentPage < pdfDoc.numPages) {
                renderPage(currentPage + 1);
            }
        });
        
        zoomInBtn.addEventListener('click', function() {
            currentZoom = Math.min(currentZoom * 1.2, 3.0);
            updateZoom();
        });
        
        zoomOutBtn.addEventListener('click', function() {
            currentZoom = Math.max(currentZoom / 1.2, 0.5);
            updateZoom();
        });
        
        fitWidthBtn.addEventListener('click', function() {
            const containerWidth = viewerContainer.clientWidth - 40; // 20px padding on each side
            pdfDoc.getPage(currentPage).then(function(page) {
                const viewport = page.getViewport({ scale: 1 });
                currentZoom = containerWidth / viewport.width;
                updateZoom();
            });
        });
        
        fullscreenBtn.addEventListener('click', function() {
            if (!isFullscreen) {
                if (pdfContainer.requestFullscreen) {
                    pdfContainer.requestFullscreen();
                } else if (pdfContainer.webkitRequestFullscreen) {
                    pdfContainer.webkitRequestFullscreen();
                } else if (pdfContainer.msRequestFullscreen) {
                    pdfContainer.msRequestFullscreen();
                }
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            }
        });
        
        // Handle fullscreen change
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('msfullscreenchange', handleFullscreenChange);
        
        function handleFullscreenChange() {
            isFullscreen = !!(document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement);
            
            if (isFullscreen) {
                pdfContainer.classList.add('fullscreen');
                fullscreenBtn.textContent = '✕ Thoát toàn màn hình';
            } else {
                pdfContainer.classList.remove('fullscreen');
                fullscreenBtn.textContent = '⛶ Toàn màn hình';
            }
            
            // Re-fit width when entering/exiting fullscreen
            setTimeout(() => {
                fitWidthBtn.click();
            }, 100);
        }
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowLeft':
                case 'PageUp':
                    e.preventDefault();
                    prevPageBtn.click();
                    break;
                case 'ArrowRight':
                case 'PageDown':
                    e.preventDefault();
                    nextPageBtn.click();
                    break;
                case 'F11':
                    e.preventDefault();
                    fullscreenBtn.click();
                    break;
                case '+':
                case '=':
                    e.preventDefault();
                    zoomInBtn.click();
                    break;
                case '-':
                    e.preventDefault();
                    zoomOutBtn.click();
                    break;
                case '0':
                    e.preventDefault();
                    fitWidthBtn.click();
                    break;
            }
        });
        
        // Prevent right-click context menu to disable download
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
        
        // Prevent drag and drop
        document.addEventListener('dragstart', function(e) {
            e.preventDefault();
        });
        
        // Mobile touch improvements
        let touchStartY = 0;
        let touchEndY = 0;
        
        viewerContainer.addEventListener('touchstart', function(e) {
            touchStartY = e.changedTouches[0].screenY;
        }, { passive: true });
        
        viewerContainer.addEventListener('touchend', function(e) {
            touchEndY = e.changedTouches[0].screenY;
            handleSwipe();
        }, { passive: true });
        
        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartY - touchEndY;
            
            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // Swipe up - next page
                    if (pdfDoc && currentPage < pdfDoc.numPages) {
                        nextPageBtn.click();
                    }
                } else {
                    // Swipe down - previous page
                    if (pdfDoc && currentPage > 1) {
                        prevPageBtn.click();
                    }
                }
            }
        }
        
        // Improve mobile zoom calculation
        function calculateMobileZoom() {
            if (window.innerWidth <= 768) {
                const containerWidth = viewerContainer.clientWidth - 10; // Less padding on mobile
                if (pdfDoc) {
                    pdfDoc.getPage(currentPage).then(function(page) {
                        const viewport = page.getViewport({ scale: 1 });
                        currentZoom = containerWidth / viewport.width;
                        updateZoom();
                    });
                }
            }
        }
        
        // Re-calculate zoom on orientation change
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                calculateMobileZoom();
            }, 200);
        });
        
        // Re-calculate zoom on window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768) {
                setTimeout(() => {
                    calculateMobileZoom();
                }, 100);
            }
        });
        
        // Mobile hint functionality
        const mobileHint = document.getElementById('mobileHint');
        let hintShown = false;
        
        function showMobileHint() {
            if (window.innerWidth <= 768 && !hintShown) {
                mobileHint.style.display = 'block';
                setTimeout(() => {
                    mobileHint.classList.add('show');
                }, 100);
                
                setTimeout(() => {
                    hideMobileHint();
                }, 3000);
                
                hintShown = true;
            }
        }
        
        function hideMobileHint() {
            mobileHint.classList.remove('show');
            setTimeout(() => {
                mobileHint.style.display = 'none';
            }, 300);
        }
        
        // Hide hint on any user interaction
        document.addEventListener('touchstart', function() {
            if (hintShown && mobileHint.classList.contains('show')) {
                hideMobileHint();
            }
        }, { once: true });
    </script>
</body>
</html> 
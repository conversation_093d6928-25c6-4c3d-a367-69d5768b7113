/* Modern Curriculum Builder Styles */
.curriculum-builder {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.curriculum-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    margin-bottom: 24px;
}

.curriculum-header .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.curriculum-header .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Section Cards */
.curriculum-sections {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.section-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
    will-change: transform, box-shadow;
}

.section-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.section-card.sortable-ghost {
    opacity: 0.4;
    transform: rotate(3deg) scale(0.95);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.section-card.sortable-chosen {
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    transform: scale(1.02);
    border-color: #667eea;
}

.section-card.sortable-drag {
    transform: rotate(3deg) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
    z-index: 1000;
}

/* Section Header */
.section-header {
    display: flex;
    align-items: center;
    padding: 16px 20px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    cursor: pointer;
}

.section-drag-handle {
    margin-right: 12px;
    cursor: grab;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.section-drag-handle:hover {
    background: rgba(102, 126, 234, 0.1);
}

.section-drag-handle:active {
    cursor: grabbing;
}

.section-info {
    flex: 1;
}

.section-title {
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.section-number {
    color: #667eea;
    font-weight: 700;
}

.section-actions {
    display: flex;
    gap: 8px;
}

.section-toggle {
    transition: transform 0.3s ease;
}

.section-toggle.collapsed {
    transform: rotate(-90deg);
}

/* Lessons Container */
.section-lessons {
    padding: 0;
    min-height: 60px;
    background: #fafbfc;
}

.section-lessons.collapsed {
    display: none;
}

/* Lesson Items */
.lesson-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    background: white;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    will-change: transform, background-color, box-shadow;
}

.lesson-item:last-child {
    border-bottom: none;
}

.lesson-item:hover {
    background: #f8fafc;
    transform: translateX(6px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.lesson-item.sortable-ghost {
    opacity: 0.3;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    transform: scale(0.95) rotate(1deg);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.lesson-item.sortable-chosen {
    background: #eff6ff;
    box-shadow: inset 4px 0 0 #667eea, 0 4px 12px rgba(102, 126, 234, 0.2);
    transform: scale(1.02);
}

.lesson-item.sortable-drag {
    transform: rotate(2deg) scale(1.05);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.2);
    background: white;
    border-radius: 8px;
    z-index: 1000;
}

.lesson-drag-handle {
    margin-right: 12px;
    cursor: grab;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0.6;
}

.lesson-drag-handle:hover {
    background: rgba(102, 126, 234, 0.15);
    opacity: 1;
    transform: scale(1.1);
}

.lesson-drag-handle:active {
    cursor: grabbing;
    background: rgba(102, 126, 234, 0.25);
    transform: scale(0.95);
}

.lesson-item:hover .lesson-drag-handle {
    opacity: 1;
}

.lesson-icon {
    margin-right: 12px;
    font-size: 18px;
    width: 24px;
    text-align: center;
}

.lesson-content {
    flex: 1;
}

.lesson-title {
    font-weight: 500;
    color: #1f2937;
    margin: 0;
}

.lesson-meta {
    display: flex;
    align-items: center;
    gap: 8px;
}

.badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.badge-primary { background: #dbeafe; color: #1e40af; }
.badge-warning { background: #fef3c7; color: #92400e; }
.badge-info { background: #dbeafe; color: #1e40af; }
.badge-success { background: #d1fae5; color: #065f46; }
.badge-secondary { background: #f3f4f6; color: #374151; }

.lesson-actions {
    display: flex;
    gap: 6px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lesson-item:hover .lesson-actions {
    opacity: 1;
}

/* Empty States */
.empty-lessons, .empty-curriculum {
    background: white;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    margin: 16px 20px;
}

.empty-curriculum {
    margin: 0;
    border-radius: 12px;
}

/* Drag Feedback */
.sortable-drag {
    opacity: 0.8;
    transform: rotate(3deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Drop Zones */
.section-lessons.drag-over {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border: 2px dashed #667eea;
    border-radius: 12px;
    margin: 8px 16px;
    position: relative;
    animation: pulseDropZone 1.5s ease-in-out infinite;
}

.section-lessons.drag-over::before {
    content: "Thả bài học vào đây";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #667eea;
    font-weight: 500;
    font-size: 14px;
    pointer-events: none;
    opacity: 0.8;
}

@keyframes pulseDropZone {
    0%, 100% {
        border-color: #667eea;
        background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    }
    50% {
        border-color: #4f46e5;
        background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .curriculum-header {
        padding: 16px;
    }
    
    .section-header {
        padding: 12px 16px;
    }
    
    .lesson-item {
        padding: 10px 16px;
    }
    
    .section-actions, .lesson-actions {
        gap: 4px;
    }
    
    .lesson-actions .btn {
        padding: 4px 6px;
        font-size: 12px;
    }
}

/* Animation for section toggle */
@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 1000px;
    }
}

@keyframes slideUp {
    from {
        opacity: 1;
        max-height: 1000px;
    }
    to {
        opacity: 0;
        max-height: 0;
    }
}

.section-lessons.expanding {
    animation: slideDown 0.3s ease-out;
}

.section-lessons.collapsing {
    animation: slideUp 0.3s ease-out;
}

/* Loading state */
.curriculum-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #6b7280;
}

.curriculum-loading i {
    animation: spin 1s linear infinite;
    font-size: 24px;
    margin-right: 8px;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Notification styles */
.curriculum-notification {
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 12px;
    font-weight: 500;
    animation: slideInRight 0.3s ease-out;
}

.curriculum-notification.alert-success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(22, 163, 74, 0.9) 100%);
    color: white;
}

.curriculum-notification.alert-danger {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
    color: white;
}

.curriculum-notification .btn-close {
    filter: brightness(0) invert(1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced drag feedback */
.sortable-fallback {
    opacity: 0.8;
    transform: rotate(3deg) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
}

/* Smooth transitions for all interactive elements */
.section-actions .btn,
.lesson-actions .btn {
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.section-actions .btn:hover,
.lesson-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

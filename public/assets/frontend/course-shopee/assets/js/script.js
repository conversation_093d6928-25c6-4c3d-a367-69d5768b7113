document.addEventListener("DOMContentLoaded", function () {
	try {
        // Get the header element
        const header = document.querySelector(".header");
        let headerHeight = header?.offsetHeight || 0; // Get the header height
        let ticking = false; // For scroll performance

        // Function to handle scroll events
        function handleScroll() {
            const currentScrollTop = window.scrollY;

            // Add or remove sticky class based on scroll position
            if (currentScrollTop > 50) {
                // If scrolling down past threshold and header is not already sticky
                if (!header.classList.contains("sticky-active")) {
                    // Remove returning class if it exists
                    header.classList.remove("returning");
                    // Add sticky-active class
                    header.classList.add("sticky-active");
                }
            } else {
                // If at top or scrolling back up to top
                if (header.classList.contains("sticky-active")) {
                    // Add returning class for animation
                    header.classList.add("returning");

                    // Wait for animation to complete before removing sticky-active
                    setTimeout(() => {
                        header.classList.remove("sticky-active");
                        header.classList.remove("returning");
                    }, 100); // Match this with the animation duration
                }
            }

            // Keep the scrolled class for visual changes (if needed)
            if (currentScrollTop > 50) {
                header.classList.add("scrolled");
            } else {
                header.classList.remove("scrolled");
            }

            ticking = false;
        }

        // Add scroll event listener with requestAnimationFrame for performance
        window.addEventListener("scroll", function () {
            if (!ticking) {
                window.requestAnimationFrame(function () {
                    handleScroll();
                });
                ticking = true;
            }
        });

        // Initial check
        handleScroll();

        // Recalculate header height on window resize
        window.addEventListener("resize", function () {
            headerHeight = header.offsetHeight;
            handleScroll();
        });
    } catch(e) {
        console.log(e)
    }
});

$(".copy-value").on("click", function () {
	// Get the value to copy from data-value attribute
	const textToCopy = $(this).data("value");

	// Use the Clipboard API instead of execCommand
	navigator.clipboard.writeText(textToCopy).then(
		() => {
			// Show copied message
			const $msg = $('<div class="copied-msg">Copied!</div>');
			$msg.css({
				position: "absolute",
				background: "rgba(0,0,0,0.7)",
				color: "white",
				padding: "5px 10px",
				"border-radius": "3px",
				"font-size": "12px",
				bottom: "100%",
				left: "50%",
				transform: "translateX(-50%)",
				"margin-bottom": "5px",
				opacity: "0",
				transition: "opacity 0.3s",
			});

			// Add message to DOM, positioned relative to the copy button
			$(this).css("position", "relative");
			$(this).append($msg);

			// Fade in
			setTimeout(() => $msg.css("opacity", "1"), 10);

			// Fade out and remove after 2 seconds
			setTimeout(() => {
				$msg.css("opacity", "0");
				setTimeout(() => $msg.remove(), 300);
			}, 1500);

			// Visual feedback for the icon
			$(this).removeClass("bi-copy").addClass("bi-check");

			// Reset icon after 1 second
			setTimeout(() => {
				$(this).removeClass("bi-check").addClass("bi-copy");
			}, 1000);
		},
		() => {
			// Copy failed
			alert("Failed to copy text. Please try again.");
		}
	);
});

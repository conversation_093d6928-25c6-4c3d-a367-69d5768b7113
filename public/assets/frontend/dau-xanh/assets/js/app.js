jQuery(document).ready(function ($) {

    $('div#main-menu ul li a').click(function (e) {
        e.preventDefault();
        var target = $(this).attr('href');
        var targetOffset = $(target).offset().top - 100;
        $('html, body').animate({
            scrollTop: targetOffset
        });
    });


    const header = document.querySelector("#header");
    const toggleClass = "is-sticky";

    window.addEventListener("scroll", () => {
        const currentScroll = window.pageYOffset;
        if (currentScroll > 150) {
            header.classList.add(toggleClass);
        } else {
            header.classList.remove(toggleClass);
        }
    });

    //review slide
    const reviewSwiper = new Swiper('.swiper-review', {
        loop: true,
        slidesPerView: 1,
        spaceBetween: 15,
        enabled: true,
        centeredSlides: true,
        autoplay: {
            delay: 4000,
        },
        preloadImages: false,
        lazy: true,
        navigation: {
            nextEl: '.swiper-review-next',
            prevEl: '.swiper-review-prev'
        },
        breakpoints: {
            425: {
                slidesPerView: 1,
            },
            640: {
                slidesPerView: 3,
            },
            1024: {
                slidesPerView: 3.5,
            }
        }
    });

    const studentSwiper = new Swiper('.swiper-student', {
        loop: true,
        slidesPerView: 1,
        spaceBetween: 15,
        enabled: true,
        centeredSlides: true,
        autoplay: {
            delay: 4000,
        },
        preloadImages: false,
        lazy: true,
        navigation: {
            nextEl: '.swiper-review-next',
            prevEl: '.swiper-review-prev'
        },
        breakpoints: {
            425: {
                slidesPerView: 1,
            },
            640: {
                slidesPerView: 3,
            },
            1024: {
                slidesPerView: 4.5,
            }
        }
    });

    function swiperAuto($element, $between, $desktop, $tablet, $mobile, $default, $giay, $element_next, $element_prev) {
        return new Swiper($element, {
            loop: true,
            slidesPerView: $default,
            spaceBetween: $between,
            enabled: true,
            autoplay: {
                delay: $giay,
            },
            preloadImages: false,
            lazy: true,
            navigation: {
                nextEl: $element_next,
                prevEl: $element_prev
            },
            breakpoints: {
                425: {
                    slidesPerView: $mobile,
                },
                640: {
                    slidesPerView: $tablet,
                },
                1024: {
                    slidesPerView: $desktop,
                }
            }
        });
    }

    function swiperNotAuto($element, $between, $desktop, $tablet, $mobile, $default, $element_next, $element_prev) {
        return new Swiper($element, {
            loop: true,
            slidesPerView: $default,
            spaceBetween: $between,
            preloadImages: false,
            lazy: true,
            navigation: {
                nextEl: $element_next,
                prevEl: $element_prev
            },
            breakpoints: {
                425: {
                    slidesPerView: $mobile,
                },
                640: {
                    slidesPerView: $tablet,
                },
                1024: {
                    slidesPerView: $desktop,
                }
            }
        });
    }

    // basic options for all sliders
    let defaults = {
        spaceBetween: 5,
        slidesPerView: 2
    };

    initSwipers(defaults);

    function initSwipers(defaults = {}, selector = ".swiper-ux") {
        let swipers = document.querySelectorAll(selector);
        swipers.forEach((swiper) => {
            // get options
            let optionsData = swiper.dataset.swiper
                ? JSON.parse(swiper.dataset.swiper)
                : {};
            // combine defaults and custom options
            let options = {
                ...defaults,
                ...optionsData
            };

            new Swiper(swiper, options);
        });
    }

    $('.swiper-button-prev span').html('<i class="fa-sharp fa-regular fa-arrow-left"></i>');
    $('.swiper-button-next span').html('<i class="fa-sharp fa-regular fa-arrow-right"></i>');
});


function copyText() {
    const text = document.querySelector(".register-text").innerText;
    navigator.clipboard.writeText(text).then(() => {
        document.querySelector(".register-button").textContent = "Đã sao chép!";
        setTimeout(() => {
            document.querySelector(".register-button").textContent = "";
        }, 2000);
    }).catch(err => {
        alert("Không thể sao chép: " + err);
    });
}
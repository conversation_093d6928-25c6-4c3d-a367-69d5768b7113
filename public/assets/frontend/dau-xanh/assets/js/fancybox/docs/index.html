<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

  <title>fancybox3 · Documentation</title>

  <!-- jQuery -->
  <script src="https://code.jquery.com/jquery-3.3.1.min.js" integrity="sha256-FgpCb/KJQlLNfOu91ta32o/NMZxltwRo8QtmkMRdAu8="
    crossorigin="anonymous"></script>

  <!-- Bootstrap  -->
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm"
    crossorigin="anonymous">
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js" integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl"
    crossorigin="anonymous"></script>

  <style>
a,
a:hover {
    color: #ff5268;
}

h2 {
    border-bottom: 1px solid rgba(0, 0, 0, .1);
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    padding-top: 2rem;
}

h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    padding-top: 1.5rem;
}

h2 + h3 {
    margin-top: -2rem;
}

h3 a,
h3 a:hover {
    color: currentColor;
}

code {
    background: #f1ecec;
    color: #555;
    padding: 2px 5px;
}

pre {
    background: #f1ecec;
    color: #555;
    max-height: 70vh;
    overflow: auto;
    padding: 1rem 1.5rem;
    -moz-tab-size: 4;
         tab-size: 4;
}

pre code {
    padding: 0;
}

section {
    margin-bottom: 1rem;
    margin-bottom: 5vh;
}

#introduction {
    background: #fbf9f9;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    padding-top: 1rem;
}

.navbar-brand {
    color: #444;
    font-size: 1.375rem;
}

.navbar-brand svg {
    position: relative;
    top: -1px;
    vertical-align: middle;
}

.navbar-brand svg path {
    fill: #fff;
    stroke: #444;
    stroke-width: 2.5;
}

.nav-link {
    color: grey;
    height: 2.5rem;
}

.navbar-brand:hover,
.nav-link:hover {
    color: #464646;
}

.nav-link.active {
    background: #ff5268;
    border-radius: .25rem;
    color: #fff;
}

.sticky {
    padding-top: 2rem;
    top: 0;
}

.sticky ul {
    line-height: 2;
    list-style: none;
}

.sticky > ul {
    padding-left: 1rem;
}

.sticky a {
    color: grey;
    text-decoration: none;
}

.sticky a:hover {
    color: #464646;
}

.sticky a.active {
    color: #ff5268;
}

.demo {
    font-size: 90%;
    margin-bottom: 2rem;
    text-align: right;
}

pre ~ .demo {
    margin-top: -1rem;
}

.badge-warning {
    font-weight: 600;
}

  </style>
</head>

<body>
  <div id="introduction">
    <div class="container">
      <nav class="navbar navbar-expand">
        <a class="navbar-brand py-0 mr-auto" href="https://fancyapps.com/fancybox/3/">
          fancybox
        </a>

        <ul class="navbar-nav ml-auto">
          <li class="nav-item px-2 d-none d-md-block">
            <a class="nav-link" href="https://fancyapps.com/fancybox/3/">Home</a>
          </li>
          <li class="nav-item px-2 d-none d-md-block">
            <a class="nav-link active" href="https://fancyapps.com/fancybox/3/docs/">Documentation</a>
          </li>
          <li class="nav-item px-2">
            <a class="nav-link" href="https://fancyapps.com/store/">Store</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="https://github.com/fancyapps/fancybox" target="_blank">
              <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" aria-label="Github" role="img" viewBox="0 0 512 512">
                <rect fill="#444" width="512" height="512" rx="50%"></rect>
                <path fill="#fff" d="M335 499c14 0 12 17 12 17H165s-2-17 12-17c13 0 16-6 16-12l-1-50c-71 16-86-28-86-28-12-30-28-37-28-37-24-16 1-16 1-16 26 2 40 26 40 26 22 39 59 28 74 22 2-17 9-28 16-35-57-6-116-28-116-126 0-28 10-51 26-69-3-6-11-32 3-67 0 0 21-7 70 26 42-12 86-12 128 0 49-33 70-26 70-26 14 35 6 61 3 67 16 18 26 41 26 69 0 98-60 120-117 126 10 8 18 24 18 48l-1 70c0 6 3 12 16 12z"></path>
              </svg>
            </a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="https://twitter.com/thefancyapps" target="_blank" title="Follow @thefancyapps">
              <svg width="24" height="24" xmlns="http://www.w3.org/2000/svg" aria-label="Twitter" role="img" viewBox="0 0 512 512">
                <rect fill="#444" width="512" height="512" rx="50%"></rect>
                <path fill="#fff" d="M437 152a72 72 0 0 1-40 12 72 72 0 0 0 32-40 72 72 0 0 1-45 17 72 72 0 0 0-122 65 200 200 0 0 1-145-74 72 72 0 0 0 22 94 72 72 0 0 1-32-7 72 72 0 0 0 56 69 72 72 0 0 1-32 1 72 72 0 0 0 67 50 200 200 0 0 1-105 29 200 200 0 0 0 309-179 200 200 0 0 0 35-37"></path>
              </svg>
            </a>
          </li>
        </ul>
      </nav>
    </div>
  </div>

  <div class="container">
    <div class="row">
      <div class="col-md-2" id="leftCol">
        <div class="sticky">
          <ul>
            <li>
              <a href="#introduction">Introduction</a>
            </li>
            <li>
              <a href="#setup">Setup</a>
            </li>
            <li>
              <a href="#usage">How to Use</a>
            </li>
            <li>
              <a href="#media_types">Media types</a>

              <ul>
                <li>
                  <a href="#images">Images</a>
                </li>
                <li>
                  <a href="#video">Video</a>
                </li>
                <li>
                  <a href="#iframe">Iframe</a>
                </li>
                <li>
                  <a href="#inline">Inline</a>
                </li>
                <li>
                  <a href="#ajax">Ajax</a>
                </li>
              </ul>
            </li>
            <li>
              <a href="#options">Options</a>
            </li>
            <li>
              <a href="#api">Api</a>
            </li>
            <li>
              <a href="#modules">Modules</a>
            </li>
            <li>
              <a href="#faq">FAQ</a>
            </li>
          </ul>
        </div>
      </div>

      <div class="col-md-10 px-2 px-md-5" id="mainCol">

        <section>
          <h2>Introduction</h2>

          <p>
            Get started with fancybox, probably the world’s most popular lightbox script.
          </p>
        </section>
        <section>

          <h3>
            Dependencies
          </h3>

          <p>
            jQuery 3+ is preferred, but fancybox works with jQuery 1.9.1+ and jQuery 2+
          </p>

          <h3>Compatibility</h3>

          <p>
            fancybox includes support for touch gestures and even supports pinch gestures for zooming.
            It is perfectly suited for both mobile and desktop browsers.
          </p>

          <p>
            fancybox has been tested in following browsers/devices:
          </p>

          <ul>
            <li>Chrome</li>
            <li>Firefox</li>
            <li>IE10/11</li>
            <li>Edge</li>
            <li>iOS Safari</li>
            <li>Android 7.0 Tablet</li>
          </ul>

        </section>

        <section id="setup">

          <h2>Setup</h2>

          <p>
            You can install fancybox by linking
            <code>.css</code> and
            <code>.js</code> files to your html file. Make sure you also load the jQuery library. Below is a basic HTML
            template to use as an example:
          </p>

          <pre>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
	&lt;meta charset=&quot;utf-8&quot;&gt;
	&lt;title&gt;My page&lt;/title&gt;

	&lt;!-- CSS --&gt;
	&lt;link rel=&quot;stylesheet&quot; type=&quot;text/css&quot; href=&quot;jquery.fancybox.min.css&quot;&gt;
&lt;/head&gt;
&lt;body&gt;

	&lt;!-- Your HTML content goes here --&gt;

	&lt;!-- JS --&gt;
	&lt;script src=&quot;https://code.jquery.com/jquery-3.2.1.min.js&quot;&gt;&lt;/script&gt;
	&lt;script src=&quot;jquery.fancybox.min.js&quot;&gt;&lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;
</pre>
          <h3>Download fancybox</h3>

          <p>
            Download the latest version of fancybox on
            <a href="https://github.com/fancyapps/fancybox" target="_blank">GitHub</a>.
            <br /> Or just link directly to fancybox files on cdnjs -
            <a href="https://cdnjs.com/libraries/fancybox" target="_blank">https://cdnjs.com/libraries/fancybox</a>.
          </p>

          <h3>Package Managers</h3>

          <p>
            fancybox is also available on npm and Bower.
          </p>

          <pre><code># NPM
npm install @fancyapps/fancybox --save

# Bower
bower install fancybox --save
</code></pre>

          <h4 class="mt-5">
            <span class="badge badge-warning">Important</span>
          </h4>

          <ul>
            <li>Make sure you add the jQuery library before the fancybox JS file</li>
            <li>If you already have jQuery on your page, you shouldn't include it second time</li>
            <li>Do not include both fancybox.js and fancybox.min.js files</li>
            <li>
              Some functionality (ajax, iframes, etc) will not work when you're opening local file directly on your
              browser, the code must
              be running on a web server
            </li>
          </ul>

        </section>

        <section id="usage">
          <h2>How to Use</h2>

          <h3>Initialize with data attributes</h3>

          <p>
            The most basic way to use fancybox is by adding the
            <code>data-fancybox</code> attribute to your element. This will automatically bind click event that will
            start
            fancybox. Use
            <code>href</code> or
            <code>data-src</code> attribute to specify source of your content. Example:
          </p>

          <pre>&lt;a href="image.jpg" data-fancybox data-caption="Caption for single image"&gt;
	&lt;img src="thumbnail.jpg" alt="" /&gt;
&lt;/a&gt;</pre>

          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/oPKVea?editors=1000" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            If you have a group of items, you can use the same attribute
            <code>data-fancybox</code> value for each of them to create a gallery. Each group should have a unique
            value.
            Example:
          </p>

          <pre>&lt;a href="image_1.jpg" data-fancybox="gallery" data-caption="Caption #1"&gt;
	&lt;img src="thumbnail_1.jpg" alt="" /&gt;
&lt;/a&gt;

&lt;a href="image_2.jpg" data-fancybox="gallery" data-caption="Caption #2"&gt;
	&lt;img src="thumbnail_2.jpg" alt="" /&gt;
&lt;/a&gt;
</pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/wEVOPa?editors=1000" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            If you choose this method, default settings will be applied. See
            <a href="#options">options</a> section for examples how to customize
            by changing defaults, using
            <code>data-options</code> attribute or by <a href="#initialize-with-javascript">initializing with
              JavaScript</a>.
          </p>

          <p>
            <span class="badge badge-info">Info</span>
            Sometimes you have multiple links pointing to the same source and that creates duplicates in the gallery.
            To avoid that, simply use
            <code>data-fancybox-trigger</code> attribute with the same value used for
            <code>data-fancybox</code> attribute for your other links. Optionally, use
            <code>data-fancybox-index</code> attribute to specify index of starting element:
          </p>

          <pre><code>&lt;a data-fancybox-trigger=&quot;gallery&quot; href=&quot;javascript:;&quot;&gt;
    &lt;img src="thumbnail_1.jpg" alt="" /&gt;
&lt;/a&gt;
</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/VGoRqO?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <h3 id="initialize-with-javascript">Initialize with JavaScript</h3>

          <p>
            Select your elements with a jQuery selector (you can use any valid selector) and call the
            <code>fancybox</code> method:
          </p>

          <pre><code>$('[data-fancybox="gallery"]').fancybox({
	// Options will go here
});
</code></pre>

          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/vzoPMB?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            <span class="badge badge-info">Info</span>
            Sometimes you might need to bind fancybox to dynamically added elements. Use
            <code>selector</code> option to attach click event listener for elements that exist now or in the future.
            All selected items will be automatically grouped in the gallery. Example:
          </p>

          <pre><code>$().fancybox({
    selector : '.imglist a:visible'
});</code></pre>

          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/NLQJQp?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <h3>Use with Javascript</h3>

          <p>
            You can also open and close fancybox programmatically. Here are a couple of examples, visit
            <a href="#api">API</a> section for more information and demos.
          </p>
          <p>
            Display simple message:
          </p>

          <pre><code>$.fancybox.open('&lt;div class=&quot;message&quot;&gt;&lt;h2&gt;Hello!&lt;/h2&gt;&lt;p&gt;You are awesome!&lt;/p&gt;&lt;/div&gt;');</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/qMewWv?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            Display iframed page:
          </p>

          <pre><code>$.fancybox.open({
	src  : 'link-to-your-page.html',
	type : 'iframe',
	opts : {
		afterShow : function( instance, current ) {
			console.info( 'done!' );
		}
	}
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/GXVLgo?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <h4 class="mt-4">
            <span class="badge badge-warning">Important</span>
          </h4>

          <p>
            fancybox attempts to automatically detect the type of content based on the given url. If it cannot be
            detected, the type can also be set manually using
            <code>data-type</code> attribute (or
            <code>type</code> option). Example:
          </p>

          <pre>&lt;a href="images.php?id=123" data-type="image" data-caption="Caption"&gt;
	Show image
&lt;/a&gt;</pre>

        </section>
        <section id="media_types">

          <h2>Media types</h2>

          <p>
            fancybox is designed to display images, video, iframes and any HTML content. For your convenience, there is
            a built in support for inline content and ajax.
          </p>

          <h3 id="images">Images</h3>

          <p>
            The standard way of using fancybox is with a number of thumbnail images that link to larger images:
          </p>

          <pre><code>&lt;a href="image.jpg" data-fancybox="images" data-caption="My caption"&gt;
	&lt;img src="thumbnail.jpg" alt="" /&gt;
&lt;/a&gt;</code></pre>

          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/EeqJPG?editors=1000" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            By default, fancybox fully preloads an image before displaying it. You can choose to display the image
            right away. It will render and show the full size image while the data is being received. To do so, some
            attributes are necessary:
          </p>

          <ul>
            <li>
              <code>data-width</code> &nbsp; - the real width of the image</li>
            <li>
              <code>data-height</code> - the real height of the image</li>
          </ul>

          <pre><code>&lt;a href=&quot;image.jpg&quot; data-fancybox=&quot;images&quot; data-width=&quot;2048&quot; data-height=&quot;1365&quot;&gt;
    &lt;img src=&quot;thumbnail.jpg&quot; /&gt;
&lt;/a&gt;</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/bxXJRr?editors=1000" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            You can also use these
            <code>width</code> and
            <code>height</code> properties to control size of the image. This can be used to make images look sharper
            on retina displays. Example:
          </p>

          <pre><code>$('[data-fancybox=&quot;images&quot;]').fancybox({
    afterLoad : function(instance, current) {
        var pixelRatio = window.devicePixelRatio || 1;

        if ( pixelRatio &gt; 1.5 ) {
            current.width  = current.width  / pixelRatio;
            current.height = current.height / pixelRatio;
        }
    }
});</code></pre>

          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/gdVyxg?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            fancybox supports "srcset" so it can display different images based on viewport width. You can use this to
            improve download times for mobile users and over time save bandwidth. Example:
          </p>

          <pre><code>&lt;a href=&quot;medium.jpg&quot; data-fancybox=&quot;images&quot; data-srcset=&quot;large.jpg 1600w, medium.jpg 1200w, small.jpg 640w&quot;&gt;
	&lt;img src=&quot;thumbnail.jpg&quot; /&gt;
&lt;/a&gt;</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/LJwvzY?editors=1000" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            It is also possible to protect images from downloading by right-click. While this does not protect from
            truly determined users, it should discourage the vast majority from ripping off your files.
            Optionally, put the watermark over image.
          </p>

          <pre><code>$('[data-fancybox]').fancybox({
	protect: true
});</code></pre>

          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/QVePOQ" target="_blank">View demo on CodePen</a>
          </p>

          <h3 id="video">Video</h3>

          <p>
            YouTube and Vimeo videos can be used with fancybox by just providing the page URL. Link to MP4 video
            directly or use trigger element to display hidden <code>&lt;video&gt;</code> element.
          </p>

          <p>
            Use
            <code>data-width</code> and
            <code>data-height</code> attributes to customize video dimensions and
            <code>data-ratio</code> for the aspect ratio.
          </p>

          <pre><code>&lt;a data-fancybox href="https://www.youtube.com/watch?v=_sI_Ps7JSEk"&gt;
    YouTube video
&lt;/a&gt;

&lt;a data-fancybox href="https://vimeo.com/191947042"&gt;
    Vimeo video
&lt;/a&gt;

&lt;a data-fancybox data-width=&quot;640&quot; data-height=&quot;360&quot; href=&quot;video.mp4&quot;&gt;
    Direct link to MP4 video
&lt;/a&gt;

&lt;a data-fancybox href=&quot;#myVideo&quot;&gt;
    HTML5 video element
&lt;/a&gt;

&lt;video width=&quot;640&quot; height=&quot;320&quot; controls id=&quot;myVideo&quot; style=&quot;display:none;&quot;&gt;
    &lt;source src=&quot;https://www.html5rocks.com/en/tutorials/video/basics/Chrome_ImF.mp4&quot; type=&quot;video/mp4&quot;&gt;
    &lt;source src=&quot;https://www.html5rocks.com/en/tutorials/video/basics/Chrome_ImF.webm&quot; type=&quot;video/webm&quot;&gt;
    &lt;source src=&quot;https://www.html5rocks.com/en/tutorials/video/basics/Chrome_ImF.ogv&quot; type=&quot;video/ogg&quot;&gt;
    Your browser doesn't support HTML5 video tag.
&lt;/video&gt;</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/GXVLyr?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            Controlling YouTube &amp; Vimeo video via URL parameters:
          </p>

          <pre><code>&lt;a data-fancybox href=&quot;https://www.youtube.com/watch?v=_sI_Ps7JSEk&amp;amp;autoplay=1&amp;amp;rel=0&amp;amp;controls=0&amp;amp;showinfo=0&quot;&gt;
    YouTube video - hide controls and info
&lt;/a&gt;

&lt;a data-fancybox href=&quot;https://vimeo.com/191947042?color=f00&quot;&gt;
    Vimeo video - custom color
&lt;/a&gt;</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/NLQmzy?editors=1000" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            Via JavaScript:
          </p>

          <pre><code>$('[data-fancybox]').fancybox({
    youtube : {
        controls : 0,
        showinfo : 0
    },
    vimeo : {
        color : 'f00'
    }
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/WgVWKz?editors=1010" target="_blank">View demo on CodePen</a>
          </p>



          <h3 id="iframe">Iframe</h3>

          <p>
            If you need to display content from another page, add <code>data-fancybox</code> and <code>data-type="iframe"</code>
            attributes to your link. This would create <code>&lt;iframe&gt;</iframe></code> element that allows to
            embed an entire web document inside the modal.
          </p>

          <pre><code>&lt;a data-fancybox data-type="iframe" data-src=&quot;http://codepen.io/fancyapps/full/jyEGGG/&quot; href=&quot;javascript:;&quot;&gt;
	Webpage
&lt;/a&gt;

&lt;a data-fancybox data-type="iframe" data-src=&quot;https://mozilla.github.io/pdf.js/web/viewer.html&quot; href=&quot;javascript:;&quot;&gt;
    Sample PDF file 
&lt;/a&gt;
</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/RYXOeK?editors=1000" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            If you have not disabled iframe preloading (using
            <code>preload</code> option), the script will atempt to calculate content dimensions and will adjust
            width/height of <code>&lt;iframe&gt;</iframe></code> to fit with content in it. Keep in mind, that due to
            <a href="https://en.wikipedia.org/wiki/Same-origin_policy" target="_blank">same origin policy</a>, there
            are some limitations.
          </p>

          <p>
            This example will disable iframe preloading and will display small close button next to iframe instead of
            the toolbar:
          </p>

          <pre><code>$('[data-fancybox]').fancybox({
	toolbar  : false,
	smallBtn : true,
	iframe : {
		preload : false
	}
})
</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/pOMBQx" target="_blank">View demo on CodePen</a>
          </p>


          <p>
            Iframe dimensions can be controlled by CSS:
          </p>

          <pre><code>.fancybox-slide--iframe .fancybox-content {
    width  : 800px;
    height : 600px;
    max-width  : 80%;
    max-height : 80%;
    margin: 0;
}</code></pre>

          <p>
            These CSS rules can be overridden by JS, if needed:
          </p>

          <pre><code>$(&quot;[data-fancybox]&quot;).fancybox({
    iframe : {
        css : {
            width : '600px'
        }
    }
});</code></pre>

          <p>
            How to access and control fancybox in parent window from inside an iframe:
          </p>

          <pre><code>// Close current fancybox instance
parent.jQuery.fancybox.getInstance().close();

// Adjust iframe height according to the contents
parent.jQuery.fancybox.getInstance().update();
</code></pre>

          <h3 id="inline">Inline</h3>

          <p>
            fancybox can be used to display any HTML element on the page.
            First, create a hidden element with unique ID:
          </p>

          <pre><code>&lt;div style=&quot;display: none;&quot; id=&quot;hidden-content&quot;&gt;
	&lt;h2&gt;Hello&lt;/h2&gt;
	&lt;p&gt;You are awesome.&lt;/p&gt;
&lt;/div&gt;</code></pre>

          <p>
            Then simply create a link having
            <code>data-src</code> attribute that matches ID of the element you want to open (preceded by a hash mark
            (#); in this example - <code>#hidden-content</code>):
          </p>

          <pre><code>&lt;a data-fancybox data-src=&quot;#hidden-content&quot; href=&quot;javascript:;&quot;&gt;
	Trigger the fancybox
&lt;/a&gt;</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/zJgXyg?editors=1100" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            The script will append small close button (if you have not disabled by
            <code>smallBtn:false</code>) and will not apply any styles except for centering. Therefore you can easily
            set custom dimensions using CSS.
          </p>

          <p>
            <span class="badge badge-info">Info</span> If necessary, you can make your element (and similarly any other
            html content) scrollable by adding additional wrapping element and some CSS -

            <a href="https://codepen.io/fancyapps/pen/yxmrwG?editors=1100" target="_blank">view demo on CodePen</a>.
          </p>


          <h3 id="ajax">Ajax</h3>

          <p>
            To load your content via AJAX, you need to add a
            <code>data-type="ajax"</code> attribute to your link:
          </p>

          <pre><code>&lt;a data-fancybox data-type=&quot;ajax&quot; data-src=&quot;my_page.com/path/to/ajax/&quot; href=&quot;javascript:;&quot;&gt;
	AJAX content
&lt;/a&gt;</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/yxmrWZ?editors=1100" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            Additionally it is possible to define a selector with the
            <code>data-filter</code> attribute to show only a part of the response. The selector can be any string,
            that is a valid jQuery selector:
          </p>

          <pre><code>&lt;a data-fancybox data-type=&quot;ajax&quot; data-src=&quot;my_page.com/path/to/ajax/&quot; data-filter=&quot;#two&quot; href=&quot;javascript:;&quot;&gt;
	AJAX content
&lt;/a&gt;
</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/jvgRRe?editors=1100" target="_blank">View demo on CodePen</a>
          </p>

        </section>


        <section id="options">
          <h2>Options</h2>

          <p>
            Quick reference for all default options as defined in the source:
          </p>

          <pre>var defaults = {
  // Close existing modals
  // Set this to false if you do not need to stack multiple instances
  closeExisting: false,

  // Enable infinite gallery navigation
  loop: false,

  // Horizontal space between slides
  gutter: 50,

  // Enable keyboard navigation
  keyboard: true,

  // Should allow caption to overlap the content
  preventCaptionOverlap: true,

  // Should display navigation arrows at the screen edges
  arrows: true,

  // Should display counter at the top left corner
  infobar: true,

  // Should display close button (using `btnTpl.smallBtn` template) over the content
  // Can be true, false, &quot;auto&quot;
  // If &quot;auto&quot; - will be automatically enabled for &quot;html&quot;, &quot;inline&quot; or &quot;ajax&quot; items
  smallBtn: &quot;auto&quot;,

  // Should display toolbar (buttons at the top)
  // Can be true, false, &quot;auto&quot;
  // If &quot;auto&quot; - will be automatically hidden if &quot;smallBtn&quot; is enabled
  toolbar: &quot;auto&quot;,

  // What buttons should appear in the top right corner.
  // Buttons will be created using templates from `btnTpl` option
  // and they will be placed into toolbar (class=&quot;fancybox-toolbar&quot;` element)
  buttons: [
    &quot;zoom&quot;,
    //&quot;share&quot;,
    &quot;slideShow&quot;,
    //&quot;fullScreen&quot;,
    //&quot;download&quot;,
    &quot;thumbs&quot;,
    &quot;close&quot;
  ],

  // Detect &quot;idle&quot; time in seconds
  idleTime: 3,

  // Disable right-click and use simple image protection for images
  protect: false,

  // Shortcut to make content &quot;modal&quot; - disable keyboard navigtion, hide buttons, etc
  modal: false,

  image: {
    // Wait for images to load before displaying
    //   true  - wait for image to load and then display;
    //   false - display thumbnail and load the full-sized image over top,
    //           requires predefined image dimensions (`data-width` and `data-height` attributes)
    preload: false
  },

  ajax: {
    // Object containing settings for ajax request
    settings: {
      // This helps to indicate that request comes from the modal
      // Feel free to change naming
      data: {
        fancybox: true
      }
    }
  },

  iframe: {
    // Iframe template
    tpl:
      '&lt;iframe id=&quot;fancybox-frame{rnd}&quot; name=&quot;fancybox-frame{rnd}&quot; class=&quot;fancybox-iframe&quot; allowfullscreen allow=&quot;autoplay; fullscreen&quot; src=&quot;&quot;&gt;&lt;/iframe&gt;',

    // Preload iframe before displaying it
    // This allows to calculate iframe content width and height
    // (note: Due to &quot;Same Origin Policy&quot;, you can't get cross domain data).
    preload: true,

    // Custom CSS styling for iframe wrapping element
    // You can use this to set custom iframe dimensions
    css: {},

    // Iframe tag attributes
    attr: {
      scrolling: &quot;auto&quot;
    }
  },

  // For HTML5 video only
  video: {
    tpl:
      '&lt;video class=&quot;fancybox-video&quot; controls controlsList=&quot;nodownload&quot; poster=&quot;{{poster}}&quot;&gt;' +
      '&lt;source src=&quot;{{src}}&quot; type=&quot;{{format}}&quot; /&gt;' +
      'Sorry, your browser doesn\'t support embedded videos, &lt;a href=&quot;{{src}}&quot;&gt;download&lt;/a&gt; and watch with your favorite video player!' +
      &quot;&lt;/video&gt;&quot;,
    format: &quot;&quot;, // custom video format
    autoStart: true
  },

  // Default content type if cannot be detected automatically
  defaultType: &quot;image&quot;,

  // Open/close animation type
  // Possible values:
  //   false            - disable
  //   &quot;zoom&quot;           - zoom images from/to thumbnail
  //   &quot;fade&quot;
  //   &quot;zoom-in-out&quot;
  //
  animationEffect: &quot;zoom&quot;,

  // Duration in ms for open/close animation
  animationDuration: 366,

  // Should image change opacity while zooming
  // If opacity is &quot;auto&quot;, then opacity will be changed if image and thumbnail have different aspect ratios
  zoomOpacity: &quot;auto&quot;,

  // Transition effect between slides
  //
  // Possible values:
  //   false            - disable
  //   &quot;fade'
  //   &quot;slide'
  //   &quot;circular'
  //   &quot;tube'
  //   &quot;zoom-in-out'
  //   &quot;rotate'
  //
  transitionEffect: &quot;fade&quot;,

  // Duration in ms for transition animation
  transitionDuration: 366,

  // Custom CSS class for slide element
  slideClass: &quot;&quot;,

  // Custom CSS class for layout
  baseClass: &quot;&quot;,

  // Base template for layout
  baseTpl:
    '&lt;div class=&quot;fancybox-container&quot; role=&quot;dialog&quot; tabindex=&quot;-1&quot;&gt;' +
    '&lt;div class=&quot;fancybox-bg&quot;&gt;&lt;/div&gt;' +
    '&lt;div class=&quot;fancybox-inner&quot;&gt;' +
    '&lt;div class=&quot;fancybox-infobar&quot;&gt;&lt;span data-fancybox-index&gt;&lt;/span&gt;&amp;nbsp;/&amp;nbsp;&lt;span data-fancybox-count&gt;&lt;/span&gt;&lt;/div&gt;' +
    '&lt;div class=&quot;fancybox-toolbar&quot;&gt;{{buttons}}&lt;/div&gt;' +
    '&lt;div class=&quot;fancybox-navigation&quot;&gt;{{arrows}}&lt;/div&gt;' +
    '&lt;div class=&quot;fancybox-stage&quot;&gt;&lt;/div&gt;' +
    '&lt;div class=&quot;fancybox-caption&quot;&gt;&lt;div class="&quot;fancybox-caption__body&quot;&gt;&lt;/div&gt;&lt;/div&gt;' +
    '&lt;/div&gt;' +
    '&lt;/div&gt;',

  // Loading indicator template
  spinnerTpl: '&lt;div class=&quot;fancybox-loading&quot;&gt;&lt;/div&gt;',

  // Error message template
  errorTpl: '&lt;div class=&quot;fancybox-error&quot;&gt;&lt;p&gt;{{ERROR}}&lt;/p&gt;&lt;/div&gt;',

  btnTpl: {
    download:
      '&lt;a download data-fancybox-download class=&quot;fancybox-button fancybox-button--download&quot; title=&quot;{{DOWNLOAD}}&quot; href=&quot;javascript:;&quot;&gt;' +
      '&lt;svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 24 24&quot;&gt;&lt;path d=&quot;M18.62 17.09V19H5.38v-1.91zm-2.97-6.96L17 11.45l-5 4.87-5-4.87 1.36-1.32 2.68 2.64V5h1.92v7.77z&quot;/&gt;&lt;/svg&gt;' +
      &quot;&lt;/a&gt;&quot;,

    zoom:
      '&lt;button data-fancybox-zoom class=&quot;fancybox-button fancybox-button--zoom&quot; title=&quot;{{ZOOM}}&quot;&gt;' +
      '&lt;svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 24 24&quot;&gt;&lt;path d=&quot;M18.7 17.3l-3-3a5.9 5.9 0 0 0-.6-7.6 5.9 5.9 0 0 0-8.4 0 5.9 5.9 0 0 0 0 8.4 5.9 5.9 0 0 0 7.7.7l3 3a1 1 0 0 0 1.3 0c.4-.5.4-1 0-1.5zM8.1 13.8a4 4 0 0 1 0-5.7 4 4 0 0 1 5.7 0 4 4 0 0 1 0 5.7 4 4 0 0 1-5.7 0z&quot;/&gt;&lt;/svg&gt;' +
      &quot;&lt;/button&gt;&quot;,

    close:
      '&lt;button data-fancybox-close class=&quot;fancybox-button fancybox-button--close&quot; title=&quot;{{CLOSE}}&quot;&gt;' +
      '&lt;svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 24 24&quot;&gt;&lt;path d=&quot;M12 10.6L6.6 5.2 5.2 6.6l5.4 5.4-5.4 5.4 1.4 1.4 5.4-5.4 5.4 5.4 1.4-1.4-5.4-5.4 5.4-5.4-1.4-1.4-5.4 5.4z&quot;/&gt;&lt;/svg&gt;' +
      &quot;&lt;/button&gt;&quot;,

    // Arrows
    arrowLeft:
      '&lt;button data-fancybox-prev class=&quot;fancybox-button fancybox-button--arrow_left&quot; title=&quot;{{PREV}}&quot;&gt;' +
      '&lt;div&gt;&lt;svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 24 24&quot;&gt;&lt;path d=&quot;M11.28 15.7l-1.34 1.37L5 12l4.94-5.07 1.34 1.38-2.68 2.72H19v1.94H8.6z&quot;/&gt;&lt;/svg&gt;&lt;/div&gt;' +
      &quot;&lt;/button&gt;&quot;,

    arrowRight:
      '&lt;button data-fancybox-next class=&quot;fancybox-button fancybox-button--arrow_right&quot; title=&quot;{{NEXT}}&quot;&gt;' +
      '&lt;div&gt;&lt;svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 24 24&quot;&gt;&lt;path d=&quot;M15.4 12.97l-2.68 2.72 1.34 1.38L19 12l-4.94-5.07-1.34 1.38 2.68 2.72H5v1.94z&quot;/&gt;&lt;/svg&gt;&lt;/div&gt;' +
      &quot;&lt;/button&gt;&quot;,

    // This small close button will be appended to your html/inline/ajax content by default,
    // if &quot;smallBtn&quot; option is not set to false
    smallBtn:
      '&lt;button type=&quot;button&quot; data-fancybox-close class=&quot;fancybox-button fancybox-close-small&quot; title=&quot;{{CLOSE}}&quot;&gt;' +
      '&lt;svg xmlns=&quot;http://www.w3.org/2000/svg&quot; version=&quot;1&quot; viewBox=&quot;0 0 24 24&quot;&gt;&lt;path d=&quot;M13 12l5-5-1-1-5 5-5-5-1 1 5 5-5 5 1 1 5-5 5 5 1-1z&quot;/&gt;&lt;/svg&gt;' +
      &quot;&lt;/button&gt;&quot;
  },

  // Container is injected into this element
  parentEl: &quot;body&quot;,

  // Hide browser vertical scrollbars; use at your own risk
  hideScrollbar: true,

  // Focus handling
  // ==============

  // Try to focus on the first focusable element after opening
  autoFocus: true,

  // Put focus back to active element after closing
  backFocus: true,

  // Do not let user to focus on element outside modal content
  trapFocus: true,

  // Module specific options
  // =======================

  fullScreen: {
    autoStart: false
  },

  // Set `touch: false` to disable panning/swiping
  touch: {
    vertical: true, // Allow to drag content vertically
    momentum: true // Continue movement after releasing mouse/touch when panning
  },

  // Hash value when initializing manually,
  // set `false` to disable hash change
  hash: null,

  // Customize or add new media types
  // Example:
  /*
    media : {
      youtube : {
        params : {
          autoplay : 0
        }
      }
    }
  */
  media: {},

  slideShow: {
    autoStart: false,
    speed: 3000
  },

  thumbs: {
    autoStart: false, // Display thumbnails on opening
    hideOnClose: true, // Hide thumbnail grid when closing animation starts
    parentEl: &quot;.fancybox-container&quot;, // Container is injected into this element
    axis: &quot;y&quot; // Vertical (y) or horizontal (x) scrolling
  },

  // Use mousewheel to navigate gallery
  // If 'auto' - enabled for images only
  wheel: &quot;auto&quot;,

  // Callbacks
  //==========

  // See Documentation/API/Events for more information
  // Example:
  /*
    afterShow: function( instance, current ) {
      console.info( 'Clicked element:' );
      console.info( current.opts.$orig );
    }
  */

  onInit: $.noop, // When instance has been initialized

  beforeLoad: $.noop, // Before the content of a slide is being loaded
  afterLoad: $.noop, // When the content of a slide is done loading

  beforeShow: $.noop, // Before open animation starts
  afterShow: $.noop, // When content is done loading and animating

  beforeClose: $.noop, // Before the instance attempts to close. Return false to cancel the close.
  afterClose: $.noop, // After instance has been closed

  onActivate: $.noop, // When instance is brought to front
  onDeactivate: $.noop, // When other instance has been activated

  // Interaction
  // ===========

  // Use options below to customize taken action when user clicks or double clicks on the fancyBox area,
  // each option can be string or method that returns value.
  //
  // Possible values:
  //   &quot;close&quot;           - close instance
  //   &quot;next&quot;            - move to next gallery item
  //   &quot;nextOrClose&quot;     - move to next gallery item or close if gallery has only one item
  //   &quot;toggleControls&quot;  - show/hide controls
  //   &quot;zoom&quot;            - zoom image (if loaded)
  //   false             - do nothing

  // Clicked on the content
  clickContent: function(current, event) {
    return current.type === &quot;image&quot; ? &quot;zoom&quot; : false;
  },

  // Clicked on the slide
  clickSlide: &quot;close&quot;,

  // Clicked on the background (backdrop) element;
  // if you have not changed the layout, then most likely you need to use `clickSlide` option
  clickOutside: &quot;close&quot;,

  // Same as previous two, but for double click
  dblclickContent: false,
  dblclickSlide: false,
  dblclickOutside: false,

  // Custom options when mobile device is detected
  // =============================================

  mobile: {
    preventCaptionOverlap: false,
    idleTime: false,
    clickContent: function(current, event) {
      return current.type === &quot;image&quot; ? &quot;toggleControls&quot; : false;
    },
    clickSlide: function(current, event) {
      return current.type === &quot;image&quot; ? &quot;toggleControls&quot; : &quot;close&quot;;
    },
    dblclickContent: function(current, event) {
      return current.type === &quot;image&quot; ? &quot;zoom&quot; : false;
    },
    dblclickSlide: function(current, event) {
      return current.type === &quot;image&quot; ? &quot;zoom&quot; : false;
    }
  },

  // Internationalization
  // ====================

  lang: &quot;en&quot;,
  i18n: {
    en: {
      CLOSE: &quot;Close&quot;,
      NEXT: &quot;Next&quot;,
      PREV: &quot;Previous&quot;,
      ERROR: &quot;The requested content cannot be loaded. &lt;br/&gt; Please try again later.&quot;,
      PLAY_START: &quot;Start slideshow&quot;,
      PLAY_STOP: &quot;Pause slideshow&quot;,
      FULL_SCREEN: &quot;Full screen&quot;,
      THUMBS: &quot;Thumbnails&quot;,
      DOWNLOAD: &quot;Download&quot;,
      SHARE: &quot;Share&quot;,
      ZOOM: &quot;Zoom&quot;
    },
    de: {
      CLOSE: &quot;Schliessen&quot;,
      NEXT: &quot;Weiter&quot;,
      PREV: &quot;Zurück&quot;,
      ERROR: &quot;Die angeforderten Daten konnten nicht geladen werden. &lt;br/&gt; Bitte versuchen Sie es später nochmal.&quot;,
      PLAY_START: &quot;Diaschau starten&quot;,
      PLAY_STOP: &quot;Diaschau beenden&quot;,
      FULL_SCREEN: &quot;Vollbild&quot;,
      THUMBS: &quot;Vorschaubilder&quot;,
      DOWNLOAD: &quot;Herunterladen&quot;,
      SHARE: &quot;Teilen&quot;,
      ZOOM: &quot;Maßstab&quot;
    }
  }
};
</pre>


          <p>
            Set instance options by passing a valid object to <code>fancybox()</code> method:
          </p>

          <pre><code>$('[data-fancybox="gallery"]').fancybox({
	thumbs : {
		autoStart : true
	}
});</code></pre>


          <p>
            Plugin options / defaults are exposed in
            <code>$.fancybox.defaults</code> namespace so you can easily adjust them globally:
          </p>

          <pre><code>$.fancybox.defaults.animationEffect = "fade";</code></pre>

          <p>
            Custom options for each element individually can be set by adding a
            <code>data-options</code> attribute to the element.
            This attribute should contain the properly formatted JSON object
            (remember, strings should be wrapped in double quotes).
          </p>
          <p>
            It is also possible to quickly set any option using
            <em>parameterized</em> name of the selected option, for example,
            <code>animationEffect</code> would be <code>data-animation-effect</code>:
          </p>

          <pre><code>&lt;a data-fancybox data-options='{&quot;caption&quot; : &quot;My caption&quot;, &quot;src&quot; : &quot;https://codepen.io/about/&quot;, &quot;type&quot; : &quot;iframe&quot;}' href=&quot;javascript:;&quot; class=&quot;btn btn-primary&quot;&gt;
    Example #1
&lt;/a&gt;

&lt;a data-fancybox data-animation-effect=&quot;false&quot; href=&quot;https://source.unsplash.com/0JYgd2QuMfw/1500x1000&quot; class=&quot;btn btn-primary&quot;&gt;
    Example #2
&lt;/a&gt;
</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/PdMgrO?editors=1000" target="_blank">View demo on CodePen</a>
          </p>

        </section>
        <section id="api">

          <h2>API</h2>

          <p>
            The fancybox API offers a couple of methods to control fancybox. This gives you the ability to extend the
            plugin and to integrate it with other web application components.
          </p>

          <h3 id="core_methods">Core methods</h3>

          <p>
            Core methods are methods which affect/handle instances:
          </p>


          <pre><code>// Start new fancybox instance
$.fancybox.open( items, opts, index );

// Get refrence to currently active fancybox instance
$.fancybox.getInstance();

// Close currently active fancybox instance (pass `true` to close all instances) 
$.fancybox.close();

// Close all instances and unbind all events
$.fancybox.destroy();
</code></pre>


          <h3 id="api_usage">Starting fancybox</h3>

          <p>
            When creating group objects manually, each item should follow this pattern:
          </p>

          <pre><code>{
	src  : '' // Source of the content
	type : '' // Content type: image|inline|ajax|iframe|html (optional)
	opts : {} // Object containing item options (optional)
}
</code></pre>

          <p>
            Example of opening image gallery programmatically:
          </p>

          <pre><code>$.fancybox.open([
	{
		src  : '1_b.jpg',
		opts : {
			caption : 'First caption',
			thumb   : '1_s.jpg'
		}
	},
	{
		src  : '2_b.jpg',
		opts : {
			caption : 'Second caption',
			thumb   : '2_s.jpg'
		}
	}
], {
	loop : false
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/wEVbdY?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            It is also possible to pass only one object. Example of opening inline content:
          </p>

          <pre><code>$.fancybox.open({
	src  : '#hidden-content',
	type : 'inline',
	opts : {
		afterShow : function( instance, current ) {
			console.info( 'done!' );
		}
	}
});
</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/mGNYwX?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            If you wish to quickly display some html content (for example, a message), then you can use a simpler
            syntax. Do not forget to use a wrapping element around your content.
          </p>

          <pre><code>$.fancybox.open('&lt;div class=&quot;message&quot;&gt;&lt;h2&gt;Hello!&lt;/h2&gt;&lt;p&gt;You are awesome!&lt;/p&gt;&lt;/div&gt;');</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/qMeGVr" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            Group items can be collection of jQuery objects, too.
            This can be used, for example, to display group of inline elements:
          </p>

          <pre><code>$('#test').on('click', function() {
  $.fancybox.open( $('.inline-gallery'), {
    touch: false
  });
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/GXVayG?editors=1010" target="_blank">View demo on CodePen</a>
          </p>


          <h3 id="instance_methods">Instance methods</h3>

          <p>
            In order to use these methods, you need an instance of the plugin's object.
            There are 3 common ways to get the reference.
          </p>

          <p>
            1) Using API method to get currently active instance:
          </p>

          <pre><code>var instance = $.fancybox.getInstance();</code></pre>

          <p>
            2) While starting fancybox programmatically:
          </p>
          <pre><code>var instance = $.fancybox.open(
	// Your content and options
);</code></pre>

          <p>
            3) From within the callback - first argument is always a reference to current instance:
          </p>

          <pre><code>$('[data-fancybox="gallery"]').fancybox({
	afterShow : function( instance, current ) {
		console.info( instance );
	}
});</code></pre>
          <p>
            Once you have a reference to fancybox instance the following methods are available:
          </p>

          <pre><code>// Go to next gallery item
instance.next( duration );

// Go to previous gallery item
instance.previous( duration );

// Switch to selected gallery item
instance.jumpTo( index, duration );

// Check if current image dimensions are smaller than actual
instance.isScaledDown();

// Scale image to the actual size of the image
instance.scaleToActual( x, y, duration );

// Check if image dimensions exceed parent element
instance.canPan();

// Scale image to fit inside parent element
instance.scaleToFit( duration );

// Update position and content of all slides
instance.update();

// Update slide position and scale content to fit
instance.updateSlide( slide );

// Update infobar values, navigation button states and reveal caption
instance.updateControls( force );

// Load custom content into the slide
instance.setContent( slide, content );

// Show loading icon inside the slide
instance.showLoading( slide );

// Remove loading icon from the slide
instance.hideLoading( slide );

// Try to find and focus on the first focusable element
instance.focus();

// Activates current instance, brings it to the front
instance.activate();

// Close instance
instance.close();
</code></pre>

          <p>
            You can also do something like this:
          </p>

          <pre><code>$.fancybox.getInstance().jumpTo(1);</code></pre>

          <p>
            or simply:
          </p>

          <pre><code>$.fancybox.getInstance('jumpTo', 1);</code></pre>

          <h3 id="events">Events</h3>

          <p>
            fancybox fires several events:
          </p>

          <pre><code>beforeLoad   : Before the content of a slide is being loaded
afterLoad    : When the content of a slide is done loading

beforeShow   : Before open animation starts
afterShow    : When content is done loading and animating

beforeClose  : Before the instance attempts to close. Return false to cancel the close.
afterClose   : After instance has been closed

onInit       : When instance has been initialized
onActivate   : When instance is brought to front
onDeactivate : When other instance has been activated</code></pre>

          <p>
            Event callbacks can be set as function properties of the options object passed to fancybox initialization
            function:
          </p>

          <pre><code>&lt;script type=&quot;text/javascript&quot;&gt;
	$(&quot;[data-fancybox]&quot;).fancybox({
		afterShow: function( instance, slide ) {

			// Tip: Each event passes useful information within the event object:

			// Object containing references to interface elements
			// (background, buttons, caption, etc)
			// console.info( instance.$refs );

			// Current slide options
			// console.info( slide.opts );

			// Clicked element
			// console.info( slide.opts.$orig );

			// Reference to DOM element of the slide
			// console.info( slide.$slide );

		}
	});
&lt;/script&gt;</code></pre>

          <p>
            Each callback receives two parameters - current fancybox instance and current gallery object, if exists.
          </p>

          <p>
            It is also possible to attach event handler for all instances. To prevent interfering with other scripts,
            these events have been namespaced to <code>.fb</code>.
            These handlers receive 3 parameters - event, current fancybox instance and current gallery object.
          </p>
          <p>
            Here is an example of binding to the
            <code>afterShow</code> event:
          </p>

          <pre><code>$(document).on('afterShow.fb', function( e, instance, slide ) {
	// Your code goes here
});</code></pre>


          <p>
            If you wish to prevent closing of the modal (for example, after form submit), you can use
            <code>beforeClose</code> callback. Simply return
            <code>false</code>:
          </p>

          <pre><code>beforeClose : function( instance, current, e ) {
	if ( $('#my-field').val() == '' ) {
		return false;
	}
}
</code></pre>

        </section>
        <section id="modules">
          <h2>Modules</h2>

          <p>
            fancybox code is split into several files (modules) that extend core functionality. You can build your own
            fancybox version by excluding unnecessary modules, if needed.
            Each one has their own <code>js</code> and/or <code>css</code> files.
          </p>

          <p>
            Some modules can be customized and controlled programmatically. List of all possible options:
          </p>

          <pre><code>fullScreen: {
  autoStart: false
},

touch : {
  vertical : true,  // Allow to drag content vertically
  momentum : true   // Continuous movement when panning
},

// Hash value when initializing manually,
// set `false` to disable hash change
hash : null,

// Customize or add new media types
// Example:
/*
media : {
  youtube : {
    params : {
      autoplay : 0
    }
  }
}
*/
media : {},

slideShow : {
  autoStart : false,
  speed     : 4000
},

thumbs : {
  autoStart   : false,                  // Display thumbnails on opening
  hideOnClose : true,                   // Hide thumbnail grid when closing animation starts
  parentEl    : '.fancybox-container',  // Container is injected into this element
  axis        : 'y'                     // Vertical (y) or horizontal (x) scrolling
},

share: {
  url: function(instance, item) {
    return (
      (!instance.currentHash &amp;&amp; !(item.type === "inline" || item.type === "html") ? item.origSrc || item.src : false) || window.location
    );
  },
  tpl:
    '&lt;div class=&quot;fancybox-share&quot;&gt;' +
    &quot;&lt;h1&gt;{{SHARE}}&lt;/h1&gt;&quot; +
    &quot;&lt;p&gt;&quot; +
    '&lt;a class=&quot;fancybox-share__button fancybox-share__button--fb&quot; href=&quot;https://www.facebook.com/sharer/sharer.php?u={{url}}&quot;&gt;' +
    '&lt;svg viewBox=&quot;0 0 512 512&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;&gt;&lt;path d=&quot;m287 456v-299c0-21 6-35 35-35h38v-63c-7-1-29-3-55-3-54 0-91 33-91 94v306m143-254h-205v72h196&quot; /&gt;&lt;/svg&gt;' +
    &quot;&lt;span&gt;Facebook&lt;/span&gt;&quot; +
    &quot;&lt;/a&gt;&quot; +
    '&lt;a class=&quot;fancybox-share__button fancybox-share__button--tw&quot; href=&quot;https://twitter.com/intent/tweet?url={{url}}&amp;text={{descr}}&quot;&gt;' +
    '&lt;svg viewBox=&quot;0 0 512 512&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;&gt;&lt;path d=&quot;m456 133c-14 7-31 11-47 13 17-10 30-27 37-46-15 10-34 16-52 20-61-62-157-7-141 75-68-3-129-35-169-85-22 37-11 86 26 109-13 0-26-4-37-9 0 39 28 72 65 80-12 3-25 4-37 2 10 33 41 57 77 57-42 30-77 38-122 34 170 111 378-32 359-208 16-11 30-25 41-42z&quot; /&gt;&lt;/svg&gt;' +
    &quot;&lt;span&gt;Twitter&lt;/span&gt;&quot; +
    &quot;&lt;/a&gt;&quot; +
    '&lt;a class=&quot;fancybox-share__button fancybox-share__button--pt&quot; href=&quot;https://www.pinterest.com/pin/create/button/?url={{url}}&amp;description={{descr}}&amp;media={{media}}&quot;&gt;' +
    '&lt;svg viewBox=&quot;0 0 512 512&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;&gt;&lt;path d=&quot;m265 56c-109 0-164 78-164 144 0 39 15 74 47 87 5 2 10 0 12-5l4-19c2-6 1-8-3-13-9-11-15-25-15-45 0-58 43-110 113-110 62 0 96 38 96 88 0 67-30 122-73 122-24 0-42-19-36-44 6-29 20-60 20-81 0-19-10-35-31-35-25 0-44 26-44 60 0 21 7 36 7 36l-30 125c-8 37-1 83 0 87 0 3 4 4 5 2 2-3 32-39 42-75l16-64c8 16 31 29 56 29 74 0 124-67 124-157 0-69-58-132-146-132z&quot; fill=&quot;#fff&quot;/&gt;&lt;/svg&gt;' +
    &quot;&lt;span&gt;Pinterest&lt;/span&gt;&quot; +
    &quot;&lt;/a&gt;&quot; +
    &quot;&lt;/p&gt;&quot; +
    '&lt;p&gt;&lt;input class=&quot;fancybox-share__input&quot; type=&quot;text&quot; value=&quot;{{url_raw}}&quot; /&gt;&lt;/p&gt;' +
    &quot;&lt;/div&gt;&quot;
}

</code></pre>

          <h5 class="mt-5">Couple of examples</h5>

          <p>
            Show thumbnails on start:
          </p>

          <pre><code>$('[data-fancybox="images"]').fancybox({
	thumbs : {
		autoStart : true
	}
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/xavNjq?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            Customize share url if displaying hidden video element:
          </p>

          <pre><code>$('[data-fancybox=&quot;test-share-url&quot;]').fancybox({
    buttons : ['share', 'close'],
    hash : false,
    share : {
        url : function( instance, item ) {
            if (item.type === 'inline' &amp;&amp; item.contentType === 'video') {
                return item.$content.find('source:first').attr('src');
            }

            return item.src;
        }
    }
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/MqNdqw?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            If you would inspect fancybox instance object, you would find that same keys ar captialized - these are
            references for each module object.
            Also, you would notice that fancybox uses common naming convention to prefix jQuery objects with <code>$</code>.
          </p>

          <p>
            This is how you, for example, can access thumbnail grid element:
          </p>

          <pre><code>$.fancybox.getInstance().Thumbs.$grid</code></pre>

          <p>
            This example shows how to call method that toggles thumbnails:
          </p>

          <pre><code>$.fancybox.getInstance().Thumbs.toggle();</code></pre>

          <p>
            List of available methods:
          </p>

          <pre><code>Thumbs.focus()
Thumbs.update();
Thumbs.hide();
Thumbs.show();
Thumbs.toggle();

FullScreen.request( elem );
FullScreen.exit();
FullScreen.toggle( elem );
FullScreen.isFullscreen();
FullScreen.enabled();

SlideShow.start();
SlideShow.stop();
SlideShow.toggle();
</code></pre>

          <p>
            If you wish to disable hash module, use this snippet (after including JS file):
          </p>

          <pre><code>$.fancybox.defaults.hash = false;</code></pre>

        </section>

        <section id="faq">

          <h2>FAQ</h2>

          <h3 id="faq-1">
            <a href="#faq-1">#1</a> Opening/closing causes fixed element to jump</h3>

          <p>
            Simply add <code>compensate-for-scrollbar</code> CSS class to your fixed positioned elements.
            Example of using Bootstrap navbar component:
          </p>

          <pre><code>&lt;nav class=&quot;navbar navbar-inverse navbar-fixed-top compensate-for-scrollbar&quot;&gt;
	&lt;div class=&quot;container&quot;&gt;
		..
	&lt;/div&gt;
&lt;/nav&gt;</code></pre>

          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/MqNdPa?editors=1000" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            The script measures width of the scrollbar and creates
            <code>compensate-for-scrollbar</code> CSS class that uses this value for
            <code>margin-right</code> property. Therefore, if your element has
            <code>width:100%</code>, you should positon it using
            <code>left</code> and
            <code>right</code> properties instead. Example:
          </p>

          <pre><code>.navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
}</code></pre>

          <h3 id="faq-2">
            <a href="#faq-2">#2</a> How to customize caption</h3>

          <p>
            You can use
            <code>caption</code> option that accepts a function and is called for each group element.
            Example of appending image download link:
          </p>

          <pre><code>$( '[data-fancybox=&quot;images&quot;]' ).fancybox({
    caption : function( instance, item ) {
        var caption = $(this).data('caption') || '';

        if ( item.type === 'image' ) {
            caption = (caption.length ? caption + '&lt;br /&gt;' : '') + '&lt;a href=&quot;' + item.src + '&quot;&gt;Download image&lt;/a&gt;' ;
        }

        return caption;
    }
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/OoKYrd?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            Add current image index and image count (the total number of images in the gallery) right in the caption:
          </p>

          <pre><code>$( '[data-fancybox=&quot;images&quot;]' ).fancybox({
    infobar : false,
    caption : function( instance, item ) {
        var caption = $(this).data('caption') || '';

        return ( caption.length ? caption + '&lt;br /&gt;' : '' ) + 'Image &lt;span data-fancybox-index&gt;&lt;/span&gt; of &lt;span data-fancybox-count&gt;&lt;/span&gt;';
    }
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/EeqzMW?editors=1010" target="_blank">View demo on CodePen</a>
          </p>

          <p>
            Inside
            <code>caption</code> method,
            <code>this</code> refers to the clicked element. Example of using different source for caption:
          </p>

          <pre><code>$( '[data-fancybox]' ).fancybox({
	caption : function( instance, item ) {
		return $(this).find('figcaption').html();
	}
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/aaerxw" target="_blank">View demo on CodePen</a>
          </p>

          <h3 id="faq-3">
            <a href="#faq-3">#3</a> How to create custom button in the toolbar</h3>

          <p>
            Example of creating reusable button:
          </p>

          <pre><code>// Create template for the button
$.fancybox.defaults.btnTpl.fb = '&lt;button data-fancybox-fb class=&quot;fancybox-button fancybox-button--fb&quot; title=&quot;Facebook&quot;&gt;' +
    '&lt;svg viewBox=&quot;0 0 24 24&quot;&gt;' +
        '&lt;path d=&quot;M22.676 0H1.324C.594 0 0 .593 0 1.324v21.352C0 23.408.593 24 1.324 24h11.494v-9.294h-3.13v-3.62h3.13V8.41c0-3.1 1.894-4.785 4.66-4.785 1.324 0 2.463.097 2.795.14v3.24h-1.92c-1.5 0-1.793.722-1.793 1.772v2.31h3.584l-.465 3.63h-3.12V24h6.115c.733 0 1.325-.592 1.325-1.324V1.324C24 .594 23.408 0 22.676 0&quot;/&gt;' +
    '&lt;/svg&gt;' +
'&lt;/button&gt;';

// Make button clickable using event delegation
$('body').on('click', '[data-fancybox-fb]', function() {
    window.open(&quot;https://www.facebook.com/sharer/sharer.php?u=&quot;+encodeURIComponent(window.location.href)+&quot;&amp;t=&quot;+encodeURIComponent(document.title), '','left=0,top=0,width=600,height=300,menubar=no,toolbar=no,resizable=yes,scrollbars=yes');
});

// Customize buttons
$( '[data-fancybox=&quot;images&quot;]' ).fancybox({
    buttons : [
        'fb',
        'close'
    ]
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/NLQVZQ" target="_blank">View demo on CodePen</a>
          </p>

          <h3 id="faq-4">
            <a href="#faq-4">#4</a> How to reposition thumbnail grid
          </h3>

          <p>
            There is currenty no JS option to change thumbnail grid position. But fancybox is designed so that you can
            use CSS to change position or dimension for each block (e.g., content area, caption or thumbnail grid).
            This gives you freedom to completely change the look and feel of the modal window, if needed.

            <a href="https://codepen.io/fancyapps/pen/PdMvML" target="_blank">View demo on CodePen</a>
          </p>

          <h3 id="faq-5">
            <a href="#faq-5">#5</a> How to disable touch gestures/swiping
          </h3>

          <p>
            When you want to make your content selectable or clickable, you have two options:
          </p>

          <ul>
            <li>
              disable touch gestures completely by setting
              <code>touch:false</code>
            </li>
            <li>
              add
              <code>data-selectable="true"</code> attribute to your html element
            </li>
          </ul>

          <p>
            <a href="https://codepen.io/fancyapps/pen/NLQZWK?editors=1000" target="_blank">View demo on CodePen</a>
          </p>

          <h3 id="faq-6">
            <a href="#faq-6">#6</a> Slider/carousel add's cloned duplicate items
          </h3>

          <p>
            If you are combining fancybox with slider/carousel script and that script clones items to enable infinite
            navigation, then duplicated items will appear in the gallery.
            To avoid that -
            1) initialise fancybox on all items except cloned;
            2) add custom click event on cloned items and trigger click event on corresponding "real" item. Here is an
            example using Slick slider:
          </p>
          <pre><code>// Init fancybox
// =============
var selector = '.slick-slide:not(.slick-cloned)';

// Skip cloned elements
$().fancybox({
  selector : selector,
  backFocus : false
});

// Attach custom click event on cloned elements, 
// trigger click event on corresponding link
$(document).on('click', '.slick-cloned', function(e) {
  $(selector)
    .eq( ( $(e.currentTarget).attr("data-slick-index") || 0) % $(selector).length )
    .trigger("click.fb-start", {
      $trigger: $(this)
    });

  return false;
});

// Init Slick
// ==========
$(".main-slider").slick({
  slidesToShow   : 3,
  infinite : true,
  dots     : false,
  arrows   : false
});</code></pre>
          <p class="demo">
            <a href="https://codepen.io/fancyapps/pen/NLQZBr?editors=1010" target="_blank">View demo on CodePen</a>
          </p>


        </section>

        <section>
          <p class="text-right mt-5">
            <a href="#introduction">Back to Top</a>
          </p>
        </section>

      </div>
    </div>
  </div>

  <script>

    $(document).ready(function () {

      /* Sticky nvigation */

      var sticky = {
        $sticky: $('.sticky'),
        offsets: [],
        targets: [],
        stickyTop: null,

        set: function () {
          var self = this;

          var windowTop = Math.floor($(window).scrollTop());

          self.offsets = [];
          self.targets = [];

          // Get current top position of sticky element
          self.stickyTop = self.$sticky.css('position', 'relative').offset().top;

          // Cache all targets and their top positions
          self.$sticky.find('a').map(function () {
            var $el = $(this),
              href = $el.data('target') || $el.attr('href'),
              $href = /^#./.test(href) && $(href);

            return $href && $href.length && $href.is(':visible') ? [[$href[0].getBoundingClientRect().top + windowTop, href]] : null;
          })
            .sort(function (a, b) { return a[0] - b[0] })
            .each(function () {
              self.offsets.push(this[0]);
              self.targets.push(this[1]);
            });
        },

        update: function () {
          var self = this;

          var windowTop = Math.floor($(window).scrollTop());
          var $stickyLinks = self.$sticky.find('a').removeClass('active');
          var stickyPosition = 'fixed';
          var currentIndex = 0;

          // Toggle fixed position depending on visibility
          if ($(window).width() < 800 || $(window).height() < 500 || self.stickyTop > windowTop) {
            stickyPosition = 'relative';

          } else {

            for (var i = self.offsets.length; i--;) {
              if (windowTop >= self.offsets[i] - 2 && (self.offsets[i + 1] === undefined || windowTop <= self.offsets[i + 1] + 2)) {
                currentIndex = i;

                break;
              }
            }

          }

          self.$sticky.css('position', stickyPosition).width($('#leftCol').width());

          $stickyLinks.eq(currentIndex).addClass('active');
        },

        init: function () {
          var self = this;

          $(window).on('resize', function () {
            self.set();

            self.update();
          });

          $(window).on('scroll', function () {
            self.update();
          });

          $(window).trigger('resize');
        }
      }

      sticky.init();
    });
  </script>
</body>

</html>

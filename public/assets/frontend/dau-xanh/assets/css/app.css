@charset "UTF-8";
body {
    overflow-x: hidden;
}

html {
    overflow-x: hidden;
}

::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-thumb {
    background-color: #aaa;
}

::-webkit-scrollbar-track {
    background-color: #f1f1f1;
}
.grecaptcha-badge {
    visibility: hidden !important;
}
@font-face {
    font-family: "baloo2";
    font-weight: 900;
    src: url("../fonts/baloo2-extrabold.ttf") format("truetype");
}

@font-face {
    font-family: "baloo2";
    font-weight: 700;
    src: url("../fonts/baloo2-bold.ttf") format("truetype");
}

@font-face {
    font-family: "baloo2";
    font-weight: 500;
    src: url("../fonts/baloo2-medium.ttf") format("truetype");
}

@font-face {
    font-family: "baloo2";
    font-weight: 200;
    src: url("../fonts/baloo2-regular.ttf") format("truetype");
}

@font-face {
    font-family: "nunito";
    font-weight: 900;
    src: url("../fonts/nunito-extrabold.ttf") format("truetype");
}


@font-face {
    font-family: "nunito";
    font-weight: 700;
    src: url("../fonts/nunito-bold.ttf") format("truetype");
}

@font-face {
    font-family: "nunito";
    font-weight: 500;
    src: url("../fonts/nunito-medium.ttf") format("truetype");
}

@font-face {
    font-family: "nunito";
    font-weight: 300;
    src: url("../fonts/nunito-black.ttf") format("truetype");
}

@font-face {
    font-family: "nunito";
    font-weight: 200;
    src: url("../fonts/nunito-regular.ttf") format("truetype");
}


@font-face {
    font-family: "OpenSans";
    font-weight: 900;
    src: url("../fonts/OpenSans-ExtraBold.ttf") format("truetype");
}


@font-face {
    font-family: "OpenSans";
    font-weight: 700;
    src: url("../fonts/OpenSans-Bold.ttf") format("truetype");
}

@font-face {
    font-family: "OpenSans";
    font-weight: 500;
    src: url("../fonts/OpenSans-Medium.ttf") format("truetype");
}

@font-face {
    font-family: "OpenSans";
    font-weight: 300;
    src: url("../fonts/OpenSans-Regular.ttf") format("truetype");
}

:root {
    --primary-color: rgb(5, 147, 216);
    --font-nunito: "nunito";
    --font-baloo2: "baloo2";
    --font-opensan: "OpenSans";
    --primary-color2: rgb(255, 156, 32);
    --primary-color3: rgb(28, 0, 194);
}

body {
    font-family: "OpenSans";
    font-weight: 300;
}

a {
    text-decoration: none;
}

textarea:focus, input[type="text"]:focus, input[type="password"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="date"]:focus, input[type="month"]:focus, input[type="time"]:focus, input[type="week"]:focus, input[type="number"]:focus, input[type="email"]:focus, input[type="url"]:focus, input[type="search"]:focus, input[type="tel"]:focus, input[type="color"]:focus, .uneditable-input:focus {
    outline: 0 none !important;
    -webkit-box-shadow: inset 0 -1px 0 #ddd !important;
    box-shadow: inset 0 -1px 0 #ddd !important;
}

ul, li {
    margin-bottom: 0px !important;
}

.row-0 {
    padding: 0px 15px !important;
}

.pd-0 {
    padding: 0px !important;
}

.row-5 {
    padding: 0px 5px !important;
}

.pd-5 {
    padding: 0px 5px 10px 5px !important;
}

.row-15 {
    padding: 0px 7.5px !important;
}

.pd-15 {
    padding: 0px 7.5px 15px 7.5px !important;
}

.row-10 {
    padding: 0px 10px !important;
}

.pd-10 {
    padding: 0px 10px 20px 10px !important;
}

/**header**/
#header {
    background: var(--primary-color);
    width: 100%;
    padding: 5px 0;
}

.is-sticky {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 999;
}

#logo {
    width: 117px;
}

#logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

#header .user-info {
    margin-top: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 5px;
    cursor: pointer;
    min-width: 136px;
    min-height: 43px;

    border-radius: 9999px;
    border: 1px solid #fff;
    background: linear-gradient(270deg, #ffe6c3 0%, #fff 70.1%);
    box-shadow: 0px -1px 11px 0px rgba(255, 255, 255, 0.5) inset;
    padding: 2px 8px 5px 21px;
}

#header .user-info-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

#header .user-name {
    font-weight: 600;

    color: #333;
    font-variant-numeric: lining-nums proportional-nums;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px;
}

#header .user-badge {
    display: flex;
    align-items: center;
    padding: 2px 6px 2px 4px;
    gap: 1px;
    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;

    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    line-height: 14px;
}

#header .user-badge img {
    width: 11px;
    height: 11px;
}

#header .Userprofile .dropmenu-end {
    width: fit-content;
    border-radius: 12px;
}

#header .free-badge {
    border-radius: 5px;
    border: 1px solid #89b3ff;
    background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

#header .pro-badge {
    border-radius: 5px;
    border: 1px solid #ffa688;
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
}

#header .dropdown-toggle::after {
    margin-left: 8px;
}

#header .user-profile {
    display: flex;
    align-items: center;
    gap: 10px;
}

/**Menu**/
div#main-menu ul {
    display: flex;
    margin: 0;
    justify-content: space-between;
}

div#main-menu ul li {
    list-style: none;
    position: relative;
}

div#main-menu ul li a {
    color: #fff;
    font-weight: 700;
    transition: 0.3s ease-in-out;
    padding: 6px 15px;
    border-radius: 99px;
    font-family: var(--font-nunito);
    font-size: 18px;
    line-height: 24px;
}

div#main-menu ul li a:hover {
    transition: 0.3s ease-in-out;
    transform: scale(1) rotate(0deg) !important;
    filter: blur(0px) grayscale(0%) !important;
    color: var(--primary-color);
    background: #fff;
}

.header-right {
    display: flex;
    gap: 15px;
    align-items: center;
    justify-content: end;
}

.header-right a.btn-register {
    width: 45px;
    height: 45px;
    border: 1px solid rgb(255, 255, 255);
    border-radius: 48px;
    background-image: linear-gradient(90deg, rgb(254, 222, 30), rgb(236, 47, 5));
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-right a.btn-register svg {
    width: 25px;
    height: 27px;
}

.header-right .btn-login {
    font-family: var(--font-nunito);
    background: linear-gradient(90deg, rgb(255, 255, 255), rgb(255, 182, 78));
    display: inline-block;
    height: 45px;
    font-weight: bold;
    line-height: 45px;
    color: #000;
    font-size: 18px;
    border-radius: 99px;
    padding: 0 30px;
    border-width: 2px;
    border-style: solid;
    border-color: rgb(255, 255, 255);
}

.header-right .btn-login:hover {
    background: var(--primary-color2) !important;
    color: #fff;
}

/**footer**/
#footer {
    background-image: linear-gradient(360deg, rgb(1, 150, 216), rgb(1, 133, 197));
    padding: 40px 0;
}

#footer .footer-top {
    width: 100%;
}

#footer .footer-logo img {
    width: 207px;
}

#footer h3.company {
    font-size: 28px;
    color: rgb(255, 255, 255);
    font-family: var(--font-baloo2);
    font-weight: bold;
    margin: 10px 0 0 0;
}

#footer .footer-address {
    margin: 20px 0 0 0;
}

#footer .footer-address a {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #fff;
    font-size: 14px;
    line-height: 1.4;
}

#footer .footer-address a svg {
    width: 22px;
}

#footer .footer-social {
    margin: 20px 0 0 0;
    display: flex;
    gap: 5px;
}

#footer .footer-social a svg {
    width: 52.5922px;
    height: 55.6466px;
}

#footer .footer-dmca {
    background-color: rgb(234, 242, 254);
    border-radius: 4px;
    margin: 20px 0 0 0;
    padding: 8px;
    display: inline-block;
}

#footer .footer-dmca img {
    width: 104.604px;
}

.mt-footer {
    margin-bottom: 20px;
}

#footer .footer-copyright {
    border-top: 1px solid #fff;
    color: #fff;
    font-size: 13px;
    padding: 10px 0 0 0;
    margin: 20px 0 0 0;
}

.section {
    padding: 30px 0;
    position: relative;
}

/*home-title*/
.home-title {
    position: relative;
    margin-bottom: 70px;
}

.home-title h3 {
    color: var(--primary-color);
    font-family: var(--font-baloo2);
    font-weight: bold;
    font-size: 38px;
    line-height: 44px;
    margin: 0 0 5px 0;
}

.home-title .sub_title {
    color: var(--primary-color3);
    font-family: var(--font-baloo2);
    font-weight: bold;
    font-size: 38px;
    line-height: 44px

}

.text-uppercase {
    text-transform: uppercase;
}

.home-title:after {
    content: '';
    background: url('../images/line-vang.png') no-repeat;
    width: 165px;
    height: 13px;
    background-size: 100%;
    background-position: center;
    display: block;
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
}

.not-line:after {
    display: none;
}

.not-line {
    margin-bottom: 30px;
}

.font-42 {
    font-size: 42px !important;
    line-height: 46px !important;
}

.home-title2 {
    border-width: 2px;
    border-radius: 0px 35px;
    border-style: solid;
    border-color: rgb(163, 216, 241);
    background-color: rgb(255, 255, 255);
    box-shadow: rgb(163, 216, 241) -6px 3px 0px 0px;
    display: inline-block;
    padding: 10px 45px;
    margin: 0 0 30px 0;
}

.home-title2 h3 {
    margin: 0;
    font-weight: bold;
    line-height: 1.6;
    color: rgb(1, 116, 172);
    font-size: 20px;
    font-family: var(--font-baloo2);
}

/**faq**/
.sec-faq {
    padding: 50px 0;
}

.faq-accordion .accordion-item {
    border: 0 !important;
    border-radius: 0px 0px 16px 16px;
    background-color: rgb(248, 248, 248);
}

.faq-accordion .accordion-item .accordion-header button {
    border: 0 !important;
    border-radius: 17px !important;
    color: #fff;
    background: var(--primary-color) !important;
    font-family: var(--font-baloo2);
    font-weight: bold;
    font-size: 18px;
}

.faq-accordion .accordion-item:not(:last-child) {
    margin: 0 0 15px 0;
}

.faq-accordion .accordion-item .accordion-body {
    padding: 40px;
}

.faq-accordion .accordion-item .accordion-body ul {
    margin: 0 !important;
    padding: 0;
}

.faq-accordion .accordion-item .accordion-body ul li {
    list-style: none;
    margin: 0 0 5px 0 !important;
}

.faq-accordion .accordion-button::after {
    content: '';
    background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' viewBox='0 0 16 16'><path d='M8 1v14M1 8h14' stroke='white' stroke-width='2'/></svg>");
    background-repeat: no-repeat;
    background-size: 1em;
    width: 1em;
    height: 1em;
    transition: transform 0.3s;
}

.faq-accordion .accordion-button:not(.collapsed)::after {
    content: '';
    background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' viewBox='0 0 16 16'><path d='M1 8h14' stroke='white' stroke-width='2'/></svg>");
}

/**sec-register**/
.sec-register {
    background: var(--primary-color);
    padding: 40px 0;
}

.register-left {
    width: 90%;
    float: right;
    margin-bottom: 80px;
    display: block;
    position: relative;
}

.register-left:after {
    content: '';
    width: 351px;
    height: 144px;
    background: url(../images/line-dau-xanh.png) no-repeat;
    display: block;
    background-size: 100% !important;
    position: absolute;
    transform: rotate(50deg);
    bottom: 68px;
    left: -120px;
}

.register-left .register-dx {
    width: 336.42px;
    height: 257.99px;
    transform: perspective(1000px) rotate(-3deg) rotateY(180deg);
    margin: 20px auto 0;
}

.register-left .register-dx img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.thumb-cloud {
    position: absolute;
    left: 200px;
    width: 526px;
    height: 101px;
    bottom: 0;
}

.thumb-cloud svg {
    width: 100%;
}

.sec-register .register-desc {
    background: rgb(254, 222, 30);
    border: 2px solid #fff;
    border-radius: 22px;
    padding: 20px;
    font-size: 16px;
    line-height: 24px;
}

.register-left .home-title h3 {
    font-size: 38px !important;
}

.thumb-computer {
    transform: rotate(1deg);
    position: absolute;
    top: 128px;
    left: 120px;
}

.thumb-computer img {
    width: 56px;
    height: 67px;
}

.thumb-clock {
    position: absolute;
    top: 317px;
    right: 50px;
    transform: rotate(-24deg);
}

.thumb-clock img {
    width: 94px;
    height: 126px;
}

.box-register {
    background: rgb(232, 245, 255);
    border-radius: 30px;
    padding: 25px;
    border: 3px solid rgb(0, 121, 245);
    width: 90%;
    margin: auto;
}

.box-register .box-register-head p {
    margin: 0 0 10px 0;
}

.box-register .box-register-head {
    background-color: rgb(0, 132, 197);
    border-radius: 30px;
    text-align: center;
    padding: 20px;
    color: #fff;
    font-family: var(--font-nunito);
}

.box-register .box-register-head p ins {
    color: rgb(255, 228, 1);
    font-size: 25px;
}

.box-register .box-register-head p.text-uppercase {
    font-weight: 700;
    font-size: 20px;
    line-height: 26px;
    font-family: var(--font-nunito);
}

.box-register .box-register-head p {
    font-size: 20px;
    line-height: 26px;
}

.box-register .box-register-head p.text-small {
    font-size: 18px !important;
    font-weight: normal;
}

.box-register .box-register-content {
    border-radius: 30px;
    background: rgb(162, 225, 253);
}

.box-register .box-register-body {
    padding: 30px;
    font-family: var(--font-nunito);
}

.box-register .box-register-body h3 {
    font-size: 25px;
    font-weight: bold;
    line-height: 1.6;
    color: rgb(17, 17, 17);
    text-align: center;
}

.box-register .register-code {
    border: 2px solid rgb(72, 188, 244);
    position: relative;
    background: #fff;
    border-radius: 10px;
    padding: 10px;
    margin-top: 30px;
}

.box-register .register-head {
    position: absolute;
    left: -30px;
    top: -30px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1536 1896.0833" class="" fill="url(&quot;#SHAPE117_desktop_gradient&quot;)"><defs id="SHAPE117_defs"><linearGradient id="SHAPE117_desktop_gradient" gradientTransform="rotate(0)"><stop offset="0%" stop-color="rgba(255, 80, 26, 1.0)"></stop><stop offset="100%" stop-color="rgba(255, 219, 1, 1)"></stop></linearGradient></defs> <path d="M1376 896l138 135q30 28 20 70-12 41-52 51l-188 48 53 186q12 41-19 70-29 31-70 19l-186-53-48 188q-10 40-51 52-12 2-19 2-31 0-51-22l-135-138-135 138q-28 30-70 20-41-11-51-52l-48-188-186 53q-41 12-70-19-31-29-19-70l53-186-188-48q-40-10-52-51-10-42 20-70l138-135L22 761q-30-28-20-70 12-41 52-51l188-48-53-186q-12-41 19-70 29-31 70-19l186 53 48-188q10-41 51-51 41-12 70 19l135 139 135-139q29-30 70-19 41 10 51 51l48 188 186-53q41-12 70 19 31 29 19 70l-53 186 188 48q40 10 52 51 10 42-20 70z"></path> </svg>');
    background-repeat: no-repeat;
    animation-name: tada;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

.box-register .register-head svg {
    width: 74.9937px;
    height: 64.2002px;
}

.box-register .register-head .register-head-bg {
    position: relative;
}

.box-register .register-head .register-head-text {
    font-size: 12px;
    font-weight: bold;
    font-style: italic;
    line-height: 1.2;
    color: rgb(255, 255, 255);
    text-align: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
}

.box-register .register-copy {
    display: flex;
    justify-content: end;
    align-items: center;
}

.box-register .register-copy .register-button {
    border-radius: 5px;
    background: rgb(66, 165, 245);
    border: 1px solid rgb(141, 219, 255);
    font-size: 16px;
    color: #fff;
    font-weight: normal;
    padding: 5px 15px;
    margin-top: 0;
    cursor: pointer;
}

.box-register .register-text {
    width: calc(100% - 150px);
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    background-image: linear-gradient(rgb(255, 1, 1), rgb(255, 143, 1));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.register-countdown div {
    color: rgb(197, 29, 29);
    font-size: 50px;
    font-weight: 500;
}

.register-countdown {
    border-radius: 200px;
    border: 2px solid rgb(255, 219, 1);
    background-image: linear-gradient(rgb(255, 245, 173), rgb(255, 222, 40));
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin: 20px 0 0;
    padding: 10px 0;
}

@keyframes tada {
    0% {
        -webkit-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
    }
    10%, 20% {
        -webkit-transform: scale(0.9) rotate(-3deg);
        -ms-transform: scale(0.9) rotate(-3deg);
        transform: scale(0.9) rotate(-3deg);
    }
    30%, 50%, 70%, 90% {
        -webkit-transform: scale(1.1) rotate(3deg);
        -ms-transform: scale(1.1) rotate(3deg);
        transform: scale(1.1) rotate(3deg);
    }
    40%, 60%, 80% {
        -webkit-transform: scale(1.1) rotate(-3deg);
        -ms-transform: scale(1.1) rotate(-3deg);
        transform: scale(1.1) rotate(-3deg);
    }
    100% {
        -webkit-transform: scale(1) rotate(0);
        -ms-transform: scale(1) rotate(0);
        transform: scale(1) rotate(0);
    }
}

.box-register .register-button {
    display: flex;
    gap: 10px;
    margin: 20px 0 0 0;
}

.box-register .register-link {
    width: 100%;
}

.box-register .register-link a {
    background-image: linear-gradient(90deg, rgb(254, 222, 30), rgb(255, 191, 1));
    border-radius: 99px;
    border: 2px solid rgb(255, 225, 38);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    z-index: 99;
}

.box-register .register-link a:before {
    position: absolute;
    top: 0;
    left: -75%;
    z-index: 2;
    display: block;
    content: '';
    width: 50%;
    height: 100%;
    background: -webkit-gradient(linear, left top, right top, from(rgba(255, 255, 255, 0)), to(rgba(255, 255, 255, 0.3)));
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.3) 100%);
    -webkit-transform: skewX(-25deg);
    transform: skewX(-25deg);
    animation: shine-ani 3s infinite;
}

@keyframes trail-ani {
    0% {
        --angle: 0deg;
    }

    100% {
        --angle: 360deg;
    }
}

@keyframes shine-ani {
    0% {
        left: -20%;
    }

    100%, 70%, 80%, 90% {
        left: 120%;
    }
}

.box-register .register-link .register-link-icon {
    display: flex;
    margin-left: -2px;
    background-image: linear-gradient(90deg, rgb(254, 222, 30), rgb(255, 155, 1));
    width: 61px;
    height: 61px;
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    border: 2px solid rgb(255, 225, 38);
}

.box-register .register-link .register-link-icon img {
    width: 30px;
}

.box-register .register-link .register-link-title {
    padding-left: 10px;
    color: #000;
    text-transform: uppercase;
    font-size: 15px;
    font-weight: 700;
    font-family: var(--font-nunito);
}

.box-register .register-link.primary a {
    border: 2px solid rgb(141, 219, 255);
    background-image: linear-gradient(90deg, rgb(1, 107, 255), rgb(37, 1, 249));
}

.box-register .register-link.primary .register-link-icon {
    background-image: linear-gradient(90deg, rgb(1, 173, 255), rgb(28, 0, 194));
    border: 2px solid rgb(141, 219, 255);
}

.box-register .register-link.primary .register-link-title {
    color: #fff;
}

/**sec author**/
.sec-author {
    padding: 40px 0;
}

.font-46 {
    font-size: 46px !important;
    line-height: 52px !important;
}

.box-author .author-thumbnail {
    width: 100%;
    border-radius: 65px;
    height: 354.2px;
    position: relative;
    z-index: 2;
}

.box-author .author-thumbnail img {
    width: 100%;
    border-radius: 65px;
    height: 100%;
    object-fit: cover;
}

.box-author .author-left {
    position: relative;
}

.box-author .author-left:after {
    content: '';
    border-radius: 30px;
    background: rgb(6, 148, 216);
    display: block;
    width: 100%;
    height: 200px;
    position: absolute;
    bottom: -11px;
    z-index: -1;
}

.box-author .author-content {
    background: #fff;
    border: 2px solid rgb(6, 148, 216);
    border-radius: 0 30px 30px 0;
    padding: 50px 50px 50px 210px;
    z-index: 0;
    bottom: 0;
    color: rgb(28, 24, 158);
    font-size: 17px;
    line-height: 28px;
    margin-left: -200px;
}

.box-author .author-cloud {
    position: relative;
}

.box-author .author-cloud svg {
    width: 231.568px;
    height: 46.714px;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.box-author .author-cloud img {
    width: 177.51px;
}

.mt-100 {
    margin-top: 100px;
}

.author-content.author-right {
    border-radius: 30px 0 0 30px;
    margin: 0 -200px 0 0;
    padding: 50px 210px 50px 80px;
}

.author-social {
    width: auto;
    background-image: linear-gradient(rgb(6, 148, 216), rgb(6, 148, 216));
    border-radius: 30px !important;
    align-items: start;
    justify-content: center;
    gap: 30px;
    padding: 10px 20px;
    margin: 20px auto 0;
    display: inline-flex
}


.author-social .social-item a {
    display: flex;
    align-items: center;
    gap: 10px;
}

.author-social .social-item svg {
    width: 45px;
    height: 56px;
}

.author-social .social-content h3 {
    color: #fff;
    font-size: 18px;
    line-height: 24px;
    font-family: var(--font-nunito);
    text-align: left;
}

.author-cloud.text-right img {
    float: right;
}

/**review**/
.sec-review {
    background: rgb(6, 148, 216);
    padding: 40px 0 70px 0;
}

.top-review {
    position: relative;
    margin-top: 70px;
}

.top-review .top-review-images img {
    width: 100%;
}

.top-review .top-review-author {
    position: relative;
    z-index: 9;
}

.top-review .top-review-author img {
    width: 85%;
    transform: perspective(1000px) rotateY(180deg);
    position: relative;
    z-index: 9;
    float: right;
}

.top-review .top-review-author .top-review-info {
    background-image: linear-gradient(rgb(255, 255, 255), rgb(237, 246, 255));
    border-radius: 43px;
    border: 2px solid rgb(183, 226, 255);
    position: absolute;
    box-shadow: rgb(1, 116, 172) 7px 6px 0px 0px;
    padding: 20px 55px;
    font-size: 23px;
    color: rgb(1, 112, 166);
    font-weight: 700;
    font-family: var(--font-baloo2);
    right: -220px;
    top: -90px;
    z-index: 0;
}

.top-review .top-review-author-info {
    padding-left: 260px;
}

.top-review-author-info p {
    font-size: 16px;
    color: #fff;
    margin: 0;
}

.top-review-author-info h4 {
    font-size: 18px;
    font-weight: bold;
    line-height: 1.6;
    color: rgb(255, 255, 255);
    margin: 0;
}

.review-body {
    margin: 30px 0;
}

.review-item .review-author {
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    height: 85px;
}

.review-item .review-images img {
    width: 100%;
    border-radius: 20px;
    height: 520px;
}

.review-item .review-author p {
    font-size: 14px;
    color: #fff;
    margin: 0;
}

.review-item .review-author h4 {
    font-size: 16px;
    font-weight: bold;
    line-height: 1.6;
    color: rgb(255, 255, 255);
    margin: 0;
}

.review-item .review-desktop {
    background: rgb(255, 143, 1);
    border-radius: 20px;
    padding: 10px;
    overflow: hidden;
    width: 350px;
    margin-left: -80px;
}

.review-item .review-desktop img {
    width: 100%;
    height: 260px;
    object-fit: cover;
    object-position: center !important;
}

.review-slide {
    margin-top: 50px;
}

.sd-item .sd-images img {
    border: 2px solid rgb(255, 255, 255);
    border-radius: 20px;
    width: 100%;
    height: 538px;
    object-fit: cover;
    object-position: top;
}

.sd-item .sd-author {
    margin: 20px 0 0 0;
    text-align: center;
}

.sd-item .sd-author p {
    font-size: 15px;
    color: #fff;
    margin: 0;
}

.sd-item .sd-author h4 {
    font-size: 18px;
    font-weight: bold;
    line-height: 1.6;
    color: rgb(255, 255, 255);
    margin: 0;
}

/**slide**/
.swiper-container {
    position: relative;
}

body .swiper-button-next, body .swiper-button-prev {
    width: 30px;
    height: 30px;
    border: none;
    outline: 0;
    margin: 0;
    background-color: #1c189e;
    background-repeat: no-repeat;
    background-position: center;
    -webkit-transition: .3s;
    transition: .3s;
    transform: translateY(-50%) !important;
    top: 50% !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, .1);
    border-radius: 50% !important;
    color: #fff
}

body .swiper-button-next.swiper-button-disabled, body .swiper-button-prev.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none
}

body .swiper-button-next:after, body .swiper-button-prev:after {
    content: '' !important;
    font-size: 15px;
    color: #000;
    display: none !important
}

body .swiper-button-next {
    right: 10px
}

body .swiper-button-prev {
    left: 10px
}

.sec-down {
    width: 75px;
    height: 75px;
    background: #fff;
    border-radius: 50%;
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    z-index: 999;
}

.sec-down svg {
    fill: rgb(1, 133, 197);
    width: 73.0112px;
    height: 40.0831px;
    animation-name: fadeInDown;
    animation-delay: 0s;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translateY(-20px);
        -ms-transform: translateY(-20px);
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        transform: translateY(0);
    }
}

/**course**/
.sec-course {
    background: rgb(237, 246, 255);
    padding: 45px 0;
}

.register-2 {
    padding: 0;
    border: 0;
    border-radius: 16px;
}

.register-2 .box-register-content {
    border-radius: 16px;
    background: rgb(215, 238, 255);
}

.box-register.register-2 .box-register-head {
    border-radius: 16px;
    padding: 15px;
}

.box-register.register-2 .box-register-head p:last-child {
    margin: 0;
}

.box-register.register-2 .box-register-head p ins {
    color: rgb(255, 228, 1);
    text-decoration: line-through;
    text-transform: uppercase;
    font-size: 30px;
    font-family: "OpenSans" !important;
    font-weight: 700;
}

.flex-text {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: center;
}

.register-3 {
    background-image: linear-gradient(rgb(131, 216, 255), rgb(182, 232, 255)) !important;
    border-radius: 0 0 16px 16px !important;
    position: relative;
    margin: 20px 0 0 0;
}

.register-2 .register-countdown {
    border-color: rgb(255, 222, 169) !important;
    background: rgb(255, 243, 224) !important;
}

.register-2 .box-register-heading {
    font-size: 19px;
    font-weight: bold;
    line-height: 1.6;
    color: rgb(0, 0, 0);
    font-family: var(--font-nunito);
    border-bottom: 3px dashed rgb(6, 148, 216);
    padding: 10px 10px 10px 38px;
    position: relative;
}

.register-2 .box-register-heading:before {
    content: '';
    width: 20px;
    height: 20px;
    top: 50%;
    transform: translateY(-50%);
    background-image: url(../images/gift.png);
    position: absolute;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    left: 10px;
}

.register-3 .heading-bookmark {
    position: absolute;
    top: -10px;
    right: 5px;
}

.register-3 .heading-bookmark svg {
    width: 41.2462px;
    height: 56.255px;
}

.code-heading {
    font-size: 18px;
    font-weight: normal;
}

.register-3 .box-register-body, .register-2 .box-register-body {
    padding-top: 20px;
}

.register-down {
    border-radius: 200px;
    border: 2px solid rgb(255, 219, 1);
    background-image: linear-gradient(rgb(255, 245, 173), rgb(255, 222, 40));
    text-align: center;
    gap: 30px;
    margin: 20px 0 0;
    padding: 10px 0;
}

/**method**/
.sec-method {
    background: rgb(177, 231, 255);
    padding: 30px 0 70px 0;
}

nav.tab-method .nav-tabs {
    display: flex;
    justify-content: center;
    gap: 3px;
    border: 0;
    margin: 0 !important;
}

nav.tab-method button {
    transform: scale(1) !important;
    padding: 15px 35px;
    background-image: linear-gradient(rgb(6, 148, 216), rgb(82, 196, 251)) !important;
    color: #fff;
    border: 2px solid rgb(234, 242, 254) !important;
    border-radius: 10px !important;
    font-size: 24px;
    text-shadow: rgb(1, 112, 166) 1px 1px 0px;
    font-weight: bold;
    line-height: 1.6;
    font-family: var(--font-baloo2);
}

nav.tab-method button.active,
nav.tab-method button:hover {
    background-color: rgb(6, 148, 216) !important;
    border-color: rgb(6, 148, 216) !important;
    color: rgb(255, 225, 38) !important;
}

.tab-method-panel {
    background-color: rgb(255, 255, 255);
    border-radius: 10px 10px 30px 30px;
    box-shadow: rgb(6, 148, 216) 5px 5px 0px 0px;
    border: 0
}

.tab-method-panel img {
    width: 100%;
}

.tab-method-panel .method-body h3 {
    font-size: 32px;
    font-weight: bold;
    font-family: var(--font-nunito);
    color: #000;
    text-transform: uppercase;
}

.tab-method-panel .method-body {
    padding: 30px 40px;
}

.tab-method-panel .method-text p {
    margin: 0;
    line-height: 24px;
    font-size: 16px;
}

.tab-method-panel .method-text {
    background: rgb(237, 246, 255);
    padding: 30px 40px;
}

.document-item {
    border: 2px solid rgb(6, 148, 216);
    border-radius: 16px;
    padding: 30px 35px 30px 70px;
    position: relative;
    width: calc(100% - 60px);
    float: right;
    margin-bottom: 30px;
}

.document-item .document-icon svg {
    width: 64.2397px;
    height: 64.026px;
}

.document-item .document-icon {
    width: 118px;
    height: 118px;
    background: rgb(6, 148, 216);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: -59px;
}

.document-item .document-icon i {
    color: rgb(255, 225, 38);
    font-size: 50px;
}

.document-item .document-text p {
    margin: 0;
    font-size: 16px;
}

.document-item .document-text h4 {
    color: var(--primary-color);
    font-size: 20px;
    font-weight: bold;
    font-family: 'baloo2';
}

.document-dx {
    text-align: right;
    position: relative;
}

.document-dx img {
    width: 221.841px;
    height: 291.307px;
}

.document-dx {
    position: relative;
    width: 100%;
    display: inline-block;
}

.document-dx .register-link {
    position: absolute;
    top: 50%;
    right: 200px;
    transform: translateY(-50%);
}

.document-dx .document-muiten {
    width: 80px;
    height: 80px;
    transform: perspective(1000px) rotate(-159deg) rotateX(180deg);
    position: absolute;
    right: 150px;
    top: 50%;
    z-index: -1;
}

.dx .register-link a {
    background-image: linear-gradient(90deg, rgb(254, 222, 30), rgb(255, 191, 1));
    border-radius: 99px;
    border: 2px solid rgb(255, 225, 38);
    display: flex;
    align-items: center;
    padding-right: 20px;
    animation-name: pulse;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

.dx .register-link .register-link-icon {
    display: flex;
    margin-left: -2px;
    background-image: linear-gradient(90deg, rgb(254, 222, 30), rgb(255, 155, 1));
    width: 61px;
    height: 61px;
    border-radius: 50%;
    align-items: center;
    justify-content: center;
    border: 2px solid rgb(255, 225, 38);
}

.dx .register-link .register-link-icon img {
    width: 30px;
    height: 30px;
}

.dx .register-link .register-link-title {
    padding-left: 15px;
    background-image: linear-gradient(rgb(255, 92, 1), rgb(218, 1, 1));
    background-color: initial;
    background-size: initial;
    background-origin: initial;
    background-position: initial;
    background-repeat: initial;
    background-attachment: initial;
    font-weight: bold;
    line-height: 1.6;
    color: rgb(224, 41, 41);
    text-align: center;
    -webkit-background-clip: text;
    filter: drop-shadow(rgb(255, 255, 255) 1px 1px 0px);
    -webkit-text-fill-color: transparent;
    text-transform: uppercase;
    font-size: 26px;
    font-family: var(--font-nunito);
}

@keyframes pulse {
    0% {
        -webkit-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
    }

    50% {
        -webkit-transform: scale(1.1);
        -ms-transform: scale(1.1);
        transform: scale(1.1);
    }
    100% {
        -webkit-transform: scale(1);
        -ms-transform: scale(1);
        transform: scale(1);
    }
}

/*8sec-whom**/
.sec-whom {
    background-image: linear-gradient(rgb(1, 133, 197), rgb(6, 148, 216));
    padding: 40px 0;
}

.sec-whom .whom-a img {
    width: 100%;
}

.sec-whom .whom-heading {
    font-size: 18px;
    font-weight: bold;
    margin: 0 0 20px 0;
    color: #fff;
    line-height: 26px;
}

.whom-group {
    border-width: 2px;
    border-radius: 0px 35px;
    border-style: solid;
    border-color: rgb(163, 216, 241);
    box-shadow: rgb(163, 216, 241) -6px 3px 0px 0px;
    background-color: rgb(255, 255, 255);
    display: flex;
    align-items: center;
    padding: 15px 30px;
    margin-bottom: 25px;
}

.whom-group .whom-check {
    width: 35px;
}

.whom-group .whom-check i {
    color: rgb(1, 116, 172);
    font-size: 35px;
}

.whom-group h3.whom-heading {
    font-weight: bold;
    line-height: 28px;
    color: rgb(1, 113, 168);
    text-align: justify;
    font-size: 19px;
    font-family: var(--font-nunito);
    margin: 0;
    width: calc(100% - 100px);
    padding: 0 20px;
}

.whom-group .whom-icon {
    width: 65px;
    text-align: center;
}

.whom-group .whom-icon svg, .whom-group .whom-icon img {
    width: 65px;
    height: 65px;
    object-fit: contain;
}

.whom-group .whom-icon i {
    font-size: 55px;
    color: rgb(1, 116, 172);
}

/**abouts**/
.dx {
    position: relative;
}

.dx .register-link {
    display: inline-block;
    margin: auto;
}

.sec-abouts {
    padding: 40px 0 200px;
}

.abouts-desc {
    font-size: 16px;
    font-style: italic;
    line-height: 26px;
}

.mui-ten {
    transform: rotate(-182deg);
    position: absolute;
    bottom: -115px;
    left: 0;
}

.mui-ten svg {
    width: 113px;
    height: 113px;
}

.cloud2 {
    position: absolute;
    bottom: 0;
    right: 50px;
    z-index: 1;
}

.cloud2 svg {
    width: 526.738px;
    height: 101.492px;
}

.cloud1 {
    position: absolute;
    bottom: 0;
    right: 280px;
    z-index: 99;
}

.cloud1 svg {
    width: 318.503px;
    height: 101.492px;
}

.abouts-video {
    margin: 30px 0;
}

.about-alert {
    display: flex;
    align-items: end;
    margin: 40px 0;
}

.about-alert .about-icon {
    width: 100%;
    height: 100%;
    object-fit: contain;
    position: relative;
    z-index: 9;
}

.about-alert .about-thumbnail {
    width: 201px;
    height: 285px;
    position: relative;
    z-index: 9;
}

.about-alert .about-text {
    border-width: 2px 2px 2px 0;
    border-radius: 30px 30px 30px 0px;
    border-style: solid;
    border-color: rgb(6, 148, 216);
    background-image: linear-gradient(90deg, rgb(255, 255, 255), rgb(232, 245, 255));
    width: calc(100% - 201px);
    padding: 30px 50px 20px 180px;
    line-height: 1.6;
    color: rgb(0, 0, 0);
    text-align: justify;
    font-size: 16px;
}

.about-alert .about-thumbnail .about-cer {
    width: 183px;
    height: 189px;
    position: absolute;
    top: 0px;
    left: 160px;
    z-index: 0;
}

.about-alert .about-thumbnail .about-cer img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    animation-name: pulse;
    animation-delay: 0s;
    animation-duration: 4s;
    animation-iteration-count: infinite;
}

.about-alert .about-svg {
    position: absolute;
    bottom: 0;
    right: -20px;
}

.about-alert .about-svg svg {
    width: 175px;
    height: 55px;
}

.cloud {
    position: absolute;
    bottom: -12px;
    left: 34%;
    transform: translateX(-50%);
    text-align: center;
}

.author-mini {
    position: absolute;
    bottom: -12px;
    right: 85%;
}

.author-mini img {
    width: 253px !important;
    height: 174px !important;
    border-radius: 60px 60px 30px 30px;
    object-fit: contain !important;
}

.about-alert .about-text-left {
    padding: 30px 250px 30px 50px !important;
    border-width: 2px 0px 2px 2px !important;
    border-radius: 60px 0 0 60px !important;
    border-color: rgb(255, 156, 32) !important;
    background: linear-gradient(90deg, rgb(255, 243, 224), rgb(255, 255, 255)) !important;
}

.alert-2 {
    position: relative;
}

.alert-2 .about-text {
    width: calc(100% - 25px) !important;
}

.alert-2 .about-thumbnail {
    width: 297px !important;
    height: 325px !important;
    position: absolute;
    right: -32px;
}

.alert-2 .about-svg {
    right: 55px;
}

.about-sleep img {
    width: 100%;
}

.about-sleep h3.about-sleep-text {
    line-height: 1.6;
    color: rgb(0, 0, 0);
    text-align: justify;
    font-size: 18px;
    margin: 0;
}

.about-sleep h3.ladi-headline {
    line-height: 1.6;
    color: rgb(0, 0, 0);
    text-align: left;
    font-size: 22px;
    font-weight: 700;
    font-family: 'baloo2';
}

.about-group {
    position: relative;
    margin-bottom: 20px;
}

.about-group h3.ladi-headline2 {
    color: rgb(6, 148, 216);
    text-transform: uppercase;
    position: relative;
    font-size: 34px;
    line-height: 1.6;
    font-weight: 700;
    font-family: 'baloo2';
}

.about-group .line-1 {
    border-top: 1px dashed rgb(6, 148, 216);
    height: 2px;
    width: 706px;
    display: inline-block;
    position: absolute;
    top: 50px;
}

.about-group .line-2 {
    border-top: 1px dashed rgb(6, 148, 216);
    height: 2px;
    width: 552px;
    display: inline-block;
    position: absolute;
    bottom: 0px;
}

.author-story {
    margin: 0 0 30px 0;
}

.author-story .story-right {
    border-radius: 22px;
    background: rgb(232, 245, 255);
    display: inline-block;
    padding: 12px 22px;
    line-height: 1.6;
    color: rgb(1, 133, 197);
    text-align: center;
    font-size: 37px;
    font-weight: 700;
    font-family: 'baloo2';
}

.author-story .box-author .author-thumbnail {
    height: 481.375px;
}

.author-faq {
    position: absolute;
    bottom: 0;
}

.author-faq img {
    width: 599.168px;
    height: 400.514px;
}

.author-story .author-content.author-right {
    border: 0 !important;
    background-image: linear-gradient(rgb(255, 225, 38), rgb(255, 255, 255));
    height: 247px;
    position: relative;
}

/**Banner**/
.sec-banner {
    background-image: linear-gradient(rgb(5, 147, 216), rgb(110, 210, 255));
    height: 750px;
    position: relative;
}

.sec-banner .banner-main {
    position: absolute;
    bottom: 0;
    right: -200px;
    z-index: 9;
}

.sec-banner .banner-main img {
    width: 1119.79px;
    height: 637.689px;
}

.sec-banner .banner-text h3 {
    line-height: 1.3;
    color: rgb(255, 255, 255);
    text-transform: uppercase;
    text-align: center;
    text-shadow: rgb(0, 63, 94) 1px 2px 3px;
    font-size: 46px;
    font-family: var(--font-baloo2);
    font-weight: 700;
}

.sec-banner .banner-text h4 {
    font-size: 24px;
    font-style: italic;
    line-height: 1.6;
    color: rgb(255, 255, 255);
    text-align: center;
    text-shadow: rgb(0, 111, 166) 1px 1px 2px;
    margin: 20px 0 30px;
}

.sec-banner .banner-list {
    border-width: 3px;
    border-radius: 39px;
    border-style: solid;
    border-color: rgb(255, 255, 255);
    background-image: linear-gradient(rgb(255, 208, 27), rgb(255, 242, 162));
    background-color: initial;
    background-size: initial;
    background-origin: initial;
    background-position: initial;
    background-repeat: initial;
    background-attachment: initial;
    -webkit-background-clip: initial;
    box-shadow: rgb(90, 150, 223) 0px 8px 0px -1px;
    padding: 40px 20px;
    position: relative;
    z-index: 99;
}

.sec-banner .nap {
    position: absolute;
    top: -25px;
}

.sec-banner .nap svg {
    width: 38.8483px;
    height: 51.9999px;
}

.sec-banner .banner-list-left {
    left: 35px;
}

.sec-banner .banner-list-right {
    right: 35px;
}

.sec-banner .banner-list ul li:before {
    content: '';
    width: 22px;
    height: 22px;
    top: 3px;
    background-image: url(../images/checked.svg);
    position: absolute;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    left: 0;
}

.sec-banner .banner-list ul {
    padding: 0;
}

.sec-banner .banner-list ul li {
    padding-bottom: 5px;
    padding-left: 28px;
    font-size: 18px;
    list-style: none;
    position: relative;
}

.sec-banner .banner-cloud1 {
    position: absolute;
    bottom: 0;
    left: 60px;
}

.sec-banner .banner-cloud1 svg {
    width: 318.503px;
    height: 101.492px;
}

.sec-banner .banner-cloud2 {
    position: absolute;
    bottom: 0;
    left: 350px;
}

.sec-banner .banner-cloud2 svg {
    width: 526.738px;
    height: 101.492px;
}

.sec-banner .banner-cloud3 {
    position: absolute;
    right: 100px;
    bottom: 0;
    z-index: 99;
}

.sec-banner .banner-cloud3 svg {
    width: 526.738px;
    height: 101.492px;
}

.sec-banner .banner-dot1 svg {
    width: 198.818px;
    height: 199.123px;
    top: 257.493px;
    left: 100px;
    position: absolute;
}

.sec-banner .banner-text {
    position: relative;
    width: 90%;
}

.sec-banner .banner-plain {
    position: absolute;
    top: 0;
    left: -80px;
}

.sec-banner .banner-plain svg {
    width: 48.8147px;
    transform: rotate(9deg);
}

.sec-banner .banner-net {
    position: absolute;
    left: -157px;
    top: 0;
}

.sec-banner .banner-net img {
    width: 134.241px;
    height: 105.383px;
}

.sec-banner .banner-mattroi {
    position: absolute;
    top: 91.4925px;
    right: 100px;
}

.sec-banner .banner-mattroi svg {
    width: 96px;
    height: 96px;
}

.sec-banner .banner-dot2 {
    position: absolute;
    top: 180px;
    right: 320px;
    z-index: 1;
}

.sec-banner .banner-dot2 svg {
    width: 264.717px;
    height: 265.123px;
}


/**home**/
.home-document .document-item .document-icon svg {
    width: 41px;
    height: 72px;
}

.home-document .document-item:nth-child(3) .document-icon svg {
    width: 51px;
    height: 51px;
}

.home-document .document-dx .document-muiten {
    width: 193.618px;
    height: 193.618px;
    transform: rotate(231deg);
    top: 0;
    right: 200px;
}

.notline:after {
    display: none !important;
}

.home-line {
    margin: 10px 0 0 0;
}

.home-line svg {
    width: 211px;
    height: 16.5302px;
}

.home-author .author-left:after {
    background: rgb(254, 222, 30) !important;
    z-index: 0;
}

.home-author .author-content {
    position: relative;
    z-index: 1;
}

.home-author .author-mini img {
    position: relative;
    z-index: 2;
}

.home-author .cloud {
    position: absolute;
    z-index: 3;
}

.notline {
    margin-bottom: 30px;
}

.notline h3 {
    font-weight: 900;
    background-image: linear-gradient(rgb(1, 133, 197), rgb(72, 188, 244));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-color: initial;
    background-size: initial;
    background-origin: initial;
    background-position: initial;
    background-repeat: initial;
    background-attachment: initial;
}

.home-author {
    padding: 50px 0 80px 0 !important;
}

.sec-student {
    background: rgb(237, 246, 255);
    padding: 50px 0 100px 0;
}

.student {
    background-image: linear-gradient(rgb(1, 133, 197), rgb(72, 188, 244));
    width: 100%;
    padding: 17px 17px 25px 17px;
    border-radius: 17px;
}

.student .student-content {
    background: rgb(255, 255, 255);
    border-radius: 8px;
    font-size: 14px;
    position: relative;
    padding: 15px;
    height: 297px;
}

.student .student-content .student-quote {
    position: absolute;
    right: 0;
    bottom: 0;
}

.student .student-content .student-quote svg {
    width: 41px;
    height: 41px;
}

.student .student-author {
    display: flex;
    flex-flow: wrap;
    margin-top: 15px;
}

.student .student-thumbnail {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.student .student-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.student .student-info {
    width: calc(100% - 50px);
    padding-left: 15px;
}

.student .student-info h4 {
    color: #fff;
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 0px;
}

.student .student-info p {
    font-size: 14px;
    color: #fff;
    margin: 0;
    height: 40px;
}

.chimcumeo svg {
    width: 105px;
    transform: rotate(11deg);
    height: 105px;
}

.chimcumeo {
    position: absolute;
    bottom: -80px;
    left: -150px;
}

.line-bottom {
    background-size: cover !important;
    background-origin: content-box !important;
    background-position: 50% 0% !important;
    background-repeat: repeat !important;
    background-attachment: scroll !important;
    background: url("../images/may-ngan.png") no-repeat;
    height: 140px;
    width: 100%;
}

.sec-course2 {
    background-image: linear-gradient(rgb(1, 133, 197), rgb(5, 147, 216)) !important;
}

.line-down svg {
    width: 134.649px;
    height: 65.25px;
    animation-name: fadeInDown;
    animation-delay: 0s;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

.list-course {
    margin-bottom: 40px;
}

.course-item {
    background: rgb(255, 255, 255);
    border-radius: 17px;
    padding: 15px;
}

.course-item .course-thumbnail {
    width: 100%;
    height: 240px;
    border-radius: 17px;
}

.course-item .course-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 17px;
}

.course-item .course-content h3 a {
    font-size: 18px;
    line-height: 1.6;
    color: rgb(1, 23, 33);
    text-align: center;
    text-align: center;
    display: block;
}

.course-item .course-button {
    margin: 12px 0 0 0;
}

.course-item .course-button a {
    border-radius: 13px;
    border: 2px solid rgb(255, 156, 32);
    width: 100%;
    display: block;
    text-align: center;
    color: rgb(255, 92, 0);
    padding: 12px 0;
    font-weight: 700;
    font-size: 17px;
    line-height: 25px;
}

.course-item .course-content {
    padding: 15px 0;
}

.course-item .course-button a:hover {
    background: rgb(255, 156, 32);
    color: #fff;
}

.dx-about-author img {
    width: 100%;
}

.dx-line .dx-heading h3 {
    text-align: center;
    color: #fff;
    font-weight: 700;
    margin: 0;
    font-size: 22px;
    line-height: 28px;
}

.dx-line {
    border: 2px solid rgb(5, 147, 216);
    border-radius: 16px;
    background: #fff;
}

.dx-line .dx-heading {
    background: rgb(5, 147, 216);
    border-radius: 13px;
    padding: 15px;
    text-align: center;
    color: #fff;
    font-weight: 700;
    margin: 0;
    font-size: 22px;
}

.dx-line .dx-line-body {
    padding: 35px 50px;
}

.dx-line .dx-line-body .ladi-paragraph {
    margin: 0 0 20px 0;
    font-size: 18px;
    line-height: 1.6;
}

.dxItem {
    border-width: 2px;
    border-radius: 0px 16px;
    border-style: solid;
    border-color: rgb(163, 216, 241);
    box-shadow: rgb(163, 216, 241) -6px 3px 0px 0px;
    padding: 8px;
    display: flex;
    align-items: center;
    flex-flow: wrap;
    height: 100%;
}

.dxItem .dxIcon svg {
    width: 67px;
    height: 67px;
}

.dxItem h4 {
    width: calc(100% - 67px);
    text-align: center;
    font-weight: bold;
    line-height: 1.5;
    color: rgb(1, 116, 172);
    font-size: 17px;
    margin: 0;
}

.dx-cloud1 svg {
    width: 491.771px;
    height: 366.637px;
    transform: rotate(363deg);
    position: absolute;
    bottom: -150px;
    left: 50px;
    z-index: -1;
}

.dx-cloud2 svg {
    width: 489.05px;
    height: 237.251px;
    transform: rotate(185deg);
    position: absolute;
    bottom: -150px;
    left: 150px;
    z-index: -1;
}

.dx-cloud3 svg {
    transform: perspective(1000px) rotateX(-28deg) rotateY(180deg);
    width: 1601px;
    height: 443.38px;
    left: 50px;
    position: absolute;
    z-index: -2;
    bottom: -50px;
}

.dx-cloud4 svg {
    width: 99px;
    height: 99px;
    position: absolute;
    right: 50px;
    bottom: 250px;
}

.banner_heading {
    font-family: var(--font-baloo2);
    font-weight: bold;
    line-height: 1.6;
    color: rgb(253, 221, 30);
    text-align: left;
    text-shadow: rgb(1, 116, 172) 1px 2px 1px;
    font-size: 29px;
}

.banner-list .ladi-paragraph {
    margin: 0 0 20px 0;
    font-size: 18px;
    line-height: 1.6;
}

.banner-nap {
    margin-top: 30px;
}

.bannerImage img {
    transform: rotate(7deg);
    width: 100%;
    position: relative;
    z-index: 9;
}

.banner-home {
    height: 588px;
}

.bannerLine svg {
    transform: rotate(192deg) !important;
    width: 385px;
    height: 304px;
    position: absolute;
    top: 40%;
    right: -80px;
    z-index: 0;
}


#button-contact-vr {
    position: fixed;
    bottom: 0;
    z-index: 99999
}

#button-contact-vr .button-contact {
    position: relative;
    margin-top: -5px
}

#button-contact-vr .button-contact .phone-vr {
    position: relative;
    visibility: visible;
    background-color: transparent;
    width: 90px;
    height: 90px;
    cursor: pointer;
    z-index: 11;
    -webkit-backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transition: visibility .5s;
    left: 0;
    bottom: 0;
    display: block
}

.phone-vr-circle-fill {
    width: 65px;
    height: 65px;
    top: 12px;
    left: 12px;
    position: absolute;
    box-shadow: 0 0 0 0 #c31d1d;
    background-color: rgba(230, 8, 8, 0.7);
    border-radius: 50%;
    border: 2px solid transparent;
    -webkit-animation: phone-vr-circle-fill 2.3s infinite ease-in-out;
    animation: phone-vr-circle-fill 2.3s infinite ease-in-out;
    transition: all .5s;
    -webkit-transform-origin: 50% 50%;
    -ms-transform-origin: 50% 50%;
    transform-origin: 50% 50%;
    -webkit-animuiion: zoom 1.3s infinite;
    animation: zoom 1.3s infinite
}

.phone-vr-img-circle {
    background-color: #e60808;
    width: 40px;
    height: 40px;
    line-height: 40px;
    top: 25px;
    left: 25px;
    position: absolute;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    -webkit-animation: phonering-alo-circle-img-anim 1s infinite ease-in-out;
    animation: phone-vr-circle-fill 1s infinite ease-in-out
}

.phone-vr-img-circle a {
    display: block;
    line-height: 37px
}

.phone-vr-img-circle img {
    max-height: 25px;
    max-width: 27px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%)
}

#instagram-vr .phone-vr-circle-fill {
    background: rgb(17, 143, 253);
    background: linear-gradient(160deg, rgba(17, 143, 253, 1) 20%, rgba(188, 60, 218, 1) 50%, rgba(253, 223, 5, 1) 80%);
    background-size: contain;
    box-shadow: 0 0 0 0 #c840c9;
    background-color: rgb(79 103 254);
    border: 0
}

#instagram-vr .phone-vr-img-circle {
    background: transparent
}

#telegram-vr .phone-vr-circle-fill {
    box-shadow: 0 0 0 0 #2c9fd8;
    background-color: rgb(44 159 216 / 74%)
}

#telegram-vr .phone-vr-img-circle {
    background: #2c9fd8
}

@-webkit-keyframes phone-vr-circle-fill {
    0% {
        -webkit-transform: rotate(0) scale(1) skew(1deg)
    }

    10% {
        -webkit-transform: rotate(-25deg) scale(1) skew(1deg)
    }

    20% {
        -webkit-transform: rotate(25deg) scale(1) skew(1deg)
    }

    30% {
        -webkit-transform: rotate(-25deg) scale(1) skew(1deg)
    }

    40% {
        -webkit-transform: rotate(25deg) scale(1) skew(1deg)
    }

    50% {
        -webkit-transform: rotate(0) scale(1) skew(1deg)
    }

    100% {
        -webkit-transform: rotate(0) scale(1) skew(1deg)
    }
}

@-webkit-keyframes zoom {
    0% {
        transform: scale(.9)
    }

    70% {
        transform: scale(1);
        box-shadow: 0 0 0 15px transparent
    }

    100% {
        transform: scale(.9);
        box-shadow: 0 0 0 0 transparent
    }
}

@keyframes zoom {
    0% {
        transform: scale(.9)
    }

    70% {
        transform: scale(1);
        box-shadow: 0 0 0 15px transparent
    }

    100% {
        transform: scale(.9);
        box-shadow: 0 0 0 0 transparent
    }
}

.phone-bar a {
    position: absolute;
    margin-top: -65px;
    left: 30px;
    z-index: -1;
    color: #fff;
    font-size: 16px;
    padding: 7px 15px 7px 50px;
    border-radius: 100px;
    white-space: nowrap
}

.phone-bar a:hover {
    opacity: 0.8;
    color: #fff
}

@media (max-width: 736px) {
    .phone-bar {
        display: none
    }
}

#zalo-vr .phone-vr-circle-fill {
    box-shadow: 0 0 0 0 #2196F3;
    background-color: rgba(33, 150, 243, 0.7)
}

#zalo-vr .phone-vr-img-circle {
    background-color: #2196F3
}

#viber-vr .phone-vr-circle-fill {
    box-shadow: 0 0 0 0 #714497;
    background-color: rgba(113, 68, 151, 0.8)
}

#viber-vr .phone-vr-img-circle {
    background-color: #714497
}

#contact-vr .phone-vr-circle-fill {
    box-shadow: 0 0 0 0 #2196F3;
    background-color: rgba(33, 150, 243, 0.7)
}

#contact-vr .phone-vr-img-circle {
    background-color: #2196F3
}

div#whatsapp-vr .phone-vr .phone-vr-circle-fill {
    box-shadow: 0 0 0 0 #1fd744;
    background-color: rgb(35 217 72 / 70%)
}

div#whatsapp-vr .phone-vr .phone-vr-img-circle {
    background: #1cd741
}

div#whatsapp-vr .phone-vr .phone-vr-img-circle img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 50%
}

#fanpage-vr img {
    max-width: 35px;
    max-height: 35px
}

#fanpage-vr .phone-vr-img-circle {
    background-color: #1877f2
}

#fanpage-vr .phone-vr-circle-fill {
    box-shadow: 0 0 0 0 rgb(24 119 242 / 65%);
    background-color: rgb(24 119 242 / 70%)
}

#gom-all-in-one .button-contact {
    transition: 1.6s all;
    -moz-transition: 1.6s all;
    -webkit-transition: 1.6s all
}

#button-contact-vr.active #gom-all-in-one .button-contact {
    margin-left: -100%
}

#all-in-one-vr .phone-bar {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 100%;
    color: #fff;
    padding: 5px 15px 5px 48px;
    border-radius: 50px;
    margin-left: -64px;
    width: max-content;
    cursor: pointer
}

#popup-showroom-vr, div#popup-form-contact-vr {
    display: none
}

#popup-showroom-vr.active, div#popup-form-contact-vr.active {
    display: block;
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 99999
}

.bg-popup-vr {
    position: absolute;
    left: 0;
    top: 0;
    background: rgb(51 51 51 / 50%);
    width: 100%;
    height: 100vh
}

.content-popup-vr {
    background: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 95%;
    border-radius: 5px;
    box-shadow: 0 0 14px 5px rgb(0 0 0 / 49%);
    max-width: 600px;
    display: flex
}

.content-popup-vr input, .content-popup-vr textarea {
    width: 100%;
    max-height: 100px;
    min-height: 38px;
    border: 1px solid #b1b1b1;
    margin-bottom: 10px;
    padding: 0 7px;
    background: #fff
}

.content-popup-vr label {
    width: 100%
}

.content-popup-vr input.wpcf7-form-control.wpcf7-submit {
    max-width: fit-content;
    padding: 5px 32px 2px;
    background: #2196f3;
    border: 0;
    color: #fff;
    font-size: 16px;
    border-radius: 5px;
    margin: 10px auto 0;
    display: block;
    box-shadow: 0 0 5px 1px rgb(0 0 0 / 29%);
    font-weight: 400;
    min-height: auto;
    line-height: 30px
}

.content-popup-vr input.wpcf7-form-control.wpcf7-submit:hover {
    opacity: 0.7
}

.content-popup-vr .close-popup-vr {
    font-family: sans-serif;
    width: 23px;
    height: 23px;
    background: black;
    position: absolute;
    top: -10px;
    right: -10px;
    color: #fff;
    text-align: center;
    line-height: 23px;
    font-size: 17px;
    border-radius: 50%;
    cursor: pointer
}

.content-popup-vr .close-popup-vr:hover {
    background: #b50000
}

.content-popup-vr .content-popup-div-vr {
    width: 100%;
    padding: 25px
}

.content-popup-vr .content-popup-img-vr {
    width: 100%;
    max-width: 45%;
    border-radius: 10px 0 0 10px;
    overflow: hidden
}

#loco-top, #loco-bottom {
    display: block
}

#loco-top .content-popup-img-vr, div#popup-form-contact-vr #loco-bottom .content-popup-img-vr {
    max-width: 100%;
    text-align: center
}

#contact-showroom.no-event a {
    pointer-events: none
}

.content-popup-vr .content-popup-div-vr ul {
    color: #333;
    list-style: none;
    font-size: 15px
}

@media (max-width: 673px) {
    div#popup-form-contact-vr .content-popup-vr {
        display: block
    }

    div#popup-form-contact-vr .content-popup-vr .content-popup-img-vr {
        max-width: 100%;
        display: none
    }
}

#ftiktok-vr .phone-vr-img-circle {
    background-color: #020202;
}

#tiktok-vr .phone-vr-circle-fill {
    box-shadow: 0 0 0 0 rgb(2 2 2 / 55%);
    background-color: rgb(2 2 2 / 60%);
}

#tiktok-vr .phone-vr-img-circle img {
    max-width: 90%;
    max-height: 90%;
}

#tiktok-vr .phone-vr-img-circle {
    background: #020202;
}

#messenger-vr .phone-vr-circle-fill {
    box-shadow: 0 0 0 0 #6a4ffe;
    background-color: rgb(106 79 254 / 50%);
}

#messenger-vr .phone-vr-img-circle {
    background-color: #6a4ffe;
}

#messenger-vr .phone-vr-img-circle img {
    max-width: 100%;
    max-height: 100%;
}

#button-contact-vr {
    right: 0;
}

#button-contact-vr {
    transform: scale(1);
}

nav.tab-method button {
    text-transform: uppercase;
}

/**responsive**/
@media only screen and (max-width: 768px) {
    .sec-banner .banner-text h3 {
        font-size: 26px;
    }

    .sec-banner .banner-text h4 {
        font-size: 14px;
        line-height: 24px;
        margin: 15px 0;
    }

    .sec-banner .banner-list ul li {
        font-size: 15px;
        list-style: none;
        position: relative;
    }

    .sec-banner .banner-list {
        padding: 25px 20px;
        z-index: 99;
    }

    .sec-banner .banner-main {
        z-index: 99;
        bottom: -23px;
        left: 0;
    }

    .sec-banner .banner-main img {
        width: 500px;
        height: 331.789px;
        object-fit: contain;
        object-position: center;
    }

    .banner-cloud1 {
        z-index: 99999;
        left: -250px !important;
    }

    .banner-cloud1 svg {
        width: 368.621px !important;
        height: 117.462px !important;
    }

    .banner-cloud2 {
        z-index: 99999;
        left: 31.983px !important;
    }

    .banner-cloud2 svg {
        width: 180.328px !important;
        height: 57.462px !important;
    }

    .banner-cloud3 {
        right: -80px !important;
        z-index: 99999;
    }

    .banner-cloud3 svg {
        width: 202.296px !important;
        height: 64.462px !important;
    }

    .sec-banner .banner-mattroi {
        display: none;
    }

    .sec-down {
        z-index: 999999;
        width: 55px;
        height: 55px;
        bottom: -25px;
    }

    .sec-banner {
        padding-bottom: 0 !important;
    }

    .about-group h3.ladi-headline2 {
        font-size: 18px;
        line-height: 35px;
    }

    .about-group .line-1 {
        width: 100%;
        top: 32px;
    }

    .about-group .line-2 {
        width: 100%;
    }

    .about-sleep h3.ladi-headline {
        line-height: 26px;
        font-size: 18px;
    }

    .about-sleep h3.about-sleep-text {
        line-height: 25px;
        font-size: 15px;
    }

    .about-alert {
        border-width: 2px !important;
        border-radius: 30px;
        border-style: solid;
        border-color: rgb(6, 148, 216);
        background-image: linear-gradient(90deg, rgb(255, 255, 255), rgb(232, 245, 255));
        padding: 25px 25px 0 25px;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-align: justify;
        font-size: 16px;
        display: block;
        width: 100%;
    }

    .about-alert .about-text {
        background: unset !important;
        border: 0 !important;
        padding: 0 !important;
        width: 100%;
    }

    .about-alert .about-thumbnail {
        width: 150px;
        height: auto;
    }

    .about-alert .about-thumbnail .about-cer {
        width: 133px;
        height: 139px;
        position: absolute;
        top: 0px;
        left: 120px;
        z-index: 0;
    }

    .box-register .register-copy .register-button {
        font-size: 13px;
    }

    .box-register .register-text {
        width: calc(100% - 100px);
        font-size: 16px;
    }

    .alert-2 .about-text {
        width: 100%;
        padding: 30px !important;
    }

    .alert-2 .about-thumbnail {
        position: unset;
    }

    .register-countdown {
        gap: 15px;
    }

    .register-down {
        gap: 15px;
        margin: 10px 0 0;
        padding: 5px 15px;
    }

    .register-3 .box-register-body, .register-2 .box-register-body {
        padding-top: 10px;
    }

    .register-down > * {
        font-size: 16px !important;
    }

    .box-register .box-register-body {

        padding: 10px;
    }

    .alert-2 {
        width: 100% !important;
        position: relative;
        display: block;
        border-width: 2px !important;
        border-radius: 30px;
        border-style: solid;
        border-color: rgb(255, 156, 32) !important;
        padding: 25px;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-align: justify;
        font-size: 16px;
    }

    .alert-2 .about-text {
        background: unset !important;
        border: 0 !important;
        padding: 0 !important;
        width: 100% !important;
    }

    .alert-2 .about-svg {
        right: 20px !important;
    }

    iframe.iframe-video-preload {
        height: 240px !important;
    }

    .abouts-desc {
        margin: 0 0 30px 0;
    }

    .font-46, .font-42, .home-title h3 {
        font-size: 28px !important;
        line-height: 38px !important;
    }

    .whom-group .whom-check {
        display: none;
    }

    .whom-group {
        padding: 20px;
        flex-flow: row-reverse;
    }

    .whom-group h3.whom-heading {
        width: calc(100% - 65px);
        padding: 0 10px;
        font-size: 16px;
        line-height: 24px;
    }

    .sec-whom .whom-a {
        text-align: right;
        margin-top: -45px;
    }

    .sec-whom .whom-a img {
        width: 164.005px;
        height: 259.716px;
    }

    .document-item .document-icon {
        width: 80px;
        height: 80px;
        left: -40px;
    }

    .document-item {
        padding: 20px 20px 20px 50px;
        width: calc(100% - 40px);
    }

    .document-item .document-icon i {
        font-size: 40px;
    }

    .document-item .document-icon svg {
        width: 40px;
        height: 40px
    }

    .document-item .document-text p {
        margin: 0;
        font-size: 14px;
    }

    .document-dx img {
        width: 180px;
        height: unset;
    }

    .dx .register-link .register-link-title {
        font-size: 18px;
        line-height: 22px;
    }

    .dx .register-link .register-link-icon img {
        width: 20px;
        height: 20px;
    }

    .dx .register-link .register-link-icon {
        width: 41px;
        height: 41px;
    }

    .dx .register-link {
        display: inline-block;
        margin: auto;
        right: unset;
        left: 10px;
    }

    nav.tab-method {
        margin-bottom: 20px;
    }

    nav.tab-method button {
        font-size: 17px;
        text-transform: uppercase;
        padding: 8px;
        width: calc(50% - 6px);
    }

    .tab-method-panel .method-body {
        padding: 20px 0;
    }

    .register-2 {
        width: 100%;
        margin: 30px 0 0 0;
    }

    .register-countdown div {
        font-size: 35px;
    }

    .box-register .register-link .register-link-title {
        font-size: 12px;
        padding-left: 5px;
    }

    .box-register .register-link .register-link-icon img {
        width: 20px;
        height: 20px;
    }

    .box-register .register-link .register-link-icon {
        width: 38px;
        height: 38px;
    }

    .top-review .top-review-author img {
        width: 55%;
        float: left;
    }

    .top-review .top-review-author .top-review-info {
        padding: 20px;
        font-size: 22px;
        right: unset;
        width: 100%;
        top: -90px;
        z-index: 0;
    }

    .top-review .top-review-author-info {
        padding-left: 200px;
        margin-top: -56px;
    }

    .top-review-author-info p {
        font-size: 13px;
    }

    .top-review-author-info h4 {
        font-size: 16px;
    }

    .review-item {
        margin-bottom: 20px;
    }

    .review-item .review-images img {
        height: 320px;
    }

    .review-item .review-desktop {
        background: rgb(255, 143, 1);
        border-radius: 20px;
        padding: 10px;
        overflow: hidden;
        width: 220px;
        margin: 0 -80px 0 0 !important;
        height: 220px;
        position: relative;
    }

    .box-register {
        width: 100%;
        position: relative;
        z-index: 99;
    }

    .box-register {
        padding: 10px;
    }

    .register-left {
        width: 100%;
    }

    .author-left {
        margin-top: 220px;
    }

    .author-left .author-thumbnail {
        right: unset !important;
        left: 0 !important;
        top: -190px;
    }

    .box-author .author-cloud {
        position: absolute;
        left: unset !important;
        top: 92px !important;
        z-index: 99;
        right: 0;
    }

    .box-author .author-thumbnail {
        width: 221px;
        border-radius: 65px;
        height: 290px;
        position: absolute;
        z-index: 2;
    }

    .authorRight .author-thumbnail {
        top: -190px;
        right: 0 !important;
        left: unset !important;
    }

    .author-content.author-right, .box-author .author-content {
        border-radius: 30px;
        margin: 0;
        padding: 140px 20px 20px 20px;
    }

    .author-lf-1  .author-content{
        padding-bottom: 220px;
    }

    .box-author {
        position: relative;
    }

    .author-cloud.text-right img, .box-author .author-cloud img {
        width: 150.5px;
    }

    .author-cloud.text-right {
        position: absolute;
        left: 0 !important;
        top: 92px !important;
        z-index: 99;
        right: unset !important;
    }

    .author-story .story-right {
        padding: 8px 10px;
        font-size: 26px;
        width: 100%;
    }

    .author-faq img {
        width: 325.086px;
        height: 217.304px;
    }

    .author-story .box-author .author-thumbnail {
        width: 252.332px;
        height: 355.015px;
        right: 0 !important;
        left: unset !important;
        position: absolute;
        z-index: 9;
    }

    .author-faq {
        z-index: 9999 !important;
    }

    .author-story .author-content.author-right {
        background: transparent !important;
        z-index: 99;
    }

    .banner-home {
        height: unset;
    }

    .banner-home .banner-text h3 {
        font-size: 25px !important;
    }

    .banner-list .ladi-paragraph {
        margin: 0 0 10px 0;
        font-size: 16px;
        line-height: 24px;
    }

    .bannerImage {
        padding-bottom: 100px;
    }

    .bannerImage img {
        transform: rotate(7deg);
        width: 80%;
        position: relative;
        z-index: 99;
        margin: -30px auto 0;
        display: block;
    }

    .bannerLine svg {
        width: 283.684px;
        height: 224px;
        top: 20%
    }

    .dx-line .dx-line-body {
        padding: 25px;
    }

    .dx-line .dx-heading h3 {
        font-size: 18px;
        line-height: 24px;
    }

    .dx-line .dx-line-body .ladi-paragraph {
        margin: 0 0 15px 0;
        font-size: 15px;
        line-height: 25px;
    }

    .dx-cloud4 svg {
        width: 90px;
        height: 90px;
        position: absolute;
        right: 80px;
        bottom: unset;
        top: 200px;
        transform: rotate(-23deg);
    }

    .line-down svg {
        width: 60px;
        height: 40px;
    }

    .line-bottom {
        height: 80px;
    }

    .dx-about-author img {
        width: 50%;
        margin-bottom: -70px;
    }

    .dx-cloud1 svg {
        width: 200px;
        height: 130px;
        transform: rotate(363deg);
        position: absolute;
        bottom: unset;
        left: -54px;
        z-index: -1;
        top: 280px;
    }

    .dx-cloud2 svg {
        width: 200px;
        height: 130px;
        transform: rotate(185deg);
        position: absolute;
        bottom: unset;
        right: -54px;
        z-index: -1;
        top: 280px;
    }

    .sd-item .sd-images img {
        height: 600px;
    }

    .thumb-clock {
        top: 50%;
        right: -25px;
        transform: rotate(-24deg);
    }

    .thumb-clock img {
        width: 80px;
        height: 80px;
        object-fit: contain;
    }

    .thumb-computer {
        transform: rotate(1deg);
        position: absolute;
        top: 35%;
        left: 10px;
    }

    .whomLeft {
        position: relative;
    }

    .whom-run svg {
        width: 80px;
        transform: rotate(-9deg);
        height: 80px;
        position: absolute;
        top: 0;
        left: 0;
    }

    .position-whome {
        position: absolute;
        top: 180px;
        left: 0;
    }

    .sec-banner .banner-text {
        width: 100% !important;
    }

    .register-down span:nth-child(3), .register-down span:nth-child(1) {
        font-size: 20px !important;
    }

    .box-register .register-button {
        gap: 5px;
    }

    .register-left:after {
        left: -210px;
    }

    .home-document .document-dx .document-muiten {
        width: 150px;
        height: 150px;
        top: 0;
        right: 150px;
    }

    .author-cloud2 .author-mini {
        right: 0;
        bottom: 0px;
    }

    .author-cloud2 .cloud {
        bottom: 0;
        left: 15px;
        transform: unset;
        width: 200px;
    }
}


@media only screen and (min-width: 768px) {
    .container, .container-lg, .container-md, .container-sm, .container-xl, .container-xxl {
        max-width: 1200px !important;
    }
}

@media only screen and (min-width: 1920px) {
    .sec-banner .banner-mattroi {
        right: 300px;
    }

    .dx-cloud4 svg {
        width: 129px;
        height: 129px;
        right: 150px;
    }

    .dx-cloud1 svg {
        left: 100px;
    }

    .dx-cloud2 svg {
        left: 300px;
    }

    .sec-banner .banner-text {
        position: relative;
        width: 100%;
    }

    .sec-banner .banner-dot1 svg {
        left: 280px;
    }

    .sec-banner .banner-main img {
        width: 1151px;
        height: 656px;
    }

    .sec-banner .banner-main {
        right: 0px;
    }

    .sec-abouts {
        padding: 40px 0 120px;
    }

    .thumb-clock {
        right: 160px;
    }

    .thumb-computer {
        left: 200px;
    }
}


/* iPad landscape style here */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: landscape) {
    .sec-banner .banner-main img {
        width: 900px;
        height: 500px;
        object-fit: contain;
    }

    .sec-banner .banner-dot2 {
        right: 0px;
    }

    div#main-menu ul li a {
        padding: 6px 10px;
        font-size: 16px;
    }
}

/* iPad portrait style here */
@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: portrait) {
    div#main-menu ul li a {
        font-size: 16px;
        padding: 6px 10px;
    }

    .sec-banner .banner-main img {
        width: 900px;
        height: 500px;
        object-fit: contain;
    }

    .sec-banner .banner-dot2 {
        right: 0px;
    }
}

@media only screen and (max-width: 370px) {
    .sec-banner .banner-text h3 {
        font-size: 24px;
    }

    .box-register .register-link .register-link-title {
        font-size: 11px;
        padding-left: 5px;
    }

    .box-register .register-link .register-link-icon {
        width: 35px;
        height: 35px;
    }

    .box-register .register-button {
        gap: 5px;
    }
}

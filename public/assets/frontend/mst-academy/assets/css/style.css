body {
    font-family: "Roboto", sans-serif;
    color: #1e1e1e;
}
@keyframes dots-left {
    0% {
        opacity: 0.4;
        transform: translateX(-10px);
    }
    100% {
        opacity: 0.9;
        transform: translateX(10px);
    }
}
@keyframes dots-right {
    0% {
        opacity: 0.4;
        transform: translateX(10px);
    }
    100% {
        opacity: 0.9;
        transform: translateX(-10px);
    }
}
.dots-animation-left {
    animation: dots-left 0.4s infinite alternate;
}
.dots-animation-right {
    animation: dots-right 0.4s infinite alternate;
}
.txt-gra {
    background: linear-gradient(4deg, #fe6100 3.36%, #ffa600 76.3%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.speech-bubble {
    position: relative;
}
.speech-bubble:after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    width: 0;
    height: 0;
    border: 10px solid transparent;
    border-top-color: #ffa500;
    border-bottom: 0;
    margin-left: -10px;
}
.register-button {
    position: relative;
    z-index: 1;
}
.register-button:before {
    content: "";
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: white;
    border-radius: 50px;
    z-index: -1;
}
.laptop-container {
    position: relative;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}
.laptop-base {
    width: 100%;
    height: auto;
    border-radius: 20px 20px 0 0;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    background: white;
    position: relative;
    z-index: 1;
}
.laptop-stand {
    width: 70%;
    height: 40px;
    margin: -5px auto 0;
    background: linear-gradient(to bottom, #e0e0e0, #f5f5f5);
    border-radius: 0 0 50% 50%;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}
.highlight-box {
    border-left: 4px solid #ffa500;
    padding-left: 20px;
}
.arrow {
    font-size: 2rem;
    color: #ffa500;
}
.btn-register-now {
    height: 50px;
}
.btn-register-now span {
    transform: translateY(3px);
    display: block;
}
.feature-header {
    background: url(../images/qt-title.png) no-repeat center center;
    background-size: cover;
    width: 370px;
    height: 62px;
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
}
.section-event {
    background: url(../images/bg-event.png) no-repeat center center;
    background-size: 100% 100%;
    position: relative;
}
.italic {
    border: none !important;
}
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-background-clip: text;
    -webkit-text-fill-color: #111;
    transition: background-color 5000s ease-in-out 0s;
    box-shadow: none;
}

.ladi-video-background {
    background-image: url(https://img.youtube.com/vi/MsbpzhzFdcI/hqdefault.jpg);
    background-size: cover;
    background-origin: content-box;
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-attachment: scroll;
}

@media (max-width: 768px) {
    .section-event {
        background-size: cover;
    }
}

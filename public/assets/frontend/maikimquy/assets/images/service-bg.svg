<svg width="1600" height="759" viewBox="0 0 1600 759" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1600" height="759" fill="url(#paint0_radial_2050_694)"/>
<g filter="url(#filter0_f_2050_694)">
<g clip-path="url(#paint1_angular_2050_694_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.083 0 0 0.083 1420 599)"><foreignObject x="-1015.06" y="-1015.06" width="2030.12" height="2030.12"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(20, 64, 255, 1) 0deg,rgba(80, 121, 251, 1) 360deg);height:100%;width:100%;opacity:0.3"></div></foreignObject></g></g><circle cx="1420" cy="599" r="83" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.080769173800945282,&#34;g&#34;:0.25130522251129150,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.31462630629539490,&#34;g&#34;:0.47688233852386475,&#34;b&#34;:0.98557692766189575,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.080769173800945282,&#34;g&#34;:0.25130522251129150,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.31462630629539490,&#34;g&#34;:0.47688233852386475,&#34;b&#34;:0.98557692766189575,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:166.0,&#34;m01&#34;:0.0,&#34;m02&#34;:1337.0,&#34;m10&#34;:0.0,&#34;m11&#34;:166.0,&#34;m12&#34;:516.0},&#34;opacity&#34;:0.30000001192092896,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<defs>
<filter id="filter0_f_2050_694" x="1294" y="473" width="252" height="252" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="21.5" result="effect1_foregroundBlur_2050_694"/>
</filter>
<clipPath id="paint1_angular_2050_694_clip_path"><circle cx="1420" cy="599" r="83"/></clipPath><radialGradient id="paint0_radial_2050_694" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(244.5 174.246) rotate(108.043) scale(789.404 2180.56)">
<stop stop-color="#EAE3FF"/>
<stop offset="0.42265" stop-color="white"/>
<stop offset="1" stop-color="#FFFAF2"/>
</radialGradient>
</defs>
</svg>

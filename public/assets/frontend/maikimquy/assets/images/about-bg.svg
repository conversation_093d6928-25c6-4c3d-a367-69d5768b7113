<svg width="1600" height="975" viewBox="0 0 1600 975" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1600" height="975" fill="url(#paint0_radial_2022_646)"/>
<g filter="url(#filter0_f_2022_646)">
<g clip-path="url(#paint1_angular_2022_646_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0.107 0 0 0.107 1428 700)"><foreignObject x="-1011.68" y="-1011.68" width="2023.36" height="2023.36"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(20, 64, 255, 1) 0deg,rgba(80, 121, 251, 1) 360deg);height:100%;width:100%;opacity:0.3"></div></foreignObject></g></g><circle cx="1428" cy="700" r="107" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.080769173800945282,&#34;g&#34;:0.25130522251129150,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.31462630629539490,&#34;g&#34;:0.47688233852386475,&#34;b&#34;:0.98557692766189575,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.080769173800945282,&#34;g&#34;:0.25130522251129150,&#34;b&#34;:1.0,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.31462630629539490,&#34;g&#34;:0.47688233852386475,&#34;b&#34;:0.98557692766189575,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:214.0,&#34;m01&#34;:0.0,&#34;m02&#34;:1321.0,&#34;m10&#34;:0.0,&#34;m11&#34;:214.0,&#34;m12&#34;:593.0},&#34;opacity&#34;:0.30000001192092896,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
</g>
<defs>
<filter id="filter0_f_2022_646" x="1278" y="550" width="300" height="300" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="21.5" result="effect1_foregroundBlur_2022_646"/>
</filter>
<clipPath id="paint1_angular_2022_646_clip_path"><circle cx="1428" cy="700" r="107"/></clipPath><radialGradient id="paint0_radial_2022_646" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(244.5 223.834) rotate(104.229) scale(994.708 2222.97)">
<stop stop-color="#EAE3FF"/>
<stop offset="0.308984" stop-color="white"/>
<stop offset="1" stop-color="#DDEDFF"/>
</radialGradient>
</defs>
</svg>

*,
::before,
::after {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style: ;
}

::backdrop {
    --tw-border-spacing-x: 0;
    --tw-border-spacing-y: 0;
    --tw-translate-x: 0;
    --tw-translate-y: 0;
    --tw-rotate: 0;
    --tw-skew-x: 0;
    --tw-skew-y: 0;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    --tw-pan-x: ;
    --tw-pan-y: ;
    --tw-pinch-zoom: ;
    --tw-scroll-snap-strictness: proximity;
    --tw-gradient-from-position: ;
    --tw-gradient-via-position: ;
    --tw-gradient-to-position: ;
    --tw-ordinal: ;
    --tw-slashed-zero: ;
    --tw-numeric-figure: ;
    --tw-numeric-spacing: ;
    --tw-numeric-fraction: ;
    --tw-ring-inset: ;
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgb(59 130 246 / 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    --tw-blur: ;
    --tw-brightness: ;
    --tw-contrast: ;
    --tw-grayscale: ;
    --tw-hue-rotate: ;
    --tw-invert: ;
    --tw-saturate: ;
    --tw-sepia: ;
    --tw-drop-shadow: ;
    --tw-backdrop-blur: ;
    --tw-backdrop-brightness: ;
    --tw-backdrop-contrast: ;
    --tw-backdrop-grayscale: ;
    --tw-backdrop-hue-rotate: ;
    --tw-backdrop-invert: ;
    --tw-backdrop-opacity: ;
    --tw-backdrop-saturate: ;
    --tw-backdrop-sepia: ;
    --tw-contain-size: ;
    --tw-contain-layout: ;
    --tw-contain-paint: ;
    --tw-contain-style: ;
}

html,
body {
    font-family: "Exo", sans-serif;
    font-size: 16px;
    width: 100vw;
    overflow-x: hidden;
}

.container {
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 1024px) {
    .container {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

@media (min-width: 1280px) {
    .container {
        padding-left: 0px;
        padding-right: 0px;
        max-width: 1140px;
        margin: 0 auto;
    }
}

@media (min-width: 1536px) {
    .container {
        max-width: 1140px !important;
    }
}

.header.sticky-header {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    animation: slideDown 0.5s ease forwards;
    background: #143fffd3;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

.header.active {
    background: #143fffd3;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

.header .container {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

@media (min-width: 1024px) {
    .header .container {
        padding-top: 1.5rem;
        padding-bottom: 1.5rem;
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

@media (min-width: 1280px) {
    .header .container {
        padding-left: 0px;
        padding-right: 0px;
    }
}

.hero .feature-list li > div {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

@media (min-width: 768px) {
    .hero .feature-list li > div {
        padding-left: 1rem;
        padding-right: 1rem;
        padding-top: 0.375rem;
        padding-bottom: 0.375rem;
    }
}

@media (min-width: 1280px) {
    .hero .feature-list li > div {
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }
}

.hero .student-badge {
    padding-top: 0.375rem;
    padding-bottom: 0.375rem;
    padding-left: 1rem;
    padding-right: 0.5rem;
}

@media (min-width: 640px) {
    .hero .student-badge {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
}

@media (min-width: 768px) {
    .hero .student-badge {
        padding-left: 1rem;
        padding-right: 1rem;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }
}

.drop-shadow {
    filter: drop-shadow(0px 4.6px 4.6px #00000040);
}

.gradient-text {
    background: linear-gradient(90deg, #1540ff 0%, #00238f 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.line-clamp-2 {
    display: -webkit-box;
    line-clamp: 2;
    text-overflow: ellipsis;
}

.line-clamp-3 {
    display: -webkit-box;
    line-clamp: 2;
    text-overflow: ellipsis;
}

.slider-container .slick-dots {
    position: static;
    margin-top: 1rem;
}

.slider-container .slick-dots li button {
    width: 0.625rem;
    height: 0.625rem;
    border-radius: 9999px;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.slider-container .slick-dots li.slick-active button {
    --tw-bg-opacity: 1;
    background-color: rgb(255 181 70 / var(--tw-bg-opacity, 1));
}

.news .slider-container .slick-dots li button {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 9999px;
    --tw-bg-opacity: 1;
    background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}

.news .slider-container .slick-dots li.slick-active button {
    --tw-bg-opacity: 1;
    background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

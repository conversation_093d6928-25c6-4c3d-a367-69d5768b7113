// FakeSeeding constructor function
function FakeSeeding(options = {}) {
    // Create container if not exists
    this.containerId = options.containerId || "noti-seeding";
    this.container = $("#" + this.containerId);
    if (this.container.length === 0) {
        $("body").append(
            `<div class="noti-seeding" id="${this.containerId}"></div>`
        );
        this.container = $("#" + this.containerId);
    }

    // Configuration
    this.showDuration = options.showDuration || 60000; // How long notifications stay visible
    this.minInterval = options.minInterval || 3000; // Minimum time between notifications
    this.maxInterval = options.maxInterval || 10000; // Maximum time between notifications

    // Data
    this.firstNames = [
        "nguyen",
        "hoang",
        "bui",
        "nguyen4",
        "le",
        "pham",
        "luu",
        "nguyen2",
        "luong",
        "pham2",
        "tran",
        "ngo",
        "vuong",
        "vu",
        "ly",
        "phi",
        "ha",
        "doan",
        "la",
        "nguyen3",
        "ngo3",
        "nguyen11",
        "phan",
        "dang2",
        "vu2",
        "vi",
        "nguyen7",
        "phung",
        "dang",
        "nguyen12",
        "to",
        "duong",
        "mai",
        "nguyen5",
        "do",
        "le2",
        "ninh",
        "Nguyen5",
        "nguyen13",
        "ho",
        "ta",
        "nguyen6",
        "ngoc",
        "nguyen14",
        "hoang2",
        "ha2",
    ];

    this.lastNames = [
        "huyentrang",
        "ngoc",
        "hoa",
        "anh",
        "huong",
        "nam",
        "viet",
        "thuy",
        "hanh",
        "giang",
        "ha",
        "hung",
        "ninh",
        "loc",
        "mai",
        "tan",
        "thoa",
        "trang",
        "bich",
        "ngan",
        "phuong",
        "tung",
        "nghia",
        "minh",
        "thanh",
        "hai",
        "duyen",
        "thuong",
        "thinh",
        "duy",
        "trung",
        "rosie",
        "vui",
        "manh",
        "khai",
        "emma",
        "tuan",
        "dang",
        "ty",
        "huyen",
        "luc",
        "tien",
        "yen",
        "thang",
        "hang",
        "toan",
        "cuong",
        "nhan",
        "an",
        "thien",
        "duong",
        "binh",
        "Tuan",
        "chung",
        "chi",
        "sen",
        "tam",
        "vu",
        "quyet",
        "duc",
        "Huyen",
        "giap",
        "uyen",
        "thai",
        "thao",
        "kien",
        "Ban",
        "hien",
        "hoi",
        "truong",
        "dung",
        "my",
        "nga",
        "luu",
        "linh",
        "khanh",
        "xuan",
    ];

    this.domains = [
        "outlook.com",
        "gmail.com",
        "hotmail.com",
        "mail.com",
        "icloud.com",
    ];

    this.avatarColors = ["purple", "blue", "green"];

    this.badges = [
        { type: "free", text: "Học viên Free", class: "badge-free" },
        { type: "pro", text: "Học viên Pro", class: "badge-pro" },
        { type: "premium", text: "Premium", class: "badge-premium" },
        {
            type: "completed",
            text: "Đã hoàn thành",
            class: "badge-completed",
        },
    ];

    this.course_name = $(document).find(".playing-video-title .title").text();

    this.lesson_titles = window?.currentLessonData
        ? window?.currentLessonData?.map((item) => item.title)
        : [];

    this.messages = [
        {
            text: "Chúc mừng bạn vừa đăng ký khóa học thành công!",
            badge: "pro",
        },
        {
            text: "Bạn vừa bắt đầu học bài đầu tiên. Chúc bạn học thật hiệu quả!",
            badge: "free",
        },
        {
            text: "Chào mừng bạn đã quay trở lại với khóa học!",
            badge: "pro",
        },
        {
            text: "Bạn đã thanh toán thành công. Khóa học đã được mở toàn bộ!",
            badge: "pro",
        },
        {
            text: "Chúc mừng bạn đã hoàn thành khóa học! Nhận ngay chứng chỉ & tiếp tục hành trình học tập nhé!",
            badge: "completed",
        },
        {
            text: `Bạn vừa hoàn thành phần học "{{title}}" trong khóa học "${this.course_name}"`,
            badge: null,
            type: "lesson",
        },
    ];
}

// Prototype methods
FakeSeeding.prototype = {
    // Generate random email
    generateRandomEmail: function () {
        const firstName =
            this.firstNames[Math.floor(Math.random() * this.firstNames.length)];
        const lastName =
            this.lastNames[Math.floor(Math.random() * this.lastNames.length)];
        const domain =
            this.domains[Math.floor(Math.random() * this.domains.length)];

        // Create email with masked characters
        const fullName = firstName + lastName;
        const firstChar = fullName.charAt(0);

        // Show first 2-3 characters then mask with asterisks
        const visibleLength = Math.floor(Math.random() * 2) + 2; // 2-3 visible chars
        const maskedEmail =
            fullName.substring(0, visibleLength) + "***@" + domain;

        return {
            firstChar: firstChar,
            email: maskedEmail,
        };
    },

    // Show a single notification
    showNotification: function () {
        const email = this.generateRandomEmail();
        const messageObj =
            this.messages[Math.floor(Math.random() * this.messages.length)];
        const avatarColor =
            this.avatarColors[
                Math.floor(Math.random() * this.avatarColors.length)
            ];
        const badgeObj = messageObj.badge
            ? this.badges.find((b) => b.type === messageObj.badge)
            : null;

        const notification = $('<div class="notification"></div>');

        // Create avatar
        const avatar = $(
            `<div class="avatar avatar-${avatarColor}">${email.firstChar.toUpperCase()}</div>`
        );

        // Create content
        const content = $('<div class="content"></div>');
        const emailElement = $('<div class="email"></div>').html(
            `<div>${email.email}</div>`
        );

        // Create message with optional badge
        const messageElement = $('<div class="message"></div>').text(
            messageObj.text.replace(
                "{{title}}",
                this.lesson_titles[
                    Math.floor(Math.random() * this.lesson_titles.length)
                ]
            )
        );

        // Append elements
        content.append(emailElement);
        content.append(messageElement);

        // Add badge if exists
        if (badgeObj) {
            const badgeElement = $(
                `<span class="badge-seeding ${badgeObj.class}">${badgeObj.text}</span>`
            );
            emailElement.append(badgeElement);
        }

        // Add action icons for some notifications (random)
        if (Math.random() > 0.5) {
            const actionIcons = $('<div class="action-icons"></div>');
            const soundIcon = $(
                '<div class="icon icon-sound"><i class="fas fa-volume-up fa-xs"></i></div>'
            );
            const messengerIcon = $(
                '<div class="icon icon-messenger"><i class="fab fa-facebook-messenger fa-xs"></i></div>'
            );

            actionIcons.append(soundIcon);
            actionIcons.append(messengerIcon);
            content.append(actionIcons);
        }

        // Add achievement message for some notifications (random)
        if (Math.random() > 0.7) {
            const achievement = $('<div class="achievement"></div>').text(
                "🏆 Achievement Unlocked!"
            );
            content.append(achievement);
        }

        notification.append(avatar);
        notification.append(content);

        this.container.append(notification);

        // Show with animation
        setTimeout(() => {
            notification.addClass("show");
        }, 10);

        // Hide and remove after duration
        setTimeout(() => {
            notification.removeClass("show");
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, this.showDuration);
    },

    // Schedule the next notification
    scheduleNext: function () {
        // Random interval between min and max
        const interval =
            Math.floor(
                Math.random() * (this.maxInterval - this.minInterval + 1)
            ) + this.minInterval;

        this.timer = setTimeout(() => {
            this.showNotification();
            this.scheduleNext();
        }, interval);
    },

    // Start showing notifications
    start: function () {
        this.scheduleNext();
        return this; // For chaining
    },

    // Stop showing notifications
    stop: function () {
        if (this.timer) {
            clearTimeout(this.timer);
        }
        return this; // For chaining
    },
};

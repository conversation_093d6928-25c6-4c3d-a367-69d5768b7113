/**
 * WebTourGuide Constructor
 * @param {Array<Object>} steps - Array of tour step objects.
 * Each object: { element: String, placement: String, title: String, content: String }
 * @param {Object} [options] - Optional configuration.
 * @param {String} [options.cookieName='tourCompleted'] - Name of the cookie.
 * @param {String} [options.highlightClass='tour-highlight-no-spotlight'] - CSS class for highlighting.
 * @param {Number} [options.cookieExpiryDays=365] - Days until the cookie expires.
 */
function WebTourGuide(steps, options) {
    // Store 'this' context for use in inner functions and event handlers
    const self = this;

    // Default options
    const defaults = {
        cookieName: "tourCompleted",
        highlightClass: "tour-highlight-no-spotlight",
        cookieExpiryDays: 365,
    };

    // Merge user options with defaults
    self.options = $.extend({}, defaults, options);
    self.tourSteps = steps;
    self.currentStep = 0;

    // Store original positions of elements
    self.originalPositions = new Map();

    // Helper function to safely add highlight class
    self.addHighlightClass = function ($element) {
        if (!$element.length) return;

        // Store original position if not already stored
        if (!self.originalPositions.has($element[0])) {
            const computedStyle = window.getComputedStyle($element[0]);
            self.originalPositions.set($element[0], computedStyle.position);
        }

        // Only add position relative if element doesn't have a position
        const originalPosition = self.originalPositions.get($element[0]);
        if (originalPosition === "static") {
            $element.css("position", "relative");
        }

        $element.addClass(self.options.highlightClass);
    };

    // Helper function to safely remove highlight class
    self.removeHighlightClass = function ($element) {
        if (!$element.length) return;

        $element.removeClass(self.options.highlightClass);

        // Restore original position
        const originalPosition = self.originalPositions.get($element[0]);
        if (originalPosition === "static") {
            $element.css("position", "");
        }
    };

    // --- Cookie Helper Functions ---
    self.setCookie = function (name, value, days) {
        let expires = "";
        if (days) {
            const date = new Date();
            date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    };

    self.getCookie = function (name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(";");
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == " ") c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    };

    // --- Tour Logic ---
    self.start = function () {
        if (self.getCookie(self.options.cookieName)) {
            console.log(
                "Tour already completed (Cookie: " + self.options.cookieName + ")."
            );
            return;
        }
        if (!self.tourSteps || self.tourSteps.length === 0) {
            console.warn("WebTourGuide: No tour steps provided.");
            return;
        }
        $("#tour-overlay").fadeIn();
        $("#tour-popover").fadeIn();
        self.showStep(self.currentStep);
    };

    self.showStep = function (index) {
        const step = self.tourSteps[index];
        if (!step || !$(step.element).length) {
            console.warn("Tour step element not found or step undefined:", step);
            self.end(false);
            return;
        }

        const $targetElement = $(step.element);

        $("#tour-title").text(step.title);
        $("#tour-content").text(step.content);
        $("#tour-step-counter").text(`${index + 1}/${self.tourSteps.length}`);

        self.addHighlightClass($targetElement);
        $targetElement[0].scrollIntoView({
            behavior: "smooth",
            block: "center",
            inline: "center",
        });

        setTimeout(() => {
            self.positionPopover($targetElement, step.placement);
        }, 150);

        $("#tour-prev")
            .prop("disabled", index === 0)
            .toggleClass("opacity-50 cursor-not-allowed", index === 0);
        if (index === self.tourSteps.length - 1) {
            $("#tour-next").hide();
            $("#tour-done").show();
        } else {
            $("#tour-next").show();
            $("#tour-done").hide();
        }
    };

    self.positionPopover = function ($targetElement, placement) {
        const $popover = $("#tour-popover");
        if (!$targetElement.length || !$popover.length) return;

        const targetOffset = $targetElement.offset();
        const targetWidth = $targetElement.outerWidth();
        const targetHeight = $targetElement.outerHeight();
        const popoverWidth = $popover.outerWidth();
        const popoverHeight = $popover.outerHeight();
        const arrowSize = 10;

        let top, left;

        $popover.removeClass(
            "placement-top placement-bottom placement-left placement-right"
        );

        switch (placement) {
            case "top":
                top = targetOffset.top - popoverHeight - arrowSize;
                left = targetOffset.left + targetWidth / 2 - popoverWidth / 2;
                $popover.addClass("placement-top");
                break;
            case "bottom":
                top = targetOffset.top + targetHeight + arrowSize;
                left = targetOffset.left + targetWidth / 2 - popoverWidth / 2;
                $popover.addClass("placement-bottom");
                break;
            case "left":
                top = targetOffset.top + targetHeight / 2 - popoverHeight / 2;
                left = targetOffset.left - popoverWidth - arrowSize;
                $popover.addClass("placement-left");
                break;
            case "right":
                top = targetOffset.top + targetHeight / 2 - popoverHeight / 2;
                left = targetOffset.left + targetWidth + arrowSize;
                $popover.addClass("placement-right");
                break;
            default:
                top = targetOffset.top + targetHeight + arrowSize;
                left = targetOffset.left + targetWidth / 2 - popoverWidth / 2;
                $popover.addClass("placement-bottom");
        }

        const screenPadding = 10;
        if (left < screenPadding) left = screenPadding;
        if (top < screenPadding) top = screenPadding;
        if (left + popoverWidth > $(window).width() - screenPadding) {
            left = $(window).width() - popoverWidth - screenPadding;
        }
        if (top + popoverHeight > $(window).height() - screenPadding) {
            top = $(window).height() - popoverHeight - screenPadding;
        }

        $popover.css({ top: top, left: left });
    };

    self.end = function (setCookieFlag = true) {
        if (setCookieFlag) {
            self.setCookie(
                self.options.cookieName,
                "true",
                self.options.cookieExpiryDays
            );
        }
        $("#tour-overlay").fadeOut();
        $("#tour-popover").fadeOut();

        if (
            self.tourSteps[self.currentStep] &&
            $(self.tourSteps[self.currentStep].element).length
        ) {
            self.removeHighlightClass($(self.tourSteps[self.currentStep].element));
        }
        self.tourSteps.forEach((step) => {
            if (step && $(step.element).length) {
                self.removeHighlightClass($(step.element));
            }
        });
        // Clear stored positions
        self.originalPositions.clear();
    };

    // --- Event Handlers ---
    $("#tour-next").on("click.webtour", function () {
        if (self.currentStep < self.tourSteps.length - 1) {
            if (
                self.tourSteps[self.currentStep] &&
                $(self.tourSteps[self.currentStep].element).length
            ) {
                self.removeHighlightClass($(self.tourSteps[self.currentStep].element));
            }
            self.currentStep++;
            self.showStep(self.currentStep);
        }
    });

    $("#tour-prev").on("click.webtour", function () {
        if (self.currentStep > 0) {
            if (
                self.tourSteps[self.currentStep] &&
                $(self.tourSteps[self.currentStep].element).length
            ) {
                self.removeHighlightClass($(self.tourSteps[self.currentStep].element));
            }
            self.currentStep--;
            self.showStep(self.currentStep);
        }
    });

    $("#tour-done").on("click.webtour", function () {
        self.end();
    });

    $("#tour-skip").on("click.webtour", function () {
        self.end();
    });

    $(window).on("resize.webtour", function () {
        if ($("#tour-popover").is(":visible") && self.tourSteps[self.currentStep]) {
            const $targetElement = $(self.tourSteps[self.currentStep].element);
            if ($targetElement.length) {
                self.positionPopover(
                    $targetElement,
                    self.tourSteps[self.currentStep].placement
                );
            }
        }
    });

    // Automatically start the tour when a new instance is created
    // You might want to call this manually depending on your application flow
    // self.start();
}

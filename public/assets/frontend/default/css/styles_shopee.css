@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

html {
	font-size: 14px;
	font-family: Inter, sans-serif;
}

:root {
	--font-family: Inter, sans-serif;
}

.header {
	background: white;
	padding: 1rem 0;
}

.header .container .btn {
	background: linear-gradient(to right, #ef4444, #f59e0b);
	border: 0;
	border-radius: 0.5rem;
	padding: 1rem 1.5rem;
	font-size: 1.25rem;
	line-height: 1.25;
	color: white;
}

.header img {
	width: 240px;
}

@media (max-width: 1280px) {
	.header img {
		width: 200px;
	}

	.header .container .btn {
		font-size: 16px;
	}
}

@media (max-width: 768px) {
	.header img {
		width: 140px;
	}

	.header .container .btn {
		font-size: 1rem;
		padding: 0.75rem 1rem;
	}
}

.hero {
	background: linear-gradient(180deg, #232c75 0%, #0f35ad 100%);
}

.hero .container {
	padding-top: 84px;
	padding-bottom: 300px;
}

.hero .container .main-heading {
	max-width: 966px;
	margin: 0 auto;
	margin-bottom: 1.25rem;
	color: #ffbc01;
	text-align: center;
	font-variant-numeric: lining-nums proportional-nums;
	font-size: 50px;
	font-style: normal;
	font-weight: 700;
	line-height: normal;
}

.hero .container .sub-heading {
	max-width: 577px;
	margin: 0 auto;
	margin-bottom: 40px;
	color: #fff;
	text-align: center;
	font-variant-numeric: lining-nums proportional-nums;
	font-size: 22px;
	font-style: normal;
	font-weight: 500;
	line-height: 28px; /* 127.273% */
}

.hero .action-buttons {
	display: flex;
	justify-content: center;
	gap: 0.75rem;
}

.hero .action-buttons button {
	color: white;
}

.hero .action-buttons .buy-course-btn {
	padding-left: 0;
	border-radius: 99999px;
	border: 1px solid #ffa688;
	background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
	box-shadow: 0px -1px 11px 0px #ffa868 inset;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 5px;
	min-width: 210px;
	font-variant-numeric: lining-nums proportional-nums;
	font-weight: 800;
}

.hero .action-buttons .buy-course-btn-icon {
	padding: 10px;
	border-radius: 9999px;
	background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
}

.hero .action-buttons .try-free-btn {
	padding-left: 0;
	border-radius: 99999px;
	border: 1px solid #89b3ff;
	background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
	box-shadow: 0px -1px 11px 0px #688bff inset;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 5px;
	min-width: 210px;
	font-variant-numeric: lining-nums proportional-nums;
	font-weight: 800;
}

.hero .action-buttons .try-free-btn-icon {
	padding: 10px;
	border-radius: 9999px;
	border-radius: 56px;
	background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

@media (max-width: 1280px) {
	.hero .container .main-heading {
		font-size: 3.5rem;
	}

	.hero .container .sub-heading {
		font-size: 1.5rem;
	}

	.hero .action-buttons .try-free-btn,
	.hero .action-buttons .buy-course-btn {
		font-size: 1rem;
	}
}

@media (max-width: 1024px) {
	.hero .container .main-heading {
		font-size: 2.75rem;
	}

	.hero .container .sub-heading {
		font-size: 1.25rem;
	}
}

@media (max-width: 768px) {
	.hero .container .main-heading {
		font-size: 2rem;
	}

	.hero .container .sub-heading {
		font-size: 1rem;
	}
}

@media (max-width: 640px) {
	.hero .action-buttons {
		gap: 0.25rem;
	}

	.hero .action-buttons .try-free-btn,
	.hero .action-buttons .buy-course-btn {
		font-size: 0.75rem;
		min-width: 150px;
	}
}

.main {
	background: #f2f5f9;
}

.main > .container {
	background-color: white;
	border-radius: 20px;
	transform: translateY(-226px);
	padding: 12px 13px;
	padding-bottom: 50px;
}

@media (max-width: 1280px) {
	.main {
		padding: 0 1.5rem;
	}
}

.main .youtube-video-container > .video-container {
	position: relative;
	aspect-ratio: 16 / 9;
	overflow: hidden;
	border-radius: 15px;
}

.main .youtube-video-container > .video-container .poster-wrapper {
	position: absolute;
	inset: 0px;
	z-index: 10;
}

.main .youtube-video-container > .video-container .poster-wrapper .play-button-container {
	position: absolute;

	inset: 0px;

	display: flex;

	align-items: center;

	justify-content: center;
}

.main
	.youtube-video-container
	> .video-container
	.poster-wrapper
	.play-button-container
	.play-button {
	display: flex;

	height: 92px;

	width: 92px;

	align-items: center;

	justify-content: center;

	border-radius: 9999px;

	background-color: transparent;

	transition-property: transform;

	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);

	transition-duration: 300ms;
	border: none;
}

.main
	.youtube-video-container
	> .video-container
	.poster-wrapper
	.play-button-container
	.play-button:hover {
	transform: scaleX(1.1) scaleY(1.1);
}

.main .youtube-video-container > .video-container .iframe-container {
	display: none;
	width: 100%;
	height: 100%;
}

.main .youtube-video-container > .video-container .iframe-container.active {
	display: block;
}

.main .youtube-video-container > .video-container .iframe-container.active iframe {
	position: absolute;

	inset: 0px;

	height: 100%;

	width: 100%;
}

@media (max-width: 640px) {
	.main
		.youtube-video-container
		> .video-container
		.poster-wrapper
		.play-button-container
		.play-button {
		height: 4rem;
		width: 4rem;
	}
}

.main .solution > .container {
	margin-top: 2rem;
}

.main .solution > .container .pill {
	width: 192px;
	margin: 0 auto;
	padding: 0.5rem 0;
	border-radius: 9999px;
	background: linear-gradient(270deg, #0043c0 0%, #064ed0 100%);

	color: var(--White-color, #fff);
	text-align: center;
	font-variant-numeric: lining-nums proportional-nums;

	font-size: 20.97px;
	font-style: normal;
	font-weight: 800;
	line-height: 22.13px; /* 105.53% */
}

.main .solution > .container .title {
	color: #1a1d24;
	text-align: center;
	font-variant-numeric: lining-nums proportional-nums;

	font-size: 32px;
	font-style: normal;
	font-weight: 700;
	line-height: 52px; /* 162.5% */
	margin-bottom: 0.25rem;
}

.main .solution > .container .sub-title {
	color: #333;
	text-align: center;
	font-size: 17px;
	font-style: normal;
	font-weight: 400;
	line-height: 24.089px; /* 141.698% */
	margin-bottom: 3rem;
}

.main .solution > .container .shopee-course-interface .module-card {
	border-radius: 10px;
	margin-bottom: 15px;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
	overflow: hidden;
}

.main .solution > .container .shopee-course-interface .course-container {
	display: flex;
	gap: 24px;
	align-items: flex-start;
}

.main .solution > .container .shopee-course-interface .course-list,
.main .solution > .container .shopee-course-interface .course-sale {
	width: 50%;
}
.main .solution > .container .shopee-course-interface .course-sale {
	border-radius: 8.315px;
	background: #fff;
	box-shadow: 0px 0px 30px 0px rgba(0, 0, 0, 0.1);
	padding: 0 20px;
}

.main
	.solution
	> .container
	.shopee-course-interface
	.module-card
	> div[aria-expanded="false"]
	.toggle-btn-icon-collapse {
	display: none;
}

.main
	.solution
	> .container
	.shopee-course-interface
	.module-card
	> div[aria-expanded="true"]
	.toggle-btn-icon-expand {
	display: none;
}

.free-badge {
	border: 1px solid #89b3ff;
	background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

.pro-trial-badge {
	border: 1px solid #ffa688;
	background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
}

.important-badge {
	border: 1px solid #89b3ff;
	background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

.premium-trial-badge {
	border: 1px solid #ffa688;
	background: linear-gradient(270deg, #000 0%, #eb2b05 100%);
}

.main .solution > .container .shopee-course-interface .module-item {
	background: #670a00;
	border-radius: 10px;
	overflow: hidden;
	margin-bottom: 15px;
	border-bottom: none;
	box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.05);
}

.main .solution > .container .shopee-course-interface .module-header {
	cursor: pointer;
	border-radius: 8px;
	border: 1px solid #fff;
	background: #fff2e4;
}

.main .solution > .container .shopee-course-interface .module-header > div {
	width: 100%;
	padding: 8px 14px 8px 17px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.main .solution > .container .shopee-course-interface .module-item.active .module-header {
	background-color: #fa8128;
	border-radius: 15px 15px 0 0;
}

.main .solution > .container .shopee-course-interface .module-item.active .module-title h3,
.main .solution > .container .shopee-course-interface .module-item.active .module-details,
.main .solution > .container .shopee-course-interface .module-item.active .module-details span {
	color: #fff;
}

.main
	.solution
	> .container
	.shopee-course-interface
	.module-header
	> div[aria-expanded="true"]
	h3 {
	color: #dc3f2e;
}

.main
	.solution
	> .container
	.shopee-course-interface
	.module-header
	> div[aria-expanded="true"]
	.toggle-btn-icon-expand {
	display: none;
}

.main
	.solution
	> .container
	.shopee-course-interface
	.module-header
	> div[aria-expanded="false"]
	.toggle-btn-icon-collapse {
	display: none;
}

.main .solution > .container .shopee-course-interface .module-title h3 {
	font-weight: 700;
	color: #333;
	font-size: 17px;
	font-style: normal;
	font-weight: 700;
	line-height: 19px;
}

.main .solution > .container .shopee-course-interface .module-details {
	display: flex;
	gap: 10px;
	color: #666;
	font-size: 14px;
}

.main .solution > .container .shopee-course-interface .module-details > span {
	display: inline-flex;
	align-items: center;
}

.main .solution > .container .shopee-course-interface .module-details span {
	color: #333;
	font-variant-numeric: lining-nums proportional-nums;
	font-family: Inter, sans-serif;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 20px; /* 142.857% */
}

.main .solution > .container .shopee-course-interface .module-details strong {
	font-weight: 800;
}

.main .solution > .container .shopee-course-interface .video-count img,
.main .solution > .container .shopee-course-interface .duration img {
	margin-right: 5px;
}

.main .solution > .container .shopee-course-interface .module-content {
	background-color: #670a00;
	color: white;
	padding: 0;
}

.main .solution > .container .shopee-course-interface .module-content .accordion-body {
	padding: 16px 16px 28px;
}

.main .solution > .container .shopee-course-interface .module-content .module-content-description {
	color: var(--White-color, #fff);

	font-size: 1.125rem;
	font-style: normal;
	font-weight: 400;
	line-height: 18.881px; /* 133.333% */
	margin-bottom: 10px;
}

.main .solution > .container .shopee-course-interface .lesson-item {
	padding-bottom: 14px;
	margin-bottom: 14px;
	border-bottom: 1px solid #a61808;
}

.main .solution > .container .shopee-course-interface .lesson-item:last-child {
	margin-bottom: 0;
	padding-bottom: 0;
	border-bottom: none;
}

.main .solution > .container .shopee-course-interface .lesson-item-title {
	color: #fff;
	font-variant-numeric: lining-nums proportional-nums;
	font-family: Inter, sans-serif;
	font-size: 15px;
	font-style: normal;
	font-weight: 700;
	line-height: 18px; /* 112.5% */
	min-height: 24px;
	margin-bottom: 8px;
}

.main .solution > .container .shopee-course-interface .lesson-item > div {
	display: flex;
	justify-content: flex-start;
	align-items: center;
}

.main .solution > .container .shopee-course-interface .badge {
	height: 20px;
	border-radius: 5px;
	color: white;
	display: flex;
	align-items: center;
	gap: 4px;
	padding: 0 9px 0 4px;
	margin-right: 9px;
}

.main .solution > .container .shopee-course-interface .duration-small {
	display: flex;
	color: white;
	align-items: center;
	font-variant-numeric: lining-nums proportional-nums;
	font-family: Inter, sans-serif;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 20px;
}

.main .solution > .container .shopee-course-interface .duration-small img {
	margin-right: 4px;
	filter: brightness(0) invert(1);
}

.main .solution > .container .shopee-course-interface .video-info {
	display: flex;
	align-items: center;
	font-size: 13px;
	margin-top: 5px;
	color: rgba(255, 255, 255, 0.8);
}

.main .solution > .container .shopee-course-interface .video-info i {
	margin-right: 5px;
}

.main .solution > .container .shopee-course-interface .video-info span {
	margin-right: 15px;
}

.main .solution > .container .shopee-course-interface .free-tag {
	background-color: #ff6a00;
	color: white;
	padding: 2px 8px;
	border-radius: 3px;
	font-size: 11px;
	font-weight: bold;
	display: inline-block;
	margin-right: 10px;
}

.main .solution > .container .shopee-course-interface .btn-view {
	background-color: #ff6a00;
	color: white;
	padding: 2px 10px;
	border-radius: 3px;
	font-size: 12px;
	border: none;
	margin-right: 10px;
}

.main .solution > .container .shopee-course-interface .btn-continue {
	background-color: #0d6efd;
	color: white;
	padding: 2px 10px;
	border-radius: 3px;
	font-size: 12px;
	border: none;
}

.main .solution > .container .shopee-course-interface .zoom-badge {
	color: white;
	font-weight: 500;
	margin-bottom: 15px;
	width: fit-content;
	margin: 12px auto;
	display: flex;
	align-items: center;
	gap: 0.375rem;

	color: #008dff;
	font-kerning: none;
	font-variant-numeric: lining-nums proportional-nums;
	font-size: 18px;
	font-style: normal;
	font-weight: 800;
	line-height: 1.3; /* 130% */
}

.main .solution > .container .shopee-course-interface .register-card {
	border-radius: 20px;
	background: #fff2e4;
	padding: 24px 16px 32px;
	text-align: center;
}

.main .solution > .container .shopee-course-interface .register-title {
	color: #333;
	text-align: center;
	font-kerning: none;
	font-variant-numeric: lining-nums proportional-nums;
	font-size: 22px;
	font-style: normal;
	font-weight: 800;
	line-height: 26.5px; /* 120.455% */
}

.main .solution > .container .shopee-course-interface .register-subtitle {
	color: #eb2805;
	font-kerning: none;
	font-variant-numeric: lining-nums proportional-nums;
	font-size: 22px;
	font-style: normal;
	font-weight: 800;
	line-height: 26.5px;
	margin-bottom: 22px;
}

.main .solution > .container .shopee-course-interface .countdown-container {
	border-radius: 18px;
	padding: 4px 32px 8px;
	background: #fff;
	margin: 0 auto;
	margin-bottom: 20px;
	width: fit-content;
}

.main .solution > .container .shopee-course-interface .countdown-text {
	color: #333;
	text-align: center;
	font-size: 19px;
	font-style: normal;
	font-weight: 400;
	line-height: 26.27px; /* 137.5% */
	margin-bottom: 0.25rem;
}

.main .solution > .container .shopee-course-interface .countdow-content {
	color: #105ce4;
	text-align: center;
	font-variant-numeric: lining-nums proportional-nums;
	font-size: 38px;
	font-style: normal;
	font-weight: 900;
	line-height: 0.875; /* 87.5% */
	letter-spacing: 2.293px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.main .solution > .container .shopee-course-interface .price-container {
	position: relative;
}

.main .solution > .container .shopee-course-interface .original-price {
	position: absolute;

	left: 2.5rem;

	top: -30px;

	display: inline-block;

	border-radius: 9999px;

	background-color: #0240b6;

	padding-left: 1.5rem;

	padding-right: 1.5rem;

	padding-top: 0.5rem;

	padding-bottom: 0.5rem;

	font-size: 1.875rem;

	line-height: 2.25rem;
	font-style: italic;

	font-weight: 700;

	color: #fff;

	text-decoration-line: line-through;
}

.main .solution > .container .shopee-course-interface .current-price-container {
	margin-top: 50px;
	margin-right: 50px;

	display: flex;

	align-items: flex-end;

	justify-content: center;

	border-radius: 9999px 0 0 9999px;
	background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);

	padding: 1.5rem;

	text-align: center;

	font-size: 1.25rem;

	line-height: 1.75rem;

	font-style: italic;

	color: rgb(255, 255, 255);
}

.main .solution > .container .shopee-course-interface .current-price-label {
	margin-right: 0.5rem;

	white-space: nowrap;
}

.main .solution > .container .shopee-course-interface .current-price {
	border-width: 0px;

	padding-top: 0px;

	padding-bottom: 0px;

	text-align: center;

	font-size: 35px;
	line-height: 1;

	font-weight: 700;

	font-style: italic;

	color: rgb(255, 255, 255);

	text-shadow: 1px 2px 1px rgb(0, 32, 132, 1);
}

.main .solution > .container .shopee-course-interface .hot-deal {
	position: absolute;
	right: 0px;
	bottom: 0;
}

.main .solution > .container .shopee-course-interface .hot-deal img {
	width: 100px;
	height: auto;
}

.main .solution > .container .shopee-course-interface .btn-icon {
	margin-right: 5px;
}

.main .solution > .container .shopee-course-interface .info-section {
	margin-top: 20px;
	text-align: left;
}

.main .solution > .container .shopee-course-interface .info-title {
	display: flex;
	justify-content: flex-start;
	align-items: center;
	gap: 4px;

	color: #333;
	font-variant-numeric: lining-nums proportional-nums;
	font-size: 16px;
	font-style: normal;
	font-weight: 700;
	line-height: 18.104px; /* 113.148% */

	margin-bottom: 1rem;
}

.main .solution > .container .shopee-course-interface .info-list {
	list-style: disc;
	padding-left: 1.5rem;
}
.main .solution > .container .shopee-course-interface .info-item {
	color: #666;
	font-size: 15px;
	font-style: normal;
	font-weight: 400;
	line-height: 17.198px; /* 114.656% */
}

.main .solution > .container .shopee-course-interface .module-meta {
	display: flex;
	align-items: center;
	font-size: 13px;
	color: #777;
	margin-top: 5px;
}

.main .solution > .container .shopee-course-interface .module-meta i {
	margin-right: 5px;
	color: #ff6a00;
}

.main .solution > .container .shopee-course-interface .module-meta span {
	margin-right: 15px;
}

.main .solution > .container .shopee-course-interface .toggle-icon {
	color: #ff6a00;
	font-size: 18px;
}

.main .solution > .container .shopee-course-interface .action-buttons {
	justify-content: center;
	gap: 5px;
	margin-top: 22px;
	margin-bottom: 2rem;
}

.main .solution > .container .shopee-course-interface .buy-course-btn,
.main .solution > .container .shopee-course-interface .try-free-btn {
	border: none;

	padding-left: 0px;
	border-radius: 50px;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	font-weight: 700;
	gap: 10px;
	width: 50%;
	cursor: pointer;

	color: #fff;
	text-align: center;
	font-variant-numeric: lining-nums proportional-nums;
	font-size: 18.63px;
	font-style: normal;
	font-weight: 800;
	line-height: 27.945px; /* 150% */
}

.main .solution > .container .shopee-course-interface .buy-course-btn {
	border-radius: 9999px;
	border: 1px solid #ffa688;
	background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
	box-shadow: 0px -1px 11px 0px #ffa868 inset;
}

.main .solution > .container .shopee-course-interface .buy-course-btn-icon {
	background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
}

.main .solution > .container .shopee-course-interface .try-free-btn {
	border-radius: 9999px;
	background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

.main .solution > .container .shopee-course-interface .try-free-btn-icon {
	background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

.main .solution > .container .shopee-course-interface .buy-course-btn-icon,
.main .solution > .container .shopee-course-interface .try-free-btn-icon {
	border-radius: 50%;
	padding: 10px;
	width: fit-content;
	flex-shrink: 0;
}

.main .solution > .container .shopee-course-interface .buy-course-btn > span:last-child,
.main .solution > .container .shopee-course-interface .try-free-btn > span:last-child {
	display: inline-block;
	width: 100%;
}

@media (max-width: 1280px) {
	.main .solution > .container .pill {
		font-size: 1.5rem;
		line-height: 1.25;
	}

	.main .solution > .container .title {
		font-size: 2rem;
		margin-top: 0.5rem;
		line-height: 1.5;
	}

	.main .solution > .container .sub-title {
		font-size: 1.5rem;
		line-height: 1.125;
		margin-bottom: 2rem;
	}

	.main .solution > .container .shopee-course-interface .module-title h3 {
		font-size: 1.125rem;
		line-height: 1.25;
	}

	.main
		.solution
		> .container
		.shopee-course-interface
		.module-content
		.module-content-description {
		font-size: 1rem;
		line-height: 1.25;
	}

	.main .solution > .container .shopee-course-interface .lesson-item-title {
		font-size: 1rem;
		line-height: 1.25;
		min-height: unset;
	}

	.main .solution > .container .shopee-course-interface .zoom-badge {
		font-size: 1.25rem;
	}

	.main .solution > .container .shopee-course-interface .register-title,
	.main .solution > .container .shopee-course-interface .register-subtitle {
		font-size: 1.5rem;
	}

	.main .solution > .container .shopee-course-interface .register-subtitle {
		margin-bottom: 1.25rem;
	}

	.main .solution > .container .shopee-course-interface .countdown-text {
		font-size: 1.25rem;
		line-height: 1.25;
	}

	.main .solution > .container .shopee-course-interface .countdow-content {
		font-size: 2.5rem;
	}

	.main .solution > .container .shopee-course-interface .original-price {
		font-size: 1.75rem;
	}

	.main .solution > .container .shopee-course-interface .current-price {
		font-size: 2.5rem;
	}

	.main .solution > .container .shopee-course-interface .current-price-container {
		margin-right: 0;
		border-radius: 9999px;
	}

	.main .solution > .container .shopee-course-interface .original-price {
		left: 2rem;
	}

	.main .solution > .container .shopee-course-interface .hot-deal {
		transform: translateY(-65%) translateX(25%);
		width: 75px;
	}

	.main .solution > .container .shopee-course-interface .info-item {
		font-size: 1rem;
		line-height: 1.25rem;
	}

	.main .solution > .container .shopee-course-interface .register-card {
		padding-bottom: 24px;
	}

	.main .solution > .container .shopee-course-interface .buy-course-btn,
	.main .solution > .container .shopee-course-interface .try-free-btn {
		font-size: 1rem;
		line-height: 1.5;
	}
}

@media (max-width: 1024px) {
	.main .solution > .container .shopee-course-interface .course-container {
		flex-direction: column;
	}

	.main .solution > .container .shopee-course-interface .course-list,
	.main .solution > .container .shopee-course-interface .course-sale {
		width: 100%;
	}

	.main .solution > .container .shopee-course-interface .hot-deal {
		transform: translateY(-50%) translateX(25%);
	}
}

@media (max-width: 768px) {
	.main .solution > .container .shopee-course-interface .countdown-container {
		padding: 8px 32px;
	}
}

@media (max-width: 640px) {
	.main .solution > .container {
		padding: 0;
	}
	.main .solution > .container .pill {
		font-size: 1rem;
		width: 140px;
	}

	.main .solution > .container .title {
		font-size: 1.5rem;
	}

	.main .solution > .container .sub-title {
		font-size: 1rem;
		margin-bottom: 1.5rem;
	}

	.main .solution > .container .shopee-course-interface .module-title h3 {
		font-size: 1rem;
	}

	.main .solution > .container .shopee-course-interface .module-details {
		margin-top: 0.25rem;
		flex-direction: column;
		gap: 0rem;
	}

	.main .solution > .container .shopee-course-interface .badge {
		margin-right: 0;
	}

	.main .solution > .container .shopee-course-interface .lesson-link > div {
		flex-wrap: wrap;
		gap: 0.25rem;
	}

	.main .solution > .container .shopee-course-interface .action-buttons {
		flex-direction: column;
	}

	.main .solution > .container .shopee-course-interface .buy-course-btn,
	.main .solution > .container .shopee-course-interface .try-free-btn {
		width: 100%;
	}

	.main .solution > .container .shopee-course-interface .original-price {
		font-size: 1.25rem;
		line-height: 1;
		top: -22px;
		left: 1rem;
	}

	.main .solution > .container .shopee-course-interface .current-price-container {
		padding: 1rem 1.25rem;
		font-size: 1rem;
		line-height: 1.25;
	}

	.main .solution > .container .shopee-course-interface .current-price {
		font-size: 1.5rem;
	}

	.main .solution > .container .shopee-course-interface .hot-deal {
		transform: translateY(-35%) translateX(30%);
		width: 60px;
	}
}

.main .faq > .container .shopee-faq-section .main-title {
	margin-top: 40px;
	margin-bottom: 30px;
	color: #1a1d24;
	text-align: center;
	font-variant-numeric: lining-nums proportional-nums;
	font-size: 32px;
	font-style: normal;
	font-weight: 700;
	line-height: 52px; /* 162.5% */
}

.main .faq > .container .shopee-faq-section .faq-item {
	border-radius: 5.5px;
	background: #fff;
	box-shadow: 0px 7px 30px 0px rgba(0, 0, 0, 0.1);
	margin-bottom: 15px;
	overflow: hidden;
}

.main .faq > .container .shopee-faq-section .faq-question {
	padding: 20px;
	cursor: pointer;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border: none;
	background: none;
	width: 100%;
	text-align: left;
}

.main .faq > .container .shopee-faq-section .faq-question:focus {
	outline: none;
}

.main .faq > .container .shopee-faq-section .question-text {
	color: #333;
	font-variant-numeric: lining-nums proportional-nums;
	font-size: 17px;
	font-style: normal;
	font-weight: 700;
	line-height: 21.899px; /* 128.817% */
	margin: 0;
	transition: color 0.3s ease;
}

/* Highlight the active item's title */
.main .faq > .container .shopee-faq-section .faq-item.active .question-text {
	color: #dc3f2e;
	font-weight: 600;
}

.main .faq > .container .shopee-faq-section .faq-answer {
	padding: 0 50px 20px 20px;
	color: #333;
	text-align: justify;
	font-size: 17px;
	font-style: normal;
	font-weight: 400;
	line-height: 24.089px; /* 141.698% */
}

.main .faq > .container .shopee-faq-section .faq-answer.collapsed {
	display: none;
}

.main .faq > .container .shopee-faq-section .faq-item.active .toggle-btn {
	transition: transform 0.3s ease;
	transform: rotate(180deg);
}

.main .faq > .container .shopee-faq-section .faq-item .toggle-btn {
	width: 20px;
	flex-shrink: 0;
}

.main
	.faq
	> .container
	.shopee-faq-section
	.faq-item.active
	.toggle-btn-icon.toggle-btn-icon-expand {
	display: block;
}
.main .faq > .container .shopee-faq-section .faq-item .toggle-btn-icon.toggle-btn-icon-expand {
	display: none;
}

.main .faq > .container .shopee-faq-section .faq-item .toggle-btn-icon.toggle-btn-icon-collapse {
	display: block;
}
.main
	.faq
	> .container
	.shopee-faq-section
	.faq-item.active
	.toggle-btn-icon.toggle-btn-icon-collapse {
	display: none;
}

.main .faq > .container .shopee-faq-section hr {
	margin: 0;
	opacity: 0.1;
}

@media (max-width: 640px) {
	.faq .container {
		padding: 0;
	}

	.main .faq > .container .shopee-faq-section .main-title {
		font-size: 1.5rem;
		margin: 1.5rem 0;
	}

	.main .faq > .container .shopee-faq-section .faq-item .question-text {
		font-size: 1.15rem;
	}

	.main .faq > .container .shopee-faq-section .faq-answer {
		font-size: 1rem;
	}
}

.footer {
	margin-top: -20px;
	background: linear-gradient(0deg, #232c75 0%, #0f35ad 100%);
	padding: 1rem 0;
}

.footer img {
	width: 200px;
	height: auto;
	object-fit: cover;

	filter: url(#faded-comp-l4og338c);
	object-position: center;
}

@media (max-width: 640px) {
	.footer {
		margin-top: -50px;
	}
}

#tour-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    display: none;
}

#tour-popover {
    position: absolute;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
    min-width: 280px;
    max-width: 350px;
    display: none;
    padding: 20px;
}

#tour-popover::before {
    content: "";
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
}

#tour-popover.placement-top::before {
    border-width: 10px 10px 0 10px;
    border-color: white transparent transparent transparent;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

#tour-popover.placement-bottom::before {
    border-width: 0 10px 10px 10px;
    border-color: transparent transparent white transparent;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

#tour-popover.placement-left::before {
    border-width: 10px 0 10px 10px;
    border-color: transparent transparent transparent white;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
}

#tour-popover.placement-right::before {
    border-width: 10px 10px 10px 0;
    border-color: transparent white transparent transparent;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
}

.tour-highlight-no-spotlight {
    /*position: relative;*/
    z-index: 9999 !important;
    outline: 2px solid #3b82f6; /* Blue outline */
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    border-radius: 4px;
}
#tour-skip {
    border: none;
    width: 30px;
    height: 30px;
    background: none;
}

.guide-btn button {border: none;cursor: pointer;padding: 5px 15px !important;}

/*tailwindcss generate*/
:root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
    monospace;
    --color-yellow-500: oklch(79.5% 0.184 86.047);
    --color-green-500: oklch(72.3% 0.219 149.579);
    --color-green-600: oklch(62.7% 0.194 149.214);
    --color-blue-500: oklch(62.3% 0.214 259.815);
    --color-blue-600: oklch(54.6% 0.245 262.881);
    --color-gray-200: oklch(92.8% 0.006 264.531);
    --color-gray-400: oklch(70.7% 0.022 261.325);
    --color-gray-500: oklch(55.1% 0.027 264.364);
    --color-gray-600: oklch(44.6% 0.03 256.802);
    --color-gray-700: oklch(37.3% 0.034 259.733);
    --color-gray-800: oklch(27.8% 0.033 256.848);
    --color-white: #fff;
    --spacing: 0.25rem;
    --text-xs: 0.75rem;
    --text-xs--line-height: calc(1 / 0.75);
    --text-sm: 0.875rem;
    --text-sm--line-height: calc(1.25 / 0.875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --font-weight-semibold: 600;
    --radius-md: 0.375rem;
    --default-transition-duration: 150ms;
    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
}

.absolute {
    position: absolute;
}
.top-2 {
    top: calc(var(--spacing) * 2);
}
.right-3 {
    right: calc(var(--spacing) * 3);
}
.mt-auto {
    margin-top: auto;
}
.mr-2 {
    margin-right: calc(var(--spacing) * 2);
}
.mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
}
.mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
}
.flex {
    display: flex;
}
.flex-col {
    flex-direction: column;
}
.items-center {
    align-items: center;
}
.justify-between {
    justify-content: space-between;
}
.rounded-md {
    border-radius: var(--radius-md);
}
.bg-blue-500 {
    background-color: var(--color-blue-500);
}
.bg-green-500 {
    background-color: var(--color-green-500);
}
.px-3 {
    padding-inline: calc(var(--spacing) * 3);
}
.px-4 {
    padding-inline: calc(var(--spacing) * 4);
}
.py-1 {
    padding-block: calc(var(--spacing) * 1);
}
.text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
}
.text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
}
.text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
}
.text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
}
.text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
}
.font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
}
.text-gray-400 {
    color: var(--color-gray-400);
}
.text-gray-500 {
    color: var(--color-gray-500);
}
.text-gray-600 {
    color: var(--color-gray-600);
}
.text-gray-700 {
    color: var(--color-gray-700);
}
.text-gray-800 {
    color: var(--color-gray-800);
}
.text-white {
    color: var(--color-white);
}
.text-yellow-500 {
    color: var(--color-yellow-500);
}
.shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
}
.duration-150 {
    --tw-duration: 150ms;
    transition-duration: 150ms;
}
.hover\:bg-blue-600 {
    &:hover {
        @media (hover: hover) {
            background-color: var(--color-blue-600);
        }
    }
}
.hover\:bg-gray-200 {
    &:hover {
        @media (hover: hover) {
            background-color: var(--color-gray-200);
        }
    }
}
.hover\:bg-green-600 {
    &:hover {
        @media (hover: hover) {
            background-color: var(--color-green-600);
        }
    }
}
.hover\:text-gray-600 {
    &:hover {
        @media (hover: hover) {
            color: var(--color-gray-600);
        }
    }
}
.hover\:text-gray-800 {
    &:hover {
        @media (hover: hover) {
            color: var(--color-gray-800);
        }
    }
}

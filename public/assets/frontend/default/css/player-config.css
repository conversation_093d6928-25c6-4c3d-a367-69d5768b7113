@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");
/*.row {*/
/*    --bs-gutter-x: 0 ;*/
/*}*/
body {
    font-family: "Inter", sans-serif;
}

.header {
    padding: 22px 0;
}

.course-badge-view {
    display: flex;
    gap: 5px;
}

.header .container {
    max-width: 1132px;
    padding: 0;
}

.header .navbar-brand img {
    height: 44px;
}

.header .nav {
    gap: 35px;
}

.header .nav-link {
    margin: 0;
    padding: 0;
    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px;
}

.header .header-right {
    position: relative;
}

.header .user-profile {
    display: flex;
    align-items: center;
}

.header .user-avatar {
    display: flex;
    align-items: center;
    border-radius: 50%;
    justify-content: center;
    margin-right: 3px;
    border: 1px solid #fff8e1;
}

.header .user-avatar img {
    width: 42px;
    border-radius: 50%;
    height: 42px;
    object-fit: cover;
    object-position: center;
}

.header .user-info {
    margin-top: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 5px;
    cursor: pointer;
    min-width: 136px;
    min-height: 43px;

    border-radius: 9999px;
    border: 1px solid #fff;
    background: linear-gradient(270deg, #ffe6c3 0%, #fff 70.1%);
    box-shadow: 0px -1px 11px 0px rgba(255, 255, 255, 0.5) inset;
    padding: 2px 8px 5px 21px;
}

.header .user-info-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.header .user-name {
    font-weight: 600;

    color: #333;
    font-variant-numeric: lining-nums proportional-nums;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px;
}

.header .user-badge {
    display: flex;
    align-items: center;
    padding: 2px 6px 2px 4px;
    gap: 1px;
    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;

    font-size: 10px;
    font-style: normal;
    font-weight: 700;
    line-height: 14px;
}

.header .user-badge img {
    width: 11px;
    height: 11px;
}

.header .Userprofile .dropmenu-end {
    width: fit-content;
    border-radius: 12px;
}

.header .free-badge {
    border-radius: 5px;
    border: 1px solid #89b3ff;
    background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

.header .pro-badge {
    border-radius: 5px;
    border: 1px solid #ffa688;
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
}

.header .dropdown-toggle::after {
    margin-left: 8px;
}

/* When the sticky-active class is added after scrolling */
.header.sticky-active {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    animation: slideDown 0.5s ease forwards;
    background-color: #f26508bd;
    backdrop-filter: blur(10px);
}

.header .header-nav {
    display: flex;
}

.header .navbar-toggler {
    display: none;
}

/* Animation for the header to slide down when becoming sticky */
@keyframes slideDown {
    0% {
        transform: translateY(-100%);
    }
    100% {
        transform: translateY(0);
    }
}

@media (max-width: 1280px) {
    .header .container {
        max-width: 1024px;
        padding: 0 16px;
    }
}

@media (max-width: 1024px) {
    .header.fixed-top:not(.sticky-active) {
        background-color: #f26508bd;
        backdrop-filter: blur(10px);
    }

    .header .header-nav {
        display: none;
    }

    .header .navbar-nav {
        gap: 16px;
    }

    .header .nav-link {
        padding: 6px 0;
    }

    .header .navbar {
        --bs-navbar-nav-link-padding-x: 12px;
    }

    .header .navbar-nav {
        gap: 24px;
        flex-wrap: nowrap;
        --bs-nav-link-padding-y: 12px;
    }

    .header .navbar-toggler {
        display: block;
    }
}

@media (max-width: 640px) {
    .header .navbar-nav {
        padding-top: 16px;
    }

    .header .header-right.Userprofile {
        width: auto;
    }
}

@media (max-width: 500px) {
    .header {
        padding: 14px 0;
    }

    .header .navbar-nav {
        gap: 12px;
    }

    .header .navbar-brand img {
        width: 80px;
        height: auto;
    }

    .header .nav-link {
        font-size: 14px;
        line-height: 18px;
    }
}

.hero-section {
    background-image: url("../images/hero-banner.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.hero-section .container {
    max-width: 1100px;
    height: 100%;
    padding-left: 0;
    padding-right: 0;
}

/* Hero Content */
.hero-content {
    padding-top: 184px;
    position: relative;
}

.course-label {
    color: white;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
}

.hero-section .course-title {
    font-variant-numeric: lining-nums proportional-nums;
    filter: drop-shadow(0px 3px 0px #c40009);
    font-family: Inter, sans-serif;
    font-size: 58px;
    font-style: normal;
    font-weight: 900;
    line-height: 80px;
    text-align: center;
    background: var(
        --liner-yellow,
        linear-gradient(180deg, #fff8e1 0%, #ffe898 100%)
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero-section .blue-box {
    background-image: url(../images/hero-sect-blue-box.svg);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    max-width: 500px;
    padding-top: 20px;
    padding-bottom: 24px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 20px auto 0;
}

.hero-section .blue-box-title {
    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter, sans-serif;
    font-size: 28px;
    font-style: normal;
    font-weight: 800;
    line-height: 38px;
    letter-spacing: 0.56px;
}

.hero-section .orders-per-day {
    color: #fbed0b;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    font-family: Inter, sans-serif;
    font-size: 54px;
    font-style: normal;
    font-weight: 800;
    line-height: 65px;
}

.hero-content .white-box {
    border-radius: 24px;
    border: 3px solid #fff;
    padding: 25px;
    background: linear-gradient(0deg, #fff4ce 0%, #fff 100%);
    box-shadow: 0px 7px 53px 0px rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    max-width: 573px;
    margin: 0 auto;
}

.white-box-title {
    margin-bottom: 5px;
    font-variant-numeric: lining-nums proportional-nums;
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);

    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.days-badge {
    border-radius: 24px;
    border: 1px solid #fa8128;
    min-width: 120px;
    height: 30px;
    display: inline-block;

    color: #dc3f2e;
    text-align: center;

    background-clip: unset;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: currentColor;
}

.white-box-description {
    color: #333;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
}

.hero-section .action-buttons {
    display: flex;
    justify-content: center;
    gap: 5px;
    width: fit-content;
    margin: 0 auto;
    padding: 15px 20px;

    border-radius: 0px 0px 24px 24px;
    background: #fff;
    box-shadow: 0px 7px 53px 0px rgba(0, 0, 0, 0.25);
}

.buy-course-btn {
    border-radius: 99999px;
    border: 1px solid #ffa688;
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    box-shadow: 0px -1px 11px 0px #ffa868 inset;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 5px;
    min-width: 210px;

    font-variant-numeric: lining-nums proportional-nums;
    font-weight: 800;
}

.buy-course-btn-icon {
    padding: 10px;
    border-radius: 9999px;
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
}

.try-free-btn {
    border-radius: 99999px;

    border: 1px solid #89b3ff;
    background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
    box-shadow: 0px -1px 11px 0px #688bff inset;

    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 5px;
    min-width: 210px;

    font-variant-numeric: lining-nums proportional-nums;
    font-weight: 800;
}

.try-free-btn-icon {
    padding: 10px;
    border-radius: 9999px;
    border-radius: 56px;
    background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

.try-icon {
    color: white;
}

@media (max-width: 1280px) {
    .hero-section .container {
        max-width: 1024px;
        padding: 0 16px;
    }

    .hero-section .hero-content {
        padding-top: 150px;
    }

    .hero-section .course-label {
        font-size: 32px;
    }

    .hero-section .course-title {
        font-size: 48px;
    }

    .hero-section .blue-box {
        max-width: 450px;
        padding-top: 24px;
        margin-top: 16px;
    }

    .hero-section .blue-box-title {
        font-size: 24px;
        line-height: 32px;
    }

    .hero-section .orders-per-day {
        font-size: 48px;
        line-height: 52px;
    }

    .hero-section .white-box {
        padding: 16px;
        border-radius: 16px;
        max-width: 500px;
    }

    .hero-section .white-box-title {
        font-size: 20px;
        line-height: 24px;
    }

    .hero-section .days-badge {
        padding-top: 3px;
    }

    .hero-section .white-box-description {
        font-size: 16px;
        line-height: 20px;
    }

    .hero-section .action-buttons {
        border-radius: 0 0 16px 16px;
        padding: 16px 16px 20px;
    }
}

@media (max-width: 1024px) {
    .hero-section .hero-content {
        flex-direction: column;
    }

    .hero-section .hero-content > div {
        width: 100%;
    }

    .hero-section .hero-content .expert-image {
        margin-top: 16px;
        margin-left: auto;
        margin-right: auto;
        width: 50%;
    }
}

@media (max-width: 640px) {
    .hero-section .hero-content {
        padding-top: 124px;
    }

    .hero-section .course-title {
        font-size: 36px;
        line-height: 40px;
    }

    .hero-section .blue-box-title {
        font-size: 20px;
        line-height: 24px;
    }

    .hero-section .orders-per-day {
        font-size: 36px;
        line-height: 40px;
    }

    .hero-section .white-box {
        max-width: unset;
        width: calc(100% - 48px);
    }

    .hero-section .white-box-title {
        font-size: 16px;
    }

    .hero-section .days-badge {
        min-width: unset;
        height: auto;
        padding: 2px 10px;
    }

    .hero-section .white-box-description {
        font-size: 14px;
    }

    .hero-section .action-buttons {
        gap: 10px;
    }

    .hero-section .buy-course-btn,
    .hero-section .try-free-btn {
        font-size: 14px;
        min-width: unset;
        padding-right: 14px;
    }

    .hero-section .buy-course-btn-icon,
    .hero-section .try-free-btn-icon {
        padding: 8px;
    }

    .hero-section .buy-course-btn-icon img,
    .hero-section .try-free-btn-icon img {
        width: 16px;
        height: 16px;
    }

    .hero-section .expert-badge {
        left: 35%;
        right: unset;
    }

    .hero-section .expert-badge-title {
        font-size: 14px;
    }

    .hero-section .expert-name {
        font-size: 16px;
    }
}

@media (max-width: 500px) {
    .hero-section .hero-content {
        padding-top: 120px;
    }

    .hero-section .course-label {
        font-size: 24px;
    }

    .hero-section .course-title {
        font-size: 28px;
        line-height: 32px;
    }

    .hero-section .white-box {
        width: calc(100% - 32px);
    }

    .hero-section .white-box-title {
        font-size: 14px;
    }

    .hero-section .days-badge {
        padding: 2px 6px;
    }

    .hero-section .action-buttons {
        flex-direction: column;
        gap: 10px;
        padding: 16px 40px;
    }

    .hero-section .expert-badge {
        min-width: 200px;
    }

    .hero-section .hero-content .expert-image {
        margin: 20px auto 0;
    }

    .hero-section .hero-content .expert-image img {
    }
}

/* Expert section */
.expert-image {
    position: relative;
    text-align: right;
}

.expert-badge {
    position: absolute;
    bottom: 29.98%;
    right: 0;

    color: white;
    border-radius: 4px;
    font-size: 14px;
    text-align: left;
    padding-top: 15px;
    padding-left: 33px;
    padding-bottom: 14px;
    padding-right: 23px;

    min-width: 250px;
    border-radius: 0px 12px 12px 0px;
    background: linear-gradient(
        270deg,
        #eb2805 0%,
        rgba(239, 74, 6, 0.78) 53.5%,
        rgba(239, 74, 6, 0) 100%
    );
}

.expert-name {
    font-variant-numeric: lining-nums proportional-nums;
}

/* Problem section */
.problem-section {
    background-image: url("../images/problems-background.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding-top: 42px;
}

.problem-section .container {
    max-width: 1200px;
    padding-left: 0;
    padding-right: 0;
}

.problem-header {
    margin-bottom: 40px;
}

.problem-title {
    text-align: center;
    font-kerning: none;
    font-variant-numeric: lining-nums proportional-nums;
    color: white;
    width: fit-content;
    margin: 0 auto;
    min-width: 171px;
    padding: 2px;
    border-radius: 9999px;
    border: 1px solid #89b3ff;
    background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
    box-shadow: 0px -1px 11px 0px #688bff inset;
}

.problem-heading {
    margin-top: 8px;
    font-variant-numeric: lining-nums proportional-nums;

    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.problems-wrapper {
    margin: 0 auto;
}

.person-thinking {
    text-align: center;
    width: fit-content;
    margin: 0 auto;
}

.problem-item {
    background: white;
    border-radius: 15px;
    padding: 20px 30px 20px 33px;
    border-radius: 20px;
    background: var(--White-color, #fff);
    box-shadow: 0px 7px 20px 0px rgba(0, 0, 0, 0.02);
    margin-bottom: 20px;

    position: absolute;
    width: 348px;
    min-height: 115px;
}

.problem-item-small {
    width: 283px;
    min-height: 86px;
}

.problem-item.left-item::after {
    content: "";
    position: absolute;
    top: 100%;
    right: 24px;
    width: 0;
    height: 0;
    border: none;
    border-top: 10px solid #fff;
    border-left: 10px solid transparent;
    border-right: 10px solid #fff;
    border-bottom: 10px solid transparent;
}

.problem-item.right-item::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 24px;
    width: 0;
    height: 0;
    border: none;
    border-top: 10px solid #fff;
    border-left: 10px solid #fff;
    border-right: 10px solid transparent;
    border-bottom: 10px solid transparent;
}

.problem-item.left-item .warning-icon {
    position: absolute;
    top: -10px;
    left: -13px;
}

.problem-item.right-item .warning-icon {
    position: absolute;
    top: -10px;
    right: -13px;

    transform: scaleX(-1);
}

.problem-item-1 {
    top: 71px;
    left: 100px;
}

.problem-item-2 {
    top: 238px;
    left: 0px;
}

.problem-item-3 {
    top: 422px;
    left: 165px;
}

.problem-item-4 {
    top: 71px;
    right: 83px;
}

.problem-item-5 {
    top: 238px;
    right: 0px;
}

.problem-section .problem-item-title {
    color: #fa8128;
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 17px;
}

.problem-item-text {
    color: #666;
    margin-bottom: 0;
}

@media (max-width: 1280px) {
    .problem-section .container {
        max-width: 1024px;
        padding: 0 16px;
    }

    .problem-section .problem-item {
        width: 332px;
    }

    .problem-section .problem-item-small {
        width: 268px;
    }

    .problem-section .problem-item-1 {
        top: 70px;
        left: 24px;
    }

    .problem-section .problem-item-2 {
        top: 238px;
        left: -10px;
    }

    .problem-section .problem-item-3 {
        top: 422px;
        left: 80px;
    }

    .problem-section .problem-item-4 {
        top: 71px;
        right: 30px;
    }

    .problem-section .problem-item-5 {
        top: 238px;
        right: -10px;
    }

    .problem-section .problem-item-title {
        font-size: 16px;
    }

    .problem-section .problem-item-text {
        font-size: 15px;
    }
}

@media (max-width: 1024px) {
    .problem-section .person-thinking img {
        width: 400px;
    }

    .problem-section .problem-item-1,
    .problem-section .problem-item-2 {
        left: 0;
    }

    .problem-section .problem-item-3 {
        left: 24px;
    }

    .problem-section .problem-item-4,
    .problem-section .problem-item-5 {
        right: 0;
    }

    .problem-section .problem-item {
        width: 300px;
    }

    .problem-section .problem-item-small {
        width: 250px;
    }
}

@media (max-width: 640px) {
    .problem-section .problem-item {
        width: calc(50% - 6px);
    }

    .problem-section .problem-header {
        margin-bottom: 24px;
    }

    .problem-section .problem-item.left-item {
        left: 0;
    }

    .problem-section .problem-item.right-item {
        right: 0;
    }

    .problem-section .problem-item::after {
        content: none;
    }

    .problem-section .problem-item-1,
    .problem-section .problem-item-4 {
        top: 0;
    }

    .problem-section .problem-item-3 {
        top: 320px;
    }

    .problem-section .problem-item-2,
    .problem-section .problem-item-5 {
        top: 160px;
    }

    .problem-section .person-thinking {
        padding-top: 440px;
    }
}

@media (max-width: 500px) {
    .problem-section {
        padding-top: 36px;
    }

    .problem-section .problem-heading {
        font-size: 24px;
        line-height: 28px;
    }

    .problem-section .problem-item {
        padding: 20px;
    }

    .problem-section .problem-item-title {
        font-size: 14px;
    }

    .problem-section .problem-item-text {
        font-size: 14px;
    }

    .problem-section .problem-item-2,
    .problem-section .problem-item-5 {
        top: 190px;
    }

    .problem-section .problem-item-3 {
        top: 370px;
    }
}

/* Course Section */
.course-section {
    background-image: url("../images/course-background.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 60px 0;
}

.course-section .container {
    max-width: 1200px;
    padding-left: 0;
    padding-right: 0;
}

.course-section-header-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-section-title {
    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    text-shadow: 0px 1px 0px #c40009;
    font-family: Inter, sans-serif;
    font-size: 72px;
    font-style: normal;
    font-weight: 900;
    line-height: 48px;
    margin-bottom: 20px;
}

.course-section-subtitle {
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    filter: drop-shadow(0px 1px 0px #c40009);
    font-family: Inter, sans-serif;
    font-size: 42px;
    font-style: normal;
    font-weight: 900;
    line-height: 48px; /* 114.286% */

    background: linear-gradient(180deg, #fff8e1 0%, #ffe898 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.course-section .blue-box-title {
    border-radius: 5px;
    border: 2px solid #162e74;
    background: linear-gradient(90deg, #064fd0 0%, #0043c0 100%);
    color: white;
    margin-top: 20px;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    padding: 9px 9px 14px;
}

.badge-from-zero {
    border-radius: 0px 0px 10px 10px;
    background: #fff;
    box-shadow: 0px 7px 53px 0px rgba(0, 0, 0, 0.25);
    width: fit-content;
    margin: 0 auto;
    padding: 7px 43px;
}

.badge-from-zero span {
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.course-section-description {
    margin-top: 26px;
    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
}

.course-benefits-wrapper {
    position: relative;
    display: flex;
    flex-wrap: wrap;
}

.benefit-card {
    width: 33.333333333333336%;
    border-radius: 9px;
    background: linear-gradient(0deg, #fff4ce 0%, #fff 38%);
    background-clip: padding-box;
    padding: 30px 25px;
    min-height: 136px;
    border: 1px solid transparent;
}

.benefit-title {
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-top: 0;
    margin-bottom: 12px;
    font-variant-numeric: lining-nums proportional-nums;
}

.benefit-text {
    color: #666;
    margin-bottom: 0;
}

.course-avatar-wrapper {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 1280px) {
    .course-section .container {
        max-width: 1024px;
        padding: 0 16px;
    }

    .course-section .course-section-title {
        font-size: 48px;
        margin-bottom: 16px;
    }

    .course-section .course-section-subtitle {
        font-size: 36px;
        line-height: 42px;
    }

    .course-section .blue-box-title {
        margin-top: 16px;
        font-size: 24px;
        line-height: 28px;
        padding: 10px 16px;
    }

    .course-section .badge-from-zero {
        padding: 8px 32px;
    }

    .course-section .course-section-description {
        margin-top: 16px;
    }

    .course-section .course-avatar-wrapper img {
        width: 450px;
    }

    .course-section .benefit-card {
        padding: 20px 16px;
    }

    .course-section .benefit-title {
        font-size: 16px;
        line-height: 20px;
    }

    .course-section .benefit-text {
        font-size: 14px;
    }
}

@media (max-width: 1024px) {
    .course-section {
        padding: 40px 0;
    }

    .course-section .course-section-title {
        font-size: 40px;
    }

    .course-section .course-section-subtitle {
        font-size: 30px;
        line-height: 36px;
    }

    .course-section .blue-box-title {
        font-size: 20px;
        line-height: 24px;
    }

    .course-section .badge-from-zero {
        font-size: 24px;
        line-height: 28px;
    }

    .course-section .course-section-description {
        font-size: 16px;
    }

    .course-section .course-avatar-wrapper img {
        width: 400px;
    }

    .course-section .benefit-card {
        width: 50%;
        min-height: 116px;
    }
}

@media (max-width: 640px) {
    .course-section .course-section-header-wrapper {
        flex-direction: column;
    }

    .course-section .course-section-title {
        font-size: 36px;
        line-height: 40px;
        margin-bottom: 12px;
    }

    .course-section .course-section-subtitle {
        font-size: 24px;
        line-height: 28px;
    }

    .course-section .badge-from-zero {
        font-size: 20px;
    }
}

@media (max-width: 500px) {
    .course-section {
        padding: 32px 0;
    }

    .course-section .course-section-title {
        font-size: 24px;
        line-height: 28px;
    }

    .course-section .course-section-subtitle {
        font-size: 18px;
        line-height: 22px;
    }

    .course-section .blue-box-title {
        font-size: 16px;
        line-height: 20px;
    }

    .course-section .course-avatar-wrapper img {
        width: 300px;
    }

    .course-section .benefit-card {
        width: 100%;
        min-height: 116px;
    }
}

/* Module Section Styles */
.modules-section {
    background-color: #fff6ed;
    padding: 50px 0;
    background-image: url("../images/course-roadmap-background.jpg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    margin-top: -8px;
}

.modules-section .container {
    max-width: 1200px;
    padding-left: 0;
    padding-right: 0;
}

.modules-section .modules-section-header {
    margin-bottom: 66px;
}

.modules-section-header-title {
    background-image: url("../images/hero-sect-blue-box.svg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    width: fit-content;
    margin: 0 auto;
    padding: 24px 40px 30px;
    color: white;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.modules-section-header-subtitle-text {
    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter, sans-serif;
    font-size: 28px;
    font-style: normal;
    font-weight: 800;
    line-height: 38px; /* 135.714% */
    letter-spacing: 0.56px;
}

.modules-section-header-title-text {
    color: #fbed0b;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    font-family: Inter, sans-serif;
    font-size: 54px;
    font-style: normal;
    font-weight: 800;
    line-height: 65px; /* 120.37% */
}

.modules-section-header-subtitle {
    border-radius: 5px;
    border: 1px solid #ffa688;
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    box-shadow: 0px -1px 11px 0px #ffa868 inset;
    width: fit-content;
    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    padding: 10px 78px;
    margin: 0 auto;
}

.modules-section .modules-list {
    max-height: 750px;
    overflow: auto;
}

.modules-section .module-item {
    background: #670a00;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
    border-bottom: none;
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.05);
}

.modules-section .module-header {
    cursor: pointer;
    border-radius: 10px;
    border: 1px solid #fff;
    padding: 0;
    background: linear-gradient(0deg, #fff4ce 0%, #fff 100%);
    box-shadow: 0px 7px 53px 0px rgba(0, 0, 0, 0.25);
}

.modules-section .module-header > div {
    width: 100%;
    padding: 8px 14px 8px 17px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modules-section .module-item.active .module-header {
    background-color: #fa8128;
    border-radius: 15px 15px 0 0;
}

.modules-section .module-item.active .module-title h3,
.modules-section .module-item.active .module-details,
.modules-section .module-item.active .module-details span {
    color: #fff;
}

.modules-section .module-header > div[aria-expanded="true"] h3 {
    color: #dc3f2e;
}

.modules-section
    .module-header
    > div[aria-expanded="true"]
    .toggle-btn-icon-expand {
    display: none;
}

.modules-section
    .module-header
    > div[aria-expanded="false"]
    .toggle-btn-icon-collapse {
    display: none;
}

.modules-section .module-title h3 {
    font-weight: 700;
    color: #333;
    font-variant-numeric: lining-nums proportional-nums;
}

.modules-section .module-details {
    display: flex;
    gap: 20px;
    color: #666;
    font-size: 14px;
}

.modules-section .module-details > span {
    display: inline-flex;
    align-items: center;
}

.modules-section .module-details span {
    color: #333;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px; /* 142.857% */
}

.modules-section .module-details strong {
    font-weight: 800;
}

.modules-section .video-count img,
.modules-section .duration img {
    margin-right: 5px;
}

.modules-section .module-content {
    background-color: #670a00;
    color: white;
    padding: 0;
}

.modules-section .module-content .accordion-body {
    padding: 16px 16px 28px;
}

.modules-section .lesson-item {
    padding-bottom: 14px;
    margin-bottom: 14px;
    border-bottom: 1px solid #a61808;
}

.modules-section .lesson-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.modules-section .lesson-item-title {
    color: #fff;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter, sans-serif;
    font-size: 15px;
    font-style: normal;
    font-weight: 700;
    line-height: 18px; /* 112.5% */
    min-height: 24px;
    margin-bottom: 8px;
}

.modules-section .lesson-item > div {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.modules-section .badge {
    height: 20px;
    border-radius: 5px;
    color: white;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 9px 0 4px;
    margin-right: 9px;
}

.free-badge {
    border: 1px solid #89b3ff;
    background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

.pro-trial-badge {
    border: 1px solid #ffa688;
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
}

.important-badge {
    border: 1px solid #ffa688;
    background: linear-gradient(270deg, #e91e63 0%, #9c27b0 100%);
}

.premium-trial-badge {
    border: 1px solid #ffa688;
    background: linear-gradient(270deg, #000 0%, #eb2b05 100%);
}

.modules-section .duration-small {
    display: flex;
    color: white;
    align-items: center;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.modules-section .duration-small img {
    margin-right: 4px;
    filter: brightness(0) invert(1);
}

/* Registration Card Styles */
.modules-section .registration-card {
    position: relative;
}

.modules-section .course-info {
    position: absolute;
    top: 0;
    transform: translateY(-50%) translateX(-50%);
    left: 50%;

    border-radius: 9999px;
    border: 3px solid #fff;
    background: linear-gradient(0deg, #fff4ce 0%, #fff 100%);
    box-shadow: 0px 7px 53px 0px rgba(0, 0, 0, 0.25);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 28px;
    padding: 10px 28px;
    color: #000;
    width: calc(100% - 72px);
}

.modules-section .course-info-icon {
    margin-right: 10px;
}

.modules-section .course-info > .divider {
    width: 0;
    border-left: 1px solid #fa8128;
    height: 46px;
}

.modules-section .course-time {
    color: #105ce4;
    font-weight: 700;
}

.modules-section .course-platform .text-h6 {
    color: #105ce4;
    font-weight: 700;
}

.modules-section .registration-section {
    padding: 20px;
    padding-top: 6px;
    border-radius: 10px;
    border: 3px solid #fff;
    background: #fff;
    box-shadow: 0px 7px 53px 0px rgba(0, 0, 0, 0.25);
}

.modules-section .registration-section-wrapper {
    padding-top: 58px;
    border-radius: 0px 0px 28px 28px;
    background: linear-gradient(180deg, #fff 0%, #fed 100%);
}

.modules-section .registration-section h3 {
    color: #333;
    text-align: center;
    font-kerning: none;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter, sans-serif;
    font-size: 22px;
    font-style: normal;
    font-weight: 800;
    line-height: 26px; /* 118.182% */
}

.modules-section .registration-section h3 > .highlight-text {
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.modules-section .countdown-section {
    border-radius: 15px;
    background: linear-gradient(180deg, #fff0f0 0%, #fed 100%);
    width: fit-content;
    margin: 13px auto 0;
    padding: 4px 30px 8px;
}

.modules-section .countdown-section > p {
    color: #333;
    margin-bottom: 2px;
}

.modules-section .countdown-timer {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;

    color: #105ce4;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter, sans-serif;
    font-size: 32px;
    font-style: normal;
    font-weight: 800;
    line-height: 28px; /* 87.5% */
    letter-spacing: 1.92px;
}

/* Tier Pricing */
.modules-section .pricing-tiers {
    margin: 24px 12px 20px;
    padding: 0 30px;
}

.modules-section .pricing-tiers .tier-item:last-child {
    margin-bottom: 0;
}

.modules-section .tier-item {
    gap: 10px;
    margin-bottom: 20px;
    position: relative;
}

.modules-section .tier-item-tail {
    position: absolute;
    top: 27px;
    left: 10px;
    width: 1px;
    height: 100%;
    border-left: 1px dashed #fa8128;
}

.modules-section .pricing-tiers .tier-item:last-child .tier-item-tail {
    display: none;
}

.modules-section .tier-number {
    margin-top: 7px;
    position: relative;
    z-index: 1;
    color: white;
    height: 22px;
    min-width: 22px;
    text-align: center;
    padding: 2px 0;
    border-radius: 50%;
    font-variant-numeric: lining-nums proportional-nums;
}

.modules-section .tier-badge {
    border-radius: 24px;
    padding: 8px 14px 8px 22px;
    width: fit-content;
    border: 1px solid var(--Primary-Color, #fa8128);
    background: var(
        --liner-yellow,
        linear-gradient(180deg, #fff8e1 0%, #ffe898 100%)
    );
}

.modules-section .tier-item-price {
    margin-top: 10px;
    color: #000;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter, sans-serif;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 25px;
}

.modules-section .price-regular {
    color: #333;
    font-weight: 800;
}

/* Bonus Section */
.modules-section .bonus-section {
    margin-top: 15px;
    padding: 0 20px 27px 16px;
    border-radius: 10px;
}

.modules-section .bonus-section-title {
    color: #333;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 20px; /* 125% */
}

.modules-section .bonus-section-title img {
    margin-right: 8px;
    display: inline-block;
}

.modules-section .bonus-list {
    padding-left: 20px;
    margin-bottom: 0;
    list-style: disc;
}

.modules-section .bonus-list li {
    margin-bottom: 8px;
    color: #666;
    font-family: Questrial, Inter, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 19px; /* 118.75% */
}

.modules-section .bonus-list li:last-child {
    margin-bottom: 0;
}

/* Action Buttons */
.modules-section .action-buttons {
    justify-content: center;
    gap: 5px;
    margin-top: 22px;
    padding: 0 50px;
}

.modules-section .buy-course-btn,
.modules-section .try-free-btn {
    border: none;

    padding-right: 14px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-weight: 700;
    gap: 10px;
    width: 50%;
    cursor: pointer;

    color: #fff;
    text-align: center;
    font-variant-numeric: lining-nums proportional-nums;
    font-family: Inter, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 800;
    line-height: 24px;
}

.modules-section .buy-course-btn {
    border-radius: 9999px;
    border: 1px solid #ffa688;
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    box-shadow: 0px -1px 11px 0px #ffa868 inset;
}

.modules-section .try-free-btn {
    border-radius: 9999px;
    background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

.modules-section .buy-course-btn-icon,
.modules-section .try-free-btn-icon {
    border-radius: 50%;
    padding: 10px;
    width: fit-content;
    flex-shrink: 0;
}

.modules-section .buy-course-btn > span:last-child,
.modules-section .try-free-btn > span:last-child {
    display: inline-block;
    width: 100%;
}

@media (max-width: 1280px) {
    .modules-section .container {
        max-width: 1024px;
        padding: 0 16px;
    }

    .modules-section .modules-section-header-subtitle-text {
        font-size: 24px;
        line-height: 28px;
    }

    .modules-section .modules-section-header-title-text {
        font-size: 48px;
        line-height: 60px;
    }

    .modules-section .modules-section-header-subtitle {
        padding: 10px 80px;
        font-size: 20px;
        line-height: 24px;
    }

    .modules-section .modules-section-header {
        margin-bottom: 52px;
    }

    .modules-section .module-header > div {
        padding: 8px 14px;
    }

    .modules-section .module-title h3 {
        font-size: 16px;
    }

    .modules-section .module-content > .accordion-body {
        padding: 16px;
    }

    .modules-section .module-content > .accordion-body > p {
        font-size: 14px;
        line-height: 18px;
    }

    .modules-section .lesson-item-title {
        font-size: 14px;
        min-height: unset;
        line-height: 18px;
    }

    .modules-section .modules-list {
        max-height: 695px;
    }

    .modules-section .course-info {
        width: calc(100% - 64px);
        gap: 24px;
    }

    .modules-section .course-info-icon {
        width: 32px;
        height: 32px;
    }

    .modules-section .course-info-icon + div > p {
        font-size: 14px;
    }

    .modules-section .course-info-icon + div .course-time {
        font-size: 16px;
    }

    .modules-section .registration-section-wrapper {
        padding-top: 48px;
    }

    .modules-section .registration-section h3 {
        font-size: 18px;
        line-height: 24px;
    }

    .modules-section .countdown-section {
        margin-top: 12px;
        padding: 8px 30px;
    }

    .modules-section .pricing-tiers {
        padding: 0 24px;
    }

    .modules-section .tier-number {
        font-size: 16px;
        padding: 1px 0;
    }

    .modules-section .tier-badge {
        font-size: 16px;
        padding: 8px 14px;
    }

    .modules-section .tier-item-price {
        font-size: 16px;
        line-height: 20px;
    }

    .modules-section .bonus-section-title {
        font-size: 14px;
        line-height: 18px;
    }

    .modules-section .bonus-list li {
        font-size: 14px;
        line-height: 18px;
    }

    .modules-section .buy-course-btn,
    .modules-section .try-free-btn {
        font-size: 14px;
        line-height: 18px;
    }
}

@media (max-width: 1024px) {
    .modules-section .modules-section-header-subtitle-text {
        font-size: 20px;
        line-height: 24px;
    }

    .modules-section .modules-section-header-title-text {
        font-size: 42px;
        line-height: 50px;
    }

    .modules-section .modules-section-header-subtitle {
        font-size: 18px;
        line-height: 22px;
    }

    .modules-section .container > div.row {
        flex-direction: column-reverse;
        gap: 24px;
    }

    .modules-section .container > div.row > div {
        width: 100%;
        max-width: 680px;
        margin: 0 auto;
    }

    .modules-section .modules-list {
        max-height: unset;
    }
}

@media (max-width: 640px) {
    .modules-section .modules-section-header-title-text {
        font-size: 36px;
        line-height: 40px;
    }

    .modules-section .modules-section-header-subtitle {
        font-size: 16px;
        padding: 10px 60px;
    }
}

@media (max-width: 500px) {
    .modules-section .modules-section-header-subtitle-text {
        font-size: 16px;
        line-height: 20px;
    }

    .modules-section .modules-section-header-title-text {
        font-size: 24px;
        line-height: 28px;
    }

    .modules-section .modules-section-header-subtitle {
        font-size: 14px;
        padding: 10px 40px;
    }

    .modules-section .toggle-btn {
        width: 16px;
    }

    .modules-section .course-info {
        width: calc(100% - 16px);
        gap: 10px;
        padding: 10px 6px;
    }

    .modules-section .course-info-icon {
        width: 24px;
        height: 24px;
        margin-right: 5px;
    }

    .modules-section .course-info-icon + div .course-time {
        font-size: 14px;
    }

    .modules-section .registration-section h3 {
        font-size: 14px;
        line-height: 18px;
    }

    .modules-section .countdown-section > p {
        font-size: 14px;
        line-height: 18px;
    }

    .modules-section .countdown-timer {
        font-size: 16px;
        line-height: 20px;
        gap: 2px;
    }

    .modules-section .pricing-tiers {
        padding: 0 14px;
    }

    .modules-section .tier-number {
        font-size: 14px;
        padding: 1px 0;
    }

    .modules-section .tier-badge {
        font-size: 14px;
        border-radius: 10px;
    }

    .modules-section .tier-item-price {
        font-size: 14px;
        line-height: 18px;
    }

    .modules-section .bonus-section-title {
        font-size: 12px;
        line-height: 16px;
    }

    .modules-section .bonus-list li {
        font-size: 12px;
        line-height: 16px;
    }

    .modules-section .action-buttons {
        flex-direction: column;
        margin-top: 15px;
        gap: 10px;
        align-items: center;
    }

    .modules-section .buy-course-btn-icon img,
    .modules-section .try-free-btn-icon img {
        width: 16px;
        height: 16px;
    }
}

.modal-regis .modal-dialog {
    max-width: 690px;
}

.modal-auth #authTabs {
    border: none;
}

.modal-auth {
    max-width: 690px;
}

.modal-auth .modal-body {
    padding: 20px 50px 40px !important;
}

.modal-auth .form-login {
    border-radius: 28px;
    border: 2px solid var(--gradient-2, #ff5a07);
    background: #fff;
    box-shadow: 0px 7px 35px 0px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.form-wrap {
    position: relative;
}

.modal-auth .form-wrap {
    position: relative;
}

.modal-auth .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
}

.modal-auth .modal-content {
    border-radius: 25px;
    overflow: hidden;
    border: none;
}

.modal-auth .modal-header {
    background: #105ce4 !important;
    color: #fbed0b;
    border-bottom: none;
    padding: 1.25rem 1.5rem 0.5rem;
    position: relative;
    text-align: center;
}

.modal-auth .modal-title {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 0;
    line-height: 1.2;
    margin-bottom: 5px;
}

.modal-auth .close-btn {
    position: absolute;
    right: 15px;
    top: 10px;
    background: transparent;
    border: none;
    color: white;
    font-size: 21px;
    padding: 0;
}

.modal-auth .modal-header p {
    color: white;
    font-size: 0.95rem;
    margin-top: 0.5rem;
}

.modal-auth .google-btn {
    margin-bottom: 1rem;
    padding: 0.75rem;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    color: #222;
    border-radius: 10px;
    border: 1px solid #ffe6e8;
    background: linear-gradient(180deg, #fff 0%, #ffeff0 100%);
}

.modal-auth .google-logo {
    margin-right: 0.5rem;
}

.modal-auth .tabs-container {
    display: flex;
    position: relative;
    overflow: hidden;
    border-radius: 25px 25px 0 0;
}

.modal-auth .tab-switch-auth span {
    color: #fff;
    -webkit-text-fill-color: #fff;
}

.modal-auth .tab-switch-auth .active span {
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    border: none !important;
}

.modal-auth .tab-switch-auth .tab.tab-active {
    background: #fff;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: #fff;
}

.modal-auth .tab-switch-auth .tab img {
    position: absolute;
    width: 100%;
    left: 0;
    z-index: 0;
    height: 100%;
    opacity: 1;
    transition: opacity 0.3s;
}

.modal-auth .tab-switch-auth .active img {
    opacity: 0;
}

.modal-auth .tab {
    flex: 1;
    text-align: center;
    height: 60px;
    font-weight: 700;
    color: white;
    cursor: pointer;
    position: relative;
    z-index: 1;
    font-size: 1.1rem;
}

.modal-auth .tab-active {
    color: #f33;
}

.modal-auth .tab-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 50%;
    height: 100%;
    background-color: white;
    z-index: 0;
    transition: transform 0.3s ease;
}

.modal-auth .tab-content {
    border: 1px solid #ffe0dc;
    border-top: none;
    border-radius: 0 0 25px 25px;
    padding: 20px 24px;
    background-color: white;
}

.form-label {
    margin-bottom: 0.25rem;
    font-size: 16px;
    position: absolute;
    background: #fff;
    top: -12px;
    z-index: 10;
    left: 14px;
    padding: 0 3px;
    color: #333;
}

.modal-auth .form-label {
    margin-bottom: 0.25rem;
    font-size: 16px;
    position: absolute;
    background: #fff;
    top: -12px;
    z-index: 10;
    left: 14px;
    padding: 0 3px;
    color: #333;
}

.modal-auth .form-control {
    border-radius: 10px;
    padding: 0.75rem;
    border-radius: 5px;
    border: 1px solid #dadada;
    height: 52px;
}

.modal-auth .form-text {
    color: #dc3f2e;
    font-weight: 500;
}

.modal-auth .red-text {
    color: #f33;
    font-weight: 500;
}

.password-field {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    background: none;
    border: none;
    cursor: pointer;
}

.modal-auth .signup-btn {
    background: linear-gradient(90deg, #ffb700 0%, #f33 100%);
    border-radius: 15px;
    margin-top: 1.5rem;
    width: 100%;
    border-radius: 10px;
    background: var(
        --gradient-2,
        linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
    );
    border: none;
    outline: none;
    font-size: 20px !important;
    font-weight: bold;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-auth .free-badge {
    position: relative;
    width: 60px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-auth .free-circle {
    width: 50px;
    height: 50px;
    background-color: #f33;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: rotate(-15deg);
}

.modal-auth .free-circle span {
    color: white;
    font-weight: bold;
    font-size: 0.8rem;
}

.modal-auth .btn-text {
    text-align: left;
    padding-left: 0.5rem;
    font-size: 19px !important;
}

.modal-auth .btn-title {
    font-size: 20px;
    line-height: 1.2;
}

.modal-auth .btn-subtitle {
    font-size: 14px;
    font-weight: 400;
    opacity: 0.9;
}

.modal-auth .nav-tabs .nav-link {
    width: 100%;
    border: none;
    background: none;
    padding: 0;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.modal-regis .img-bank {
    position: absolute;
    width: calc(100% - 10px);
    top: 42%;
    left: 50%;
    transform: translate(-50%, -50%);
    object-fit: contain;
}

.modal-regis .border-bank {
    aspect-ratio: 1;
    width: 100%;
}

.modal-regis .body-method-option {
    background: #f7f7f7;
    padding: 0 10px;
}

.modal-regis #btn-checkout-regis {
    border-radius: 10px;
    background: var(
        --gradient-2,
        linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
    );
    border: none;
    display: block;
    width: 100%;
    height: 56px;
    font-size: 18px;
    text-transform: uppercase;
    font-weight: bold;
    color: #fff;
}

.modal-regis .phone-group {
    border: 1px solid #dadada;
    border-radius: 5px;
}

.modal-regis .phone-group input {
    border: none;
}

.modal-regis .phone-group button {
    border: none;
    border-right: 1px solid #dadada;
    border-radius: 0;
    display: flex;
    width: 110px;
    font-size: 16px;
    align-items: center;
    gap: 6px;
    justify-content: center;
}

.modal-regis input#couponInput {
    color: #dc3f2e;
}

.modal-regis .step.active .step-label,
.modal-regis .step.completed .step-label {
    font-weight: bold;
    color: #105ce4;
}

.modal-regis .step-label {
    color: #666;
}

.modal-regis .payment-header-t .input-group {
    gap: 8px;
    display: flex;
}

.modal-regis button#applyCoupon {
    border-radius: 5px;
    background: #fa8128;
    width: 110px;
    font-size: 16px;
}

.modal-regis .payment-header-t {
    padding: 0 20px;
    border-radius: 20px 20px 0px 0px;
    background: linear-gradient(180deg, #fff 0%, #fff4e9 100%);
}

.modal-regis .payment-container {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.modal-regis .progress-steps {
    display: flex;
    justify-content: space-between;
    padding: 20px 0;
    position: relative;
}

.modal-regis .progress-line {
    position: absolute;
    top: 34px;
    left: 30px;
    right: 100px;
    height: 1px;
    background-color: #fa8128;
    z-index: 0;
    width: calc(100% - 60px);
}

.modal-regis .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
    /* color: #105CE4;
           */
}

.modal-regis .step-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    background-color: white;
    border: 1px solid #fa8128;
}

.modal-regis .step.active .step-circle {
    background-color: #ff8c00;
    border-color: #ff8c00;
    color: white;
}

.modal-regis .step.completed .step-circle {
    background-color: #ff8c00;
    border-color: #ff8c00;
    color: white;
}

.modal-regis .payment-header {
    margin-bottom: 10px;
}

.modal-regis .payment-body {
    padding: 20px 40px;
}

.modal-regis .price-info {
    display: flex;
    justify-content: space-between;
    /* margin-bottom: 5px;
           */
}

.modal-regis .total-price {
    display: flex;
    justify-content: space-between;
    padding: 15px 0;
    margin-top: 10px;
    border-top: 1px dashed #e0e0e0;
}

.modal-regis .price {
    color: #ff5722;
    font-weight: bold;
}

.modal-regis .payment-methods-container {
    background-color: white;
    padding: 20px 15px;
    border-radius: 15px;
    border: 1px solid #ffd8a8;
}

.modal-regis .method-option {
    border-radius: 10px;
    margin-bottom: 20px;
}

.modal-regis .method-title {
    /* font-weight: bold;
           */
    margin-bottom: 15px;
    color: #333;
}

.modal-regis .qr-container {
    position: relative;
}

.modal-regis .bank-info {
    margin-bottom: 10px;
    font-size: 14px;
}

.modal-regis .bank-info-row {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #fff;
    padding: 8px 0;
    color: #333;
}

.modal-regis .bank-info-label {
    color: #333;
}

.modal-regis .bank-info-value {
    font-weight: 600;
    color: #333;
}

.modal-regis .warning-box {
    background-color: #fff9f0;
    border: 1px dashed #ffca80;
    border-radius: 8px;
    padding: 10px;
    margin: 15px 0;
    border: 1px dashed var(--Primary-Color, #fa8128);
    background: #fff9ec;
    font-size: 12px;
    color: #333;
}

.modal-regis .warning-icon {
    color: #ff8c00;
    margin-right: 5px;
}

.modal-regis .download-btn {
    background-color: #105ce4;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 5px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 12px;
}

.modal-regis .timer-container {
    background-color: #fff5eb;
    border-radius: 10px;
    padding: 10px 15px;
    /* margin-top: 20px; */
    display: flex;
    justify-content: space-between;
    border-radius: 15px;
    background: linear-gradient(180deg, #fff0f0 0%, #fed 100%);
    font-size: 16px;
    color: #333;
}

.modal-regis .timer {
    color: #105ce4;
    font-weight: bold;
    font-size: 24px;
}

.modal-regis .status-container {
    text-align: center;
    margin-top: 15px;
    color: #666;
}

.modal-regis .status-container .loading-icon {
    color: #fa8128;
}

.modal-regis .loading-icon {
    display: inline-block;
    margin-left: 5px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.modal-regis .close-btn {
    position: absolute;
    top: 6px;
    right: 10px;
    background: none;
    border: none;
    font-size: 25px;
    cursor: pointer;
    background: #fff;
    /* padding: 5px; */
    /* border: 1px solid #DC3F2E; */
    border-radius: 50%;
    color: #dc3f2e !important;
    font-weight: normal !important;
    width: 30px;
    z-index: 99999;
    height: 30px;
}

.modal-regis .nav-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.modal-regis .nav-btn {
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    cursor: pointer;
}

.modal-regis .prev-btn {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    color: #666;
}

.modal-regis .next-btn {
    background-color: #ffa726;
    border: none;
    color: white;
}

.modal-regis .tab-content {
    display: none;
}

.modal-regis .tab-content.active {
    display: block;
}

.modal-regis .user-info-container {
    background-color: white;
    padding: 20px;
    border-radius: 15px;
    border: 1px solid #ffd8a8;
    background: linear-gradient(to right, #fffcf5, #fff5eb);
    border-radius: 20px;
    border: 2px solid var(--gradient-2, #eb2805);
    background: #fff;
    box-shadow: 0px 7px 35px 0px rgba(0, 0, 0, 0.15);
}

.user-info-container h6 {
    font-size: 20px;
    color: #333;
}

.modal-regis .user-info-container a {
    color: #fa8128;
    text-decoration: none;
}

.modal-regis .success-container {
    background-color: white;
    padding: 40px 20px;
    border-radius: 15px;
    text-align: center;
    /* background: linear-gradient(to right, #fffaf0, #fff5eb);
           */
}

.modal-regis .success-icon {
    width: 65px;
    height: 65px;
    background-color: #4caf50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: white;
    font-size: 40px;
}

.modal-regis .success-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #333;
}

.modal-regis .success-text {
    color: #666;
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 20px;
}

.modal-regis .start-course-btn {
    background: linear-gradient(to right, #ff8a00, #ff5722);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: bold;
    font-size: 16px;
    text-transform: uppercase;
    box-shadow: 0 4px 10px rgba(255, 138, 0, 0.2);
    width: 240px;
}

.modal-regis .contact-text {
    color: #666;
    margin-top: 20px;
}

.modal-regis .hotline {
    color: #ff5722;
    font-weight: bold;
}

.modal-regis .coupon-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
    margin-bottom: 15px;
}

.modal-regis .coupon-btn {
    background-color: white;
    border: 1px dashed #ff5722;
    border-radius: 30px;
    padding: 6px 15px;
    color: #ff5722;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s;
}

.modal-regis .coupon-btn:hover {
    background-color: #fff5f0;
    transform: translateY(-2px);
}

.modal-regis .coupon-tag {
    font-size: 10px;
    color: white;
    background-color: #ff5722;
    padding: 2px 4px;
    border-radius: 2px;
    margin-right: 5px;
}

.tab-switch-auth {
    border: none !important;
}

@media (max-width: 769px) {
    * {
    }

    .modal-auth .modal-body {
        padding: 20px 15px !important;
    }
}

@media (max-width: 500px) {
    * {
    }

    .col-password {
        flex-wrap: wrap;
    }

    .modal-auth .form-wrap {
        width: 100% !important;
    }

    .modal-auth .modal-header {
        font-size: 13px;
    }

    .modal-auth .modal-title {
        font-size: 18px;
    }

    .modal-auth .tab-content {
        padding: 20px 15px;
    }

    .modal-auth .tab {
        height: 51px;
    }

    .modal-auth .tab-switch-auth span {
        font-size: 16px;
    }
}

* {
}

.course-video-area {
    border-radius: 0;
    border: none !important;
}

.course-video-wrap .plyr--video {
    border-radius: 0 !important;
}

.course-video-wrap iframe {
    border-radius: 0 !important;
}

.wrap-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
}

.teacher-box img {
    position: relative;
    z-index: 10;
    width: 670px;
}

.teacher-box {
    position: relative;
    padding-right: 20px;
}

.expert-quote-box {
    display: flex;
}

.feed-wrapper {
    border-radius: 10px;
    border: 1px solid #fff;
    background: linear-gradient(
        269deg,
        #fff 58.16%,
        rgba(255, 255, 255, 0.47) 99.55%
    );
    box-shadow: 0px 4px 30px 0px rgba(186, 107, 50, 0.11);
    padding: 15px;
}

.feed-imgs {
    /* border-radius: 10px; */
    /* border: 1px solid #fff; */
    /* background: linear-gradient(
          269deg,
          #fff 58.16%,
          rgba(255, 255, 255, 0.47) 99.55%
        ); */
    /* box-shadow: 0px 4px 30px 0px rgba(186, 107, 50, 0.11); */
    /* padding: 15px; */
    gap: 15px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    /* margin-top: 20px; */
}

.feed-imgs img {
    max-width: 100%;
}

.feed-title-2 {
    color: #105ce4;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 12px;
}

.feedback-title {
    line-height: 90px;
    font-size: 56px;
    font-style: normal;
    font-weight: 800;
    text-align: center;
}

.achievement-badge-l {
    border-radius: 10px;
    border: 1px solid #fff;
    background: linear-gradient(
        269deg,
        #fff 58.16%,
        rgba(255, 255, 255, 0.47) 99.55%
    );
    display: inline-block;
    color: #333;
    padding: 10px 15px;
    position: absolute;
    z-index: 1;
    top: 108px;
    right: 18px;
}

.achievement-badge-l-2 {
    top: 230px;
    z-index: 20;
}

.achievement-number {
    background: var(
        --gradient-2,
        linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 20px;
    font-weight: bold;
}

.ach-title {
    font-size: 18px;
    color: #dc3f2e;
    border-radius: 24px;
    border: 1px solid #fff;
    background: linear-gradient(0deg, #fff4ce 0%, #fff 100%);
    box-shadow: 0px 7px 53px 0px rgba(0, 0, 0, 0.25);
    padding: 5px 15px;
    display: inline;
}

.achievement-title {
    font-size: 30px;
    font-weight: bold;
}

.container {
    max-width: 1200px;
}

.bg-orange-gradient {
    background: linear-gradient(180deg, #ff9a5b 0%, #ff5722 100%);
    height: 120px;
}

.target-section {
    /* background-color: #fff; */
    border-radius: 15px;
    margin-top: -30px;
    /* padding: 30px; */
    /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); */
    padding-top: 60px;
}

.course-badge {
    background-color: #2196f3;
    color: white;
    padding: 8px 20px;
    border-radius: 30px;
    display: inline-block;
    font-weight: 500;
    border-radius: 56px;
    border: 1px solid #89b3ff;
    background: var(
        --Gradient-color,
        linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%)
    );
    box-shadow: 0px -1px 11px 0px #688bff inset;
}

.section-title {
    color: #ff7215;
    font-size: 28px;
    font-weight: bold;
    margin-top: 15px;
}

.user-card {
    border-radius: 12px;
    /* overflow: hidden; */
    margin-bottom: 20px;
    transition: transform 0.3s;
    height: 100%;
    border: none;
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 60px 20px rgba(0, 0, 0, 0.1);
}

.user-card img {
    width: 100%;
    /* height: 200px; */
    object-fit: cover;
}

.user-type {
    color: #ff7215;
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 10px;
}

.user-description {
    color: #000;
    font-size: 16px;
    margin: 0;
}

/* Expert section styles */
.expert-section {
    /* border-radius: 15px; */
    padding: 40px 0;
    color: #fff;
    position: relative;
    overflow: hidden;
    background: url(../images/bg-htt.png) no-repeat center center;
    background-size: cover;
    padding-bottom: 0;
}

.expert-name {
    font-size: 48px;
    font-weight: 800;
    margin-bottom: 0;
    line-height: 80px;
    background: var(
        --liner-yellow,
        linear-gradient(180deg, #fff8e1 0%, #ffe898 100%)
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
}

.expert-title {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 30px;
    text-align: center;
}

.expert-quote {
    font-size: 18px;
    line-height: 1.5;
    margin-bottom: 20px;
    position: relative;
    padding-left: 40px;
    font-size: 18px;
    font-style: normal;
    /* font-weight: 800; */
    line-height: 20px; /* 111.111% */
    font-weight: bold;
    width: 410px;
    margin-top: 100px;
    max-width: 100%;
}

.expert-quote::before {
    /* content: '"';
        font-size: 60px;
        position: absolute;
        left: 0;
        top: -20px;
        opacity: 0.8; */
}

.achievement-badge {
    /* background-color: #fff; */
    color: #ffffff;
    border-radius: 15px;
    display: inline-block;
    margin-bottom: 20px;
    /* font-weight: bold; */
    /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
}

.achievement-text {
    /* margin-bottom: 5px; */
    font-size: 15px;
    line-height: 1.5;
    padding: 5px;
    margin-top: 5px;
}

/* FAQ section styles */
.faq-section {
    background: linear-gradient(90deg, #ff9a5b 0%, #ffb74d 100%);
    /* border-radius: 15px; */
    padding: 50px 0;
    color: #fff;
    /* margin-bottom: 30px; */
    text-align: center;
    background: url(../images/bg-ques.png) no-repeat center center;
    background-size: cover;
}

.faq-badge {
    background-color: #2196f3;
    color: white;
    padding: 8px 20px;
    border-radius: 30px;
    display: inline-block;
    font-weight: 500;
    margin-bottom: 15px;
    border-radius: 56px;
    border: 1px solid #89b3ff;
    background: var(
        --Gradient-color,
        linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%)
    );
    box-shadow: 0px -1px 11px 0px #688bff inset;
}

.faq-title {
    font-size: 38px;
    font-weight: bold;
    margin-bottom: 40px;
}

.faq-card {
    background-color: #fff;
    border-radius: 15px;
    margin-bottom: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.faq-question {
    padding: 20px;
    font-weight: 500;
    /* color: #ff5722; */
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    /* background: var(
          --gradient-2,
          linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
        ); */
    /* background-clip: text; */
    /* -webkit-background-clip: text; */
    /* -webkit-text-fill-color: transparent; */
    font-weight: 700;
    color: #444;
    text-align: left;
}

.faq-card.active .faq-question {
    padding: 20px;
    font-weight: 500;
    color: #ff5722;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    background: var(
        --gradient-2,
        linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 700;
}

.faq-answer {
    padding: 0 20px;
    color: #333;
    font-size: 14px;
    line-height: 1.6;
    max-height: 0;
    text-align: left;
    overflow: hidden;
    transition: max-height 0.4s ease, padding 0.4s ease;
}

.faq-icon {
    font-size: 24px;
    /* color: #ff5722; */
    transition: transform 0.3s ease;
    -webkit-text-fill-color: #ff5722;
}

.faq-card.active {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.faq-card.active .faq-answer {
    max-height: 200px; /* Adjust based on your content */
    padding: 0 20px 20px;
}

.faq-card.active .faq-icon {
    transform: rotate(180deg);
}

@media (max-width: 768px) {
    .user-card {
        margin-bottom: 20px;
    }

    .target-section {
        /* padding: 15px; */
    }

    .section-title {
        font-size: 24px;
    }
}

.txt-gradient-orange {
    background: var(
        --gradient-2,
        linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
    );
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.feedback-section {
    background: url(../images/bg-hv.png) no-repeat center center;
    padding-bottom: 60px;
    background-size: cover;
}

.box-phh {
    border-radius: 56px;
    border: 1px solid #89b3ff;
    background: var(
        --Gradient-color,
        linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%)
    );
    box-shadow: 0px -1px 11px 0px #688bff inset;
    display: inline-block;
    color: #fff;
    padding: 5px 15px;
}

@media (max-width: 768px) {
    * {
    }

    .playing-header-btns * {
        font-size: 14px !important;
    }

    .course-playing-header {
        padding: 0 10px;
    }

    .course-play-logo {
        display: none;
    }

    .playing-header-section .my-container {
        padding: 0 !important;
    }

    .playing-header-section .my-container .col-md-12 {
    }

    #forum-area {
        margin: 0;
    }

    .main .elearning-course-playlist {
        width: 100% !important;
        padding: 0 5px;
    }

    .main-content .course-video-navtab {
        border: none !important;
    }

    .elearning-course .course-video-info {
        padding: 0 15px;
    }

    .feed-imgs {
        grid-template-columns: repeat(3, 1fr);
    }

    .expert-section {
        background: linear-gradient(180deg, #ff9a5b 0%, #ff5722 100%);
    }
}

@media (max-width: 500px) {
    * {
    }

    .elearning-course .lesson-meta .badge span {
        /* display: none; */
    }

    .elearning-course .course-section .course-item-accordion-header-title {
        font-size: 16px !important;
    }

    .elearning-course .course-item-accordion-header.accordion-button {
        padding: 12px 10px;
    }

    .elearning-course .course-video-info-title {
        font-size: 16px !important;
    }

    .playing-header-btns {
        width: 100%;
        display: flex;
        justify-content: space-between;
    }

    .playing-video-title {
        display: none;
    }

    .ach-title {
        display: block !important;
    }

    .faq-title {
        font-size: 24px;
    }

    .user-info-wrapper .badge {
        padding: 2px 6px;
    }

    .user-info-wrapper .badge span {
        font-size: 10px;
    }

    .Userprofile img {
        width: 28px;
        height: auto;
    }

    .modules-section .module-details span {
        font-size: 13px;
    }

    .modules-section .registration-section-wrapper {
        border-radius: 0px 0px 15px 15px;
    }

    .modules-section .registration-section {
        padding: 20px 10px;
    }

    .course-video-navtab {
        min-height: auto;
    }

    .main-content .course-video-navtab > .tab-content > .tab-pane {
        padding: 14px !important;
    }

    .add-question h1 {
        font-size: 16px;
    }

    .divider.divider-movie {
        display: none !important;
    }

    .elearning-course .course-video-info-meta {
        flex-wrap: wrap;
    }

    .course-video-info-meta .divider:first-child {
        display: none;
    }

    .elearning-course .course-video-info-meta .course-badge-view {
        display: flex;
        margin-bottom: 5px;
        gap: 4px;
    }

    .modal-regis .payment-body {
        padding: 20px 15px;
    }

    .wrap-footer {
        flex-direction: column;
        gap: 10px;
    }

    .achievement-title {
        font-size: 24px;
    }

    .teacher-box {
        padding: 0;
        margin-bottom: 20px;
    }

    .feed-imgs {
        margin: 0;
        grid-template-columns: repeat(2, 1fr);
    }

    .expert-name {
        font-size: 33px;
    }

    .expert-quote {
        margin-top: 20px;
    }

    .expert-quote {
        padding: 0;
    }

    .expert-quote-box {
        flex-direction: column;
    }
}

.modal {
    color: #333;
    font-size: 16px;
}

.modal-regis .payment-header * {
    font-size: 16px;
}

.footer-call {
    display: flex;
    justify-content: center;
    margin: 20px 0;
}

.footer-call-btn {
    display: flex;
    align-items: center;
    background: linear-gradient(90deg, #ffa200 0%, #ff4800 100%);
    border-radius: 50px;
    padding: 10px 30px 10px 10px;
    text-decoration: none;
    box-shadow: 0px 4px 10px rgba(255, 85, 0, 0.3);
    transition: all 0.3s ease;
    border-radius: 56px;
    border: 1px solid #ffa688;
    background: var(
        --gradient-2,
        linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
    );
    box-shadow: 0px -1px 11px 0px #ffa868 inset;
    position: relative;
    padding-left: 45px;
}

.footer-call-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0px 6px 12px rgba(255, 85, 0, 0.4);
}

.call-icon {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    position: absolute;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #fff;
    font-size: 22px;
    border-radius: 56px;
    border: 1px solid #ffa688;
    background: var(
        --gradient-2,
        linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
    );
    box-shadow: 0px -1px 11px 0px #ffa868 inset;
    top: 50%;
    transform: translateY(-50%);
    left: -10px;
}

.call-number {
    color: white;
    font-weight: 700;
    font-size: 24px;
}

.footer-app-content {
    font-size: 20px;
    font-weight: bold;
    color: #333;
}

@media (max-width: 1024px) {
    .footer-app .logo-footer {
        width: 80px;
    }

    .footer-app .footer-app-content {
        font-size: 18px;
    }

    .footer-app .call-number {
        font-size: 18px;
    }

    .footer-app .footer-call-btn {
        padding: 10px 40px;
        padding-right: 14px;
    }
}

@media (max-width: 640px) {
    .footer-app .call-icon {
        width: 32px;
        height: 32px;

        font-size: 16px;
    }

    .footer-app .footer-call-btn {
        padding: 3px 28px;
        padding-right: 14px;
    }

    .footer-app .call-number {
        font-size: 16px;
    }

    .footer-app .footer-app-content {
        font-size: 16px;
    }
}

@media (max-width: 576px) {
    .footer-app .wrap-footer {
        flex-wrap: wrap;
    }

    .footer-app .footer-call {
        margin: 6px 0;
        width: 100%;
    }

    .footer-call-btn {
        padding: 8px 20px 8px 8px;
    }

    .call-icon {
        width: 32px;
        height: 32px;
        margin-right: 10px;
    }
}

#forum-area {
    padding: 0 !important;
    max-width: 100% !important;
}

.brand-section {
    background-color: #ffffff;
    box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.08);
    padding: 22px 0;
    position: relative;
}

.brand-section .container {
    max-width: 1176px;
    padding: 0;
}

.brand-section .brand-list {
    display: flex;
    gap: 47px;
}

.brand-section .brand-item {
    width: 12.5%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.brand-section img {
    max-height: 40px;
    transition: all 0.3s ease;
}

@media (max-width: 1280px) {
    .brand-section .brand-list {
        flex-wrap: wrap;
    }

    .brand-section .brand-item {
        width: 15%;
    }
}

@media (max-width: 1024px) {
    .brand-section .brand-item {
        width: 17.5%;
    }

    .brand-section .brand-list {
        gap: 24px;
    }
}

@media (max-width: 640px) {
    .brand-section .brand-item {
        width: calc(33.333333333333336% - 24px);
    }
}

@media (max-width: 500px) {
    .brand-section .brand-item {
        width: calc(50% - 24px);
    }

    .countdown-timer .timer-box .timer-box-label {
        font-size: 14px !important;
    }

    .modules-section .countdown-timer {
        font-size: 22px !important;
        gap: 10px !important;
    }

    .countdown-timer .timer-box {
        width: 60px !important;
        gap: 0 !important;
    }

    .modules-section .countdown-section {
        padding: 10px 20px !important;
    }
}

/* @media (max-width: 992px) {
	.brand-section img {
		max-height: 35px;
	}
}

@media (max-width: 768px) {
	.brand-section .row {
		max-width: 600px;
		margin: 0 auto;
	}
} */

.main > .container {
    display: flex;
    gap: 20px;
    max-width: 1360px;
    /* padding: 24px 0; */
}

#player_content .container {
    max-width: 100vw;
    padding: 20px 0px;
    max-width: calc(100vw - 50px);
}

.main .main-content {
    /*width: 66.66666666666667%;*/
}

.elearning-course .main-content {
    width: 80%;
    flex: 1;
}

.main .elearning-course-playlist {
    width: 500px;
}

.elearning-course .main-content .course-video-area {
    border: 0;
    padding: 0;
}

.main .elearning-course-playlist .course-list-container {
    max-height: calc(100vh - 90px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    top: 72px !important;
}

.elearning-course
    .elearning-course-playlist
    .course-list-container
    > .course-header {
    height: 52px;
    flex-shrink: 0;
}

.elearning-course
    .elearning-course-playlist
    .course-list-container
    > .course-section {
    flex: 1 1 auto;
    height: auto;
    overflow-y: auto;
}

.elearning-course .course-video-info {
    margin-top: 16px;
}

.elearning-course .course-video-info-title {
    color: #111827;
    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 18px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px; /* 155.556% */
}

.elearning-course .course-video-info-meta {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-top: 8px;
}

.course-video-info-meta .badge * {
    font-size: 11px !important;
}

.elearning-course .course-video-info-meta .user-badge span {
    color: white;
}

.elearning-course .course-video-info-meta span {
    color: #4b5563;
    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 150% */
}

.main-content .course-video-info-meta span > span {
    margin-left: 4px;
}

.main-content .course-video-info-meta .divider {
    width: 1px;
    height: 12px;
    border-radius: 9999px;
    display: inline-block;
    margin: 0 12px;
    background: #d1d5db;
}

.elearning-premium {
    width: 100%;
}

.elearning-premium .video-censorred.premium-video-container {
    background: rgba(0, 0, 0, 0.85);
    padding: 40px 20px;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    justify-content: center;
}

/* Premium video section specific styles */
.elearning-premium .premium-video-container {
    max-height: 567px;
    width: 100%;

    aspect-ratio: 16/9;
    border-radius: 8px;
}

.elearning-premium .premium-icon {
    margin-bottom: 12px;
}

.elearning-premium .premium-title {
    color: #ff8a00;
    font-weight: 500;
    margin-bottom: 16px;

    background: linear-gradient(92deg, #fe6d34 -0.1%, #facc15 100.1%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;

    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
}

.elearning-premium .premium-description {
    color: white;
    text-align: center;
    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    opacity: 0.8;
    margin-bottom: 24px;
}

.elearning-premium .premium-buttons {
    display: flex;
    gap: 12px;
}

.elearning-premium .btn-upgrade {
    border-radius: 8px;
    border: 1px solid #a5b4fc;
    background: linear-gradient(90deg, #818cf8 0%, #6366f1 100%);
    color: white;
    padding: 10px 20px;

    display: flex;
    align-items: center;
    gap: 12px;

    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
}

.elearning-premium .btn-free {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 10px 16px 10px 20px;
    border-radius: 8px;
    border: none;
    display: flex;
    align-items: center;
    gap: 8px;

    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
}

/* Hover effects */
.elearning-premium .btn-upgrade:hover {
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    color: white;
}

.elearning-premium .btn-free:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
}

.elearning-course .course-list-container > .course-header {
    gap: 0;
    margin-bottom: 0;
    border: 0;
}

.elearning-course .course-list-container {
    background: #fff;
    border-radius: 8px;
    font-family: "SVN-Mona Sans", sans-serif;
    border: 1px solid #eb2805;
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
}

.elearning-course .course-header > .nav-item {
    width: calc(50% - 12px);
}

.elearning-course .course-header > .nav-item.has-divider {
    width: calc(50% + 12px);
}

.elearning-course .course-header .course-divider {
    width: 22px;
    flex-shrink: 0;
    height: 53px;
    object-fit: cover;
    position: absolute;
    right: -22px;
    top: 0;
}

.elearning-course .course-header .nav-link {
    height: 100%;
    width: 100%;
    padding: 0;
    border-radius: 0;
    display: flex;
    align-items: center;
    gap: 0;
    background-color: transparent;
    position: relative;
}

.elearning-course .course-header .nav-link::after {
    display: none;
}

.elearning-course .course-header .nav-link:not(.active) > span {
    color: white;
}

.elearning-course .course-header .nav-link.active > span {
    position: relative;
    color: #eb2805;
}

.elearning-course .course-header .nav-link.active > span::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 1px;
    background-color: #eb2805;
}

.elearning-course .course-header > .course-menu span {
    border-top-left-radius: 7px;
}

.elearning-course .course-header > .course-update span {
    border-top-right-radius: 7px;
}

.elearning-course
    .course-header
    > .course-menu.nav-item
    > .nav-link:not(.active)
    > img {
    transform: scaleX(-1);
    right: 0;
}

.elearning-course .course-header .nav-link > span {
    height: 100%;
    width: 100%;
    gap: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
}

.elearning-course .course-header .nav-link.active > span {
    background-color: white;
}

.elearning-course .course-menu {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}

.elearning-course .course-update {
    color: #6366f1;
    font-size: 14px;
    font-weight: 500;
}

.elearning-course .course-section-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #0f172a;
    margin: 0;
}

.elearning-course
    .course-list-container
    .accordion
    > .accordion-item:last-child {
    border-bottom: 0;
}

.elearning-course .course-list-container .accordion-item {
    border: 0;
    border-bottom: 1px solid #e5e7eb;
    border-radius: 0;

    background-color: transparent;
}

.elearning-course .course-section .accordion-body {
    border-top: 1px solid #e5e7eb;
}

.elearning-course .course-item-accordion-header.accordion-button {
    background-color: transparent;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    box-shadow: none;
}

.elearning-course
    .course-item-accordion-header.accordion-button
    > .course-item-accordion-header-icon {
    margin: 0;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);

    width: 24px;
    height: 24px;
}

.elearning-course
    .course-item-accordion-header.accordion-button[aria-expanded="true"]
    > .course-item-accordion-header-icon {
    transform: translateY(-50%) rotate(180deg);
}

.elearning-course .course-item-accordion-header.accordion-button::after {
    display: none;
}

.elearning-course .course-item-accordion-header:focus {
    box-shadow: none;
    border: none;
    outline: none;
    border-radius: 0;
}

.elearning-course .course-section {
    background: #fff;
    border-radius: 0 0 7px 7px;
    padding: 15px 0;
}

.elearning-course .course-section .course-item-accordion-header-title {
    color: #111827;
    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px; /* 150% */
    letter-spacing: 0.5px;
    padding-right: 20px;
}

.elearning-course .course-meta {
    margin-top: 4px;
    color: #262626;
    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px; /* 145.455% */
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.elearning-course .course-meta .total-time-value,
.elearning-course .course-meta .lesson-count-value {
    font-weight: 500;
    margin-left: 4px;
}

.elearning-course .course-meta .divider {
    width: 1px;
    height: 12px;
    border-radius: 9999px;
    display: inline-block;
    margin: 0 12px;
    background: #d1d5db;
}

.elearning-course .course-section .accordion-body {
    padding: 0px;
}

.elearning-course .course-lessons > .lesson-item {
    display: flex;
    margin-top: 0px;
    padding: 8px 15px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.elearning-course .course-lessons > .lesson-item:hover {
    background-color: #f5f5f5;
}

.elearning-course .course-lessons > .lesson-item:first-child {
    margin-top: 0;
}

.elearning-course .course-lessons .lesson-content-header {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.elearning-course .lesson-thumbnail img {
    width: 22px;
    height: 22px;
    object-fit: cover;
}

.elearning-course .lesson-content {
    flex: 1;
    padding-left: 10px;
}

.elearning-course .lesson-content a {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    color: #111827;
    text-overflow: ellipsis;
    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: ;
    line-height: 20px; /* 153.846% */
    cursor: pointer;
}

.elearning-course .lesson-item a:hover {
    color: #6366f1;
}

.elearning-course .lesson-meta {
    display: flex;
    align-items: center;
    gap: 0px;
}

.user-badge {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    gap: 6px;
    color: #fff;
    text-align: center;

    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 11px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
}

.user-badge.free-badge {
    border-radius: 5px;
    border: 1px solid #89b3ff;
    background: linear-gradient(270deg, #105ce4 29.9%, #45c3fa 100%);
}

.user-badge.pro-badge {
    border-radius: 5px;
    border: 1px solid #ffa688;
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
}

.elearning-course .duration {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 6px;
    border: 1px solid #f3f4f6;
    background: #ededed;
    color: #4b5563;
    text-overflow: ellipsis;
    font-family: "SVN-Mona Sans", Inter, sans-serif;
    font-size: 11px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
}

.main-content .course-video-navtab {
    box-shadow: none;
    margin-top: 20px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    padding: 0;
    background: #fff;
}

.main-content .course-video-navtab > .nav-pills {
    padding: 0 20px;
    margin-bottom: 0;
}

.main-content .course-video-navtab > .tab-content > .tab-pane {
    padding: 20px;
}

.main-content
    .course-video-navtab
    > .tab-content
    .tab-pane.course-list-container {
    padding: 0;
}

.main-content .course-video-navtab > .nav-pills .nav-link {
    padding: 14px 0;
}

.course-video-navtab .course-menu-tab {
    display: none;
}

.course-video-navtab .course-list-container {
    display: none;
}

.course-video-navtab .nav-pills .nav-link:focus,
.course-video-navtab .nav-pills .nav-link:focus-visible {
    box-shadow: none;
}

@media (max-width: 1360px) {
    .main > .container {
        justify-content: center;
        padding: 24px;
        /* flex-direction: column; */
    }

    .main .main-content {
        /*width: 66.66666666666667%;*/
        /*min-width: 900px;*/
    }

    .elearning-course .elearning-course-playlist {
        display: none;
    }

    .course-video-navtab .course-menu-tab {
        display: block;
    }

    .course-video-navtab .course-list-container.show {
        display: block;
    }
}

@media (max-width: 1024px) {
    .main > .container {
        padding: 24px 16px;
        flex-direction: column;
        max-width: 100vw !important;
        padding: 0 !important;
        gap: 0;
    }
    .main .main-content {
        width: 100%;
        min-width: unset;
    }
}

@media (max-width: 768px) {
    /*.main > .container {*/
    /*    padding: 24px 16px;*/
    /*    flex-direction: column;*/
    /*    max-width: 100vw !important;*/
    /*    padding: 0 !important;*/
    /*    gap: 0;*/
    /*}*/

    .elearning-premium .premium-title {
        font-size: 20px;
        line-height: 1.15;
    }

    .main .elearning-course .table-responsive > .table {
        min-width: 700px;
    }

    .elearning-course .forum-tab-content .add-question {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .elearning-course .forum-tab-content .add-question #create-question {
        margin-left: auto;
        padding: 8px 16px;
        font-size: 14px;
    }

    .elearning-course .questions .parent-review .review-title {
        white-space: normal;
    }

    .elearning-course .questions .parent-review .review-content {
        white-space: normal;
    }

    .elearning-course .questions .parent-review .review-actions {
        flex-wrap: wrap;
    }

    .elearning-course .questions .parent-review + .child-review {
        padding-left: 1rem !important;
    }
}

@media (max-width: 640px) {
    .main > .container {
        padding: 0;
        padding-bottom: 24px;
    }

    .elearning-course .main-content .course-video-area {
        border-radius: 0;
    }

    .main-content .course-video-navtab {
        margin-top: 16px;
        border-radius: 0;
        border-left: 0;
        border-right: 0;
        border-bottom: 0;
    }

    .main-content .course-video-navtab > .nav-pills {
        padding: 0 16px;
        gap: 20px;
        overflow: auto hidden;
        max-width: 100%;
        flex-wrap: nowrap;
    }

    .course-video-navtab .nav-pills .nav-link > span {
        white-space: nowrap;
    }

    .elearning-course .course-header > .course-menu span {
        border-top-left-radius: 0;
    }

    .elearning-course .course-header > .course-update span {
        border-top-right-radius: 0;
    }

    .elearning-course .course-list-container {
        border: 0;
        border-radius: 0;
    }

    .elearning-course .course-video-info {
        margin-top: 12px;
        padding: 0 16px;
    }

    .elearning-course .course-video-wrap,
    .elearning-premium .premium-video-container {
        border-radius: 0;
    }

    .elearning-premium .video-censorred.premium-video-container {
        padding: 20px 24px;
    }

    .elearning-course
        .elearning-course-playlist
        .course-list-container
        > .course-section {
        overflow-y: inherit;
    }

    .main .elearning-course-playlist .course-list-container {
        height: auto;
        max-height: inherit;
    }

    .elearning-course .course-section {
        border-radius: 0;
    }

    .elearning-premium .premium-title {
        font-size: 16px;
    }

    .elearning-premium .premium-description {
        margin-bottom: 12px;
    }

    .elearning-premium .premium-buttons {
        flex-direction: column;
    }

    .elearning-premium .btn-upgrade,
    .elearning-premium .btn-free {
        padding: 8px 12px;
        justify-content: center;
    }

    .elearning-premium .btn-upgrade img,
    .elearning-premium .btn-free img {
        width: 16px;
    }

    .elearning-course .course-header .nav-link > span {
        font-size: 12px;
        line-height: 1.25;
    }
}

.modules-section .tier-item.active .tier-badge {
    background: #fa8128;
    color: #fff !important;
}

.elearning-course .lesson-meta .badge {
    display: flex;
    gap: 3px;
    align-items: center;
    font-size: 10px;
    margin-right: 5px;
}

@media (min-width: 769px) {
    .main .elearning-course-playlist {
        display: block !important;
    }
}

.breadcum-area {
    background: linear-gradient(90deg, #f68034 26%, #f67f33 93%);
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px white inset !important;
}

.start-course-btn {
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    color: #fff;
    text-transform: uppercase;
    font-weight: 600;
    padding: 8px 10px;
    border-radius: 30px;
    display: flex;
    gap: 6px;
    justify-content: center;
}

.row {
    max-width: 100vw;
}
.upgrade-btn {
    background: linear-gradient(270deg, #eb2805 0%, #fbc30b 100%);
    color: white;
    border: none;
    padding: 8px 13px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 80px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
}

.upgrade-btn:hover {
background-color: #e04d00;
transform: translateY(-2px);
}

.upgrade-btn:active {
transform: scale(0.98);
}
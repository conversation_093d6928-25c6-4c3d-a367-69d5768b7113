/* When the sticky-active class is added after scrolling */
.header.sticky {
	box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
	animation: slideDown 0.5s ease forwards;
	background: #fff;
}

.gradient-text {
	background: linear-gradient(180deg, #ff372a 22.74%, #cd1f00 77.34%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.hero-left-image {
	stroke-width: 3px;
	stroke: #fff;
	filter: drop-shadow(0px 4px 33px rgba(255, 255, 255, 0.55));
}

.animate-icon {
	outline: 1px solid;
	animation: ring 1s ease-in-out infinite;
}

.line-clamp-2 {
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}

/* Story Section Styles */
.story {
	padding-top: 4rem;
	padding-bottom: 6rem;
	position: relative;
}

.story .story-bg {
	z-index: 0;
}

.story .story-bg img {
	height: max(100%, 1528px);
}

.story .playball-font {
	font-family: "Playball", cursive;
}

.story .playball-font span {
	display: inline-block;
}

/* Gallery styles */
.story .grid {
	grid-template-columns: repeat(4, 1fr);
}

.story .grid img {
	transition: transform 0.3s ease;
}

.story .grid img:hover {
	transform: scale(1.03);
}

/* Courses Section Styles */

.courses .course-card {
	box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.2);
	border-radius: 30px;
	overflow: hidden;
	transition: all 0.3s ease;
}

.courses .course-card:hover {
	transform: translateY(-5px);
}

.courses .course-card img {
	transition: transform 0.5s ease;
}

.courses .course-card .course-tag {
	border: 1px solid #fff;
	background: #f6f4e9;
	box-shadow: 0px 0px 5.48px 0px #dac560 inset;
}

.courses .shadow-lg {
	box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.courses .bg-white {
	background-color: #ffffff;
}

/* Reason Section Styles */
.reason {
	padding-top: 5rem;
	padding-bottom: 8rem;
	position: relative;
}

.reason .rounded-xl {
	border-radius: 1rem;
	overflow: hidden;
}

.reason .rounded-2xl {
	border-radius: 1.5rem;
	overflow: hidden;
}

.reason .shadow-lg {
	box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.reason h2 {
	text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.reason .bg-red-100 {
	background-color: rgba(254, 226, 226, 0.8);
}

.reason .bg-white {
	background-color: #ffffff;
	transition: all 0.3s ease;
}

.reason .bg-white:hover {
	transform: translateY(-5px);
	box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

@media (max-width: 768px) {
	.story {
		padding-top: 3rem;
		padding-bottom: 4rem;
	}

	.story .grid {
		grid-template-columns: repeat(2, 1fr);
	}

	.reason {
		padding-top: 3rem;
		padding-bottom: 6rem;
	}
}

.faq .faq-item:hover .faq-question,
.faq .faq-item.active .faq-question {
	color: var(--red-color, #cd1f00);
}

/* Course Button Animations */

.courses .course-button:hover {
	transform: scale(1.05) translateY(-2px);
	box-shadow: 0 15px 30px rgba(255, 223, 134, 0.4), 0 0 20px rgba(255, 223, 134, 0.6);
}

.courses .course-button:hover .course-icon {
	transform: rotate(360deg) scale(1.1);
}

.courses .course-button:hover .course-text {
	letter-spacing: 0.1em;
	color: #b91c1c;
}

.courses .course-icon {
	transition: transform 0.6s ease-in-out;
}

/* Register Button Animations */
.reason .register-button:hover {
	transform: scale(1.05) translateY(-2px);
	box-shadow: 0 15px 30px rgba(255, 223, 134, 0.4), 0 0 20px rgba(255, 223, 134, 0.6);
}

.reason .register-button:hover .register-text {
	letter-spacing: 0.1em;
	color: #b91c1c;
}

.reason .register-icon {
	transition: transform 0.6s ease-in-out;
}

.reason .register-text {
	transition: all 0.3s ease;
	animation: registerTextShimmer 3s ease-in-out infinite;
}

/* Testimonia Button Animations */
.testimonia .testimonia-button {
	animation: testimoniaGlow 2s ease-in-out infinite alternate, bounce 1s infinite;
}

.testimonia .testimonia-button:hover {
	transform: scale(1.05) translateY(-2px);
	box-shadow: 0 15px 30px rgba(255, 223, 134, 0.4), 0 0 20px rgba(255, 223, 134, 0.6);
}

.testimonia .testimonia-button:hover .testimonia-text {
	color: #b91c1c;
}

.testimonia .testimonia-icon {
	transition: transform 0.6s ease-in-out;
}

.testimonia .testimonia-text {
	transition: all 0.3s ease;
}

/* Animation for the header to slide down when becoming sticky */
@keyframes slideDown {
	0% {
		transform: translateY(-100%);
	}
	100% {
		transform: translateY(0);
	}
}

@keyframes ring {
	0% {
		outline-color: rgba(205, 31, 0, 0.5);
		outline-offset: -3px;
	}
	100% {
		outline-color: rgba(205, 31, 0, 0);
		outline-offset: 15px;
	}
}

@keyframes wiggle {
	0%,
	100% {
		--tw-rotate: -3deg;
	}
	50% {
		--tw-rotate: 3deg;
	}
}

@keyframes registerTextShimmer {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0.9;
	}
}

@keyframes testimoniaGlow {
	0% {
		box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.2), 0 0 15px rgba(255, 223, 134, 0.3);
	}
	100% {
		box-shadow: 0px 8px 20px rgba(0, 0, 0, 0.3), 0 0 25px rgba(255, 223, 134, 0.6);
	}
}

@keyframes bounce {
	0%,
	100% {
		transform: translateY(-25%);
		animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
	}
	50% {
		transform: none;
		animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
	}
}

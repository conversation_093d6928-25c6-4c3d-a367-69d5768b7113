/**
 * Demo version of Curriculum Builder
 */

document.addEventListener('DOMContentLoaded', function() {
    const sectionsContainer = document.getElementById('curriculum-container');
    
    if (!sectionsContainer) return;

    // Initialize section sortable
    new Sortable(sectionsContainer, {
        animation: 300,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        dragClass: 'sortable-drag',
        handle: '.section-drag-handle',
        onEnd: function(evt) {
            updateSectionNumbers();
            showNotification('Đã cập nhật thứ tự sections', 'success');
        }
    });

    // Initialize lesson sortables
    const lessonContainers = document.querySelectorAll('.section-lessons');
    
    lessonContainers.forEach(container => {
        new Sortable(container, {
            group: 'lessons',
            animation: 300,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            handle: '.lesson-drag-handle',
            onEnd: function(evt) {
                updateLessonCounts();
                showNotification('Đã cập nhật thứ tự bài học', 'success');
            },
            onMove: function(evt) {
                const related = evt.related;
                if (related && related.classList.contains('section-lessons')) {
                    related.classList.add('drag-over');
                    setTimeout(() => {
                        related.classList.remove('drag-over');
                    }, 300);
                }
            }
        });
    });

    // Section toggle functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.section-toggle')) {
            e.preventDefault();
            const toggle = e.target.closest('.section-toggle');
            const sectionCard = toggle.closest('.section-card');
            const lessonsContainer = sectionCard.querySelector('.section-lessons');
            
            toggleSection(toggle, lessonsContainer);
        }
    });

    function toggleSection(toggle, lessonsContainer) {
        const icon = toggle.querySelector('i');
        
        if (lessonsContainer.style.display === 'none') {
            // Expand
            lessonsContainer.style.display = 'block';
            lessonsContainer.classList.add('expanding');
            icon.classList.remove('fi-rr-angle-down');
            icon.classList.add('fi-rr-angle-up');
            toggle.classList.remove('collapsed');
            
            setTimeout(() => {
                lessonsContainer.classList.remove('expanding');
            }, 300);
        } else {
            // Collapse
            lessonsContainer.classList.add('collapsing');
            icon.classList.remove('fi-rr-angle-up');
            icon.classList.add('fi-rr-angle-down');
            toggle.classList.add('collapsed');
            
            setTimeout(() => {
                lessonsContainer.style.display = 'none';
                lessonsContainer.classList.remove('collapsing');
            }, 300);
        }
    }

    function updateSectionNumbers() {
        const sectionCards = sectionsContainer.querySelectorAll('.section-card');
        
        sectionCards.forEach((card, index) => {
            const numberSpan = card.querySelector('.section-number');
            if (numberSpan) {
                numberSpan.textContent = (index + 1) + '.';
            }
        });
    }

    function updateLessonCounts() {
        const sectionCards = document.querySelectorAll('.section-card');
        
        sectionCards.forEach(card => {
            const lessonsContainer = card.querySelector('.section-lessons');
            const lessonItems = lessonsContainer.querySelectorAll('.lesson-item');
            const countElement = card.querySelector('.section-info small');
            
            if (countElement) {
                countElement.textContent = `${lessonItems.length} bài học`;
            }
        });
    }

    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
});

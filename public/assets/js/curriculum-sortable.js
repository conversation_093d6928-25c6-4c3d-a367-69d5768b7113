/**
 * Modern Curriculum Builder with Drag & Drop
 * Uses SortableJS for advanced drag and drop functionality
 */

console.log('Curriculum Sortable JS loaded');

class CurriculumBuilder {
    constructor() {
        console.log('CurriculumBuilder constructor called');
        this.sectionsContainer = document.getElementById('curriculum-container');
        this.courseId = this.getCourseId();
        console.log('Sections container:', this.sectionsContainer);
        console.log('Course ID:', this.courseId);
        this.init();
    }

    init() {
        if (!this.sectionsContainer) {
            console.error('Curriculum container not found!');
            return;
        }

        console.log('Initializing curriculum builder...');
        this.initSectionSortable();
        this.initLessonSortables();
        this.initSectionToggles();

        // Initialize tooltips after a short delay to ensure DOM is ready
        setTimeout(() => {
            this.initTooltips();
        }, 100);
    }

    getCourseId() {
        // Extract course ID from URL or data attribute
        const urlParts = window.location.pathname.split('/');
        const courseId = urlParts[urlParts.length - 1];

        // If URL doesn't contain course ID, try to get from container data attribute
        if (!courseId || isNaN(courseId)) {
            const container = document.querySelector('[data-course-id]');
            return container ? container.dataset.courseId : null;
        }

        return courseId;
    }

    initSectionSortable() {
        console.log('Initializing section sortable...');
        if (typeof Sortable === 'undefined') {
            console.error('SortableJS not loaded!');
            return;
        }

        new Sortable(this.sectionsContainer, {
            animation: 300,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            dragClass: 'sortable-drag',
            handle: '.section-drag-handle',
            onEnd: (evt) => {
                console.log('Section moved:', evt);
                this.updateSectionOrder();
            }
        });
        console.log('Section sortable initialized');
    }

    initLessonSortables() {
        console.log('Initializing lesson sortables...');
        const lessonContainers = document.querySelectorAll('.section-lessons');
        console.log('Found lesson containers:', lessonContainers.length);

        lessonContainers.forEach((container, index) => {
            console.log(`Initializing sortable for container ${index}:`, container);

            new Sortable(container, {
                group: 'lessons', // Allow moving between sections
                animation: 300,
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',
                handle: '.lesson-drag-handle',
                onStart: (evt) => {
                    console.log('Lesson drag started:', evt);
                },
                onEnd: (evt) => {
                    console.log('Lesson moved:', evt);
                    this.updateLessonOrder(evt);
                },
                onMove: (evt) => {
                    console.log('Lesson moving:', evt);
                    // Add visual feedback when dragging over sections
                    const related = evt.related;
                    if (related && related.classList.contains('section-lessons')) {
                        related.classList.add('drag-over');
                        setTimeout(() => {
                            related.classList.remove('drag-over');
                        }, 300);
                    }
                }
            });
        });
        console.log('Lesson sortables initialized');
    }

    initSectionToggles() {
        document.addEventListener('click', (e) => {
            if (e.target.closest('.section-toggle')) {
                e.preventDefault();
                const toggle = e.target.closest('.section-toggle');
                const sectionCard = toggle.closest('.section-card');
                const lessonsContainer = sectionCard.querySelector('.section-lessons');
                
                this.toggleSection(toggle, lessonsContainer);
            }
        });
    }

    initTooltips() {
        // Initialize Bootstrap tooltips if available
        if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    toggleSection(toggle, lessonsContainer) {
        const icon = toggle.querySelector('i');
        
        if (lessonsContainer.style.display === 'none') {
            // Expand
            lessonsContainer.style.display = 'block';
            lessonsContainer.classList.add('expanding');
            icon.classList.remove('fi-rr-angle-down');
            icon.classList.add('fi-rr-angle-up');
            toggle.classList.remove('collapsed');
            
            setTimeout(() => {
                lessonsContainer.classList.remove('expanding');
            }, 300);
        } else {
            // Collapse
            lessonsContainer.classList.add('collapsing');
            icon.classList.remove('fi-rr-angle-up');
            icon.classList.add('fi-rr-angle-down');
            toggle.classList.add('collapsed');
            
            setTimeout(() => {
                lessonsContainer.style.display = 'none';
                lessonsContainer.classList.remove('collapsing');
            }, 300);
        }
    }

    updateSectionOrder() {
        const sectionIds = [];
        const sectionCards = this.sectionsContainer.querySelectorAll('.section-card');
        
        sectionCards.forEach((card, index) => {
            const sectionId = card.dataset.sectionId;
            sectionIds.push(sectionId);
            
            // Update section number display
            const numberSpan = card.querySelector('.section-number');
            if (numberSpan) {
                numberSpan.textContent = (index + 1) + '.';
            }
        });

        this.saveSectionOrder(sectionIds);
    }

    updateLessonOrder(evt) {
        const lessonItem = evt.item;
        const newSectionContainer = evt.to;
        const oldSectionContainer = evt.from;
        
        const lessonId = lessonItem.dataset.lessonId;
        const newSectionId = newSectionContainer.dataset.sectionId;
        const oldSectionId = oldSectionContainer.dataset.sectionId;
        
        // Update lesson order within the section
        const lessonIds = [];
        const lessonItems = newSectionContainer.querySelectorAll('.lesson-item');
        
        lessonItems.forEach(item => {
            lessonIds.push(item.dataset.lessonId);
        });

        // If lesson moved to different section, update section_id
        if (newSectionId !== oldSectionId) {
            this.updateLessonSection(lessonId, newSectionId, lessonIds);
        } else {
            this.saveLessonOrder(newSectionId, lessonIds);
        }

        // Update lesson counts
        this.updateLessonCounts();
    }

    updateLessonSection(lessonId, newSectionId, lessonIds) {
        const formData = new FormData();
        formData.append('lesson_id', lessonId);
        formData.append('section_id', newSectionId);
        formData.append('lesson_order', JSON.stringify(lessonIds));
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);

        fetch('/admin/lesson/move-section', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showNotification('Đã di chuyển bài học thành công', 'success');
            } else {
                this.showNotification('Có lỗi xảy ra khi di chuyển bài học', 'error');
                location.reload(); // Reload on error
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showNotification('Có lỗi xảy ra', 'error');
            location.reload();
        });
    }

    saveSectionOrder(sectionIds) {
        const formData = new FormData();
        formData.append('itemJSON', JSON.stringify(sectionIds));
        formData.append('course_id', this.courseId);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);

        fetch('/admin/section/sort', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            this.showNotification('Đã cập nhật thứ tự sections', 'success');
        })
        .catch(error => {
            console.error('Error:', error);
            this.showNotification('Có lỗi xảy ra khi sắp xếp sections', 'error');
        });
    }

    saveLessonOrder(sectionId, lessonIds) {
        const formData = new FormData();
        formData.append('itemJSON', JSON.stringify(lessonIds));
        formData.append('section_id', sectionId);
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').content);

        fetch('/admin/lesson/sort', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            this.showNotification('Đã cập nhật thứ tự bài học', 'success');
        })
        .catch(error => {
            console.error('Error:', error);
            this.showNotification('Có lỗi xảy ra khi sắp xếp bài học', 'error');
        });
    }

    updateLessonCounts() {
        const sectionCards = document.querySelectorAll('.section-card');
        
        sectionCards.forEach(card => {
            const lessonsContainer = card.querySelector('.section-lessons');
            const lessonItems = lessonsContainer.querySelectorAll('.lesson-item');
            const countElement = card.querySelector('.section-info small');
            
            if (countElement) {
                countElement.textContent = `${lessonItems.length} bài học`;
            }
        });
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new CurriculumBuilder();
});

// Export for global access
window.CurriculumBuilder = CurriculumBuilder;

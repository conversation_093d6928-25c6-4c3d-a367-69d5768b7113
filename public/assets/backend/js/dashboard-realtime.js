/**
 * Real-time Dashboard Updates
 * Handles live data updates and WebSocket connections
 */

class DashboardRealtime {
    constructor(options = {}) {
        this.options = {
            updateInterval: 30000, // 30 seconds
            enableWebSocket: false,
            apiEndpoint: '/api/dashboard/stats',
            ...options
        };
        
        this.isActive = true;
        this.updateTimer = null;
        this.websocket = null;
        
        this.init();
    }
    
    init() {
        if (this.options.enableWebSocket) {
            this.initWebSocket();
        } else {
            this.initPolling();
        }
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
        
        // Handle window focus/blur
        window.addEventListener('focus', () => this.resume());
        window.addEventListener('blur', () => this.pause());
    }
    
    initPolling() {
        this.updateTimer = setInterval(() => {
            if (this.isActive) {
                this.fetchUpdates();
            }
        }, this.options.updateInterval);
        
        // Initial fetch
        this.fetchUpdates();
    }
    
    initWebSocket() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/dashboard`;
            
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('Dashboard WebSocket connected');
                this.showConnectionStatus('connected');
            };
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleUpdate(data);
            };
            
            this.websocket.onclose = () => {
                console.log('Dashboard WebSocket disconnected');
                this.showConnectionStatus('disconnected');
                
                // Fallback to polling
                setTimeout(() => {
                    this.initPolling();
                }, 5000);
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.showConnectionStatus('error');
            };
        } catch (error) {
            console.error('WebSocket initialization failed:', error);
            this.initPolling();
        }
    }
    
    async fetchUpdates() {
        try {
            const response = await fetch(this.options.apiEndpoint, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf_token"]')?.content
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                this.handleUpdate(data);
                this.showConnectionStatus('connected');
            } else {
                throw new Error(`HTTP ${response.status}`);
            }
        } catch (error) {
            console.error('Failed to fetch dashboard updates:', error);
            this.showConnectionStatus('error');
        }
    }
    
    handleUpdate(data) {
        // Update statistics cards
        if (data.statistics) {
            this.updateStatistics(data.statistics);
        }
        
        // Update charts
        if (data.chartData) {
            this.updateCharts(data.chartData);
        }
        
        // Update notifications
        if (data.notifications) {
            this.updateNotifications(data.notifications);
        }
        
        // Update recent activities
        if (data.activities) {
            this.updateActivities(data.activities);
        }
        
        // Trigger custom event
        document.dispatchEvent(new CustomEvent('dashboardUpdated', {
            detail: data
        }));
    }
    
    updateStatistics(stats) {
        Object.keys(stats).forEach(key => {
            const element = document.querySelector(`[data-stat="${key}"]`);
            if (element) {
                const currentValue = parseInt(element.textContent.replace(/,/g, ''));
                const newValue = stats[key];
                
                if (currentValue !== newValue) {
                    this.animateNumberChange(element, currentValue, newValue);
                    
                    // Show trend indicator
                    this.showTrendIndicator(element, currentValue, newValue);
                }
            }
        });
    }
    
    animateNumberChange(element, from, to) {
        const duration = 1000;
        const startTime = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(from + (to - from) * this.easeOutQuart(progress));
            element.textContent = current.toLocaleString('vi-VN');
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    }
    
    easeOutQuart(t) {
        return 1 - Math.pow(1 - t, 4);
    }
    
    showTrendIndicator(element, oldValue, newValue) {
        const container = element.closest('.stats-card');
        if (!container) return;
        
        let trendElement = container.querySelector('.trend-indicator');
        if (!trendElement) {
            trendElement = document.createElement('div');
            trendElement.className = 'trend-indicator';
            container.appendChild(trendElement);
        }
        
        const change = newValue - oldValue;
        const percentage = oldValue > 0 ? ((change / oldValue) * 100).toFixed(1) : 0;
        
        if (change > 0) {
            trendElement.className = 'trend-indicator trend-up';
            trendElement.innerHTML = `<i class="fi-rr-arrow-up"></i> +${percentage}%`;
        } else if (change < 0) {
            trendElement.className = 'trend-indicator trend-down';
            trendElement.innerHTML = `<i class="fi-rr-arrow-down"></i> ${percentage}%`;
        } else {
            trendElement.className = 'trend-indicator trend-neutral';
            trendElement.innerHTML = `<i class="fi-rr-minus"></i> 0%`;
        }
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            trendElement.style.opacity = '0';
        }, 5000);
    }
    
    updateCharts(chartData) {
        // Update revenue chart if it exists
        if (window.revenueChart && chartData.monthly_amount) {
            window.revenueChart.data.datasets.forEach((dataset, index) => {
                if (chartData.monthly_amount.total_revenue && index === 0) {
                    dataset.data = chartData.monthly_amount.total_revenue;
                }
                if (chartData.monthly_amount.affiliate_amount && index === 1) {
                    dataset.data = chartData.monthly_amount.affiliate_amount;
                }
                if (chartData.monthly_amount.actual_revenue && index === 2) {
                    dataset.data = chartData.monthly_amount.actual_revenue;
                }
            });
            window.revenueChart.update('none'); // No animation for real-time updates
        }
    }
    
    updateNotifications(notifications) {
        notifications.forEach(notification => {
            if (window.DashboardUtils && window.DashboardUtils.showNotification) {
                window.DashboardUtils.showNotification(
                    notification.message,
                    notification.type,
                    notification.duration
                );
            }
        });
    }
    
    updateActivities(activities) {
        const container = document.querySelector('.recent-activities');
        if (container && activities.length > 0) {
            // Update recent activities list
            const list = container.querySelector('.activity-list');
            if (list) {
                activities.slice(0, 5).forEach(activity => {
                    const item = this.createActivityItem(activity);
                    list.insertBefore(item, list.firstChild);
                    
                    // Remove old items if more than 10
                    const items = list.querySelectorAll('.activity-item');
                    if (items.length > 10) {
                        items[items.length - 1].remove();
                    }
                });
            }
        }
    }
    
    createActivityItem(activity) {
        const item = document.createElement('div');
        item.className = 'activity-item';
        item.innerHTML = `
            <div class="activity-icon">
                <i class="${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-title">${activity.title}</div>
                <div class="activity-time">${activity.time}</div>
            </div>
        `;
        return item;
    }
    
    showConnectionStatus(status) {
        let indicator = document.querySelector('.connection-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'connection-indicator';
            document.body.appendChild(indicator);
        }
        
        indicator.className = `connection-indicator connection-${status}`;
        
        const messages = {
            connected: '🟢 Kết nối',
            disconnected: '🟡 Mất kết nối',
            error: '🔴 Lỗi kết nối'
        };
        
        indicator.textContent = messages[status] || status;
        
        // Auto-hide success status
        if (status === 'connected') {
            setTimeout(() => {
                indicator.style.opacity = '0';
            }, 3000);
        } else {
            indicator.style.opacity = '1';
        }
    }
    
    pause() {
        this.isActive = false;
        if (this.websocket) {
            this.websocket.close();
        }
    }
    
    resume() {
        this.isActive = true;
        if (!this.websocket || this.websocket.readyState === WebSocket.CLOSED) {
            if (this.options.enableWebSocket) {
                this.initWebSocket();
            } else if (!this.updateTimer) {
                this.initPolling();
            }
        }
    }
    
    destroy() {
        this.isActive = false;
        
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
        }
        
        if (this.websocket) {
            this.websocket.close();
        }
        
        const indicator = document.querySelector('.connection-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
}

// Initialize real-time updates when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize on dashboard page
    if (window.location.pathname.includes('/admin/dashboard')) {
        window.dashboardRealtime = new DashboardRealtime({
            updateInterval: 30000, // 30 seconds
            enableWebSocket: false, // Set to true if WebSocket server is available
        });
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.dashboardRealtime) {
        window.dashboardRealtime.destroy();
    }
});

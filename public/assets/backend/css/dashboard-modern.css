/* Modern Dashboard Enhancements */

/* Dashboard Header Gradient */
.dashboard-header-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.dashboard-header-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* Modern Card Shadows */
.modern-card-shadow {
    box-shadow: 
        0 4px 20px rgba(0, 0, 0, 0.08),
        0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card-shadow:hover {
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.12),
        0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Gradient Backgrounds */
.gradient-courses {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-lessons {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-enrollments {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-students {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.gradient-instructors {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* Modern Typography */
.modern-title {
    font-weight: 700;
    color: #2d3748;
    line-height: 1.2;
}

.modern-subtitle {
    color: #718096;
    font-weight: 500;
}

/* Enhanced Buttons */
.btn-modern {
    border-radius: 12px;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.2s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-modern:hover::before {
    left: 100%;
}

.btn-modern:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Loading Animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Chart Container Enhancements */
.chart-modern {
    background: white;
    border-radius: 20px;
    padding: 30px;
    position: relative;
    overflow: hidden;
}

.chart-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #4facfe);
}

/* Responsive Grid Enhancements */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
    }
    
    .chart-modern {
        padding: 20px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .modern-card-shadow {
        background: #1a202c;
        color: #e2e8f0;
    }
    
    .modern-title {
        color: #f7fafc;
    }
    
    .modern-subtitle {
        color: #a0aec0;
    }
}

/* Accessibility Enhancements */
.focus-visible {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print Styles */
@media print {
    .dashboard-header-gradient {
        background: #f8f9fa !important;
        color: #000 !important;
    }
    
    .modern-card-shadow {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .modern-card-shadow {
        border: 2px solid #000;
    }
    
    .btn-modern {
        border: 2px solid #000;
    }
}

/* Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* Micro-interactions */
.micro-bounce:hover {
    filter: brightness(1.05);
    transition: filter 0.2s ease;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active {
    background-color: #12c093;
    box-shadow: 0 0 0 2px rgba(18, 192, 147, 0.2);
}

.status-pending {
    background-color: #ffc107;
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.2);
}

.status-inactive {
    background-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

/* Tooltip Enhancements */
.modern-tooltip {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Loading States */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

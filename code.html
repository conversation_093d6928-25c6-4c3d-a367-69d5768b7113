<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fixed Chat Widget</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 50%, #9333EA 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Confetti animation */
        .confetti {
            position: absolute;
            width: 8px;
            height: 15px;
            background: #FFD700;
            animation: confetti-fall linear infinite;
            transform-origin: center;
        }

        .confetti:nth-child(2n) {
            background: #FFA500;
            width: 6px;
            height: 12px;
        }

        .confetti:nth-child(3n) {
            background: #FFFF00;
            width: 10px;
            height: 8px;
            border-radius: 50%;
        }

        .confetti:nth-child(4n) {
            background: #FF6B35;
            width: 4px;
            height: 20px;
        }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* Chat widget container */
        .chat-widget-container {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* Messenger icon (separate) */
        .messenger-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #8B5CF6, #A855F7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
            border: 3px solid rgba(255, 255, 255, 0.9);
        }

        .messenger-icon:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.6);
        }

        .messenger-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .messenger-icon:hover::before {
            left: 100%;
        }

        .messenger-icon svg {
            width: 24px;
            height: 24px;
            fill: white;
            z-index: 2;
        }

        /* Chat text bubble (separate) */
        .chat-text-bubble {
            background: white;
            padding: 12px 18px;
            border-radius: 25px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.8);
            min-width: 180px;
        }

        .chat-text-bubble:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            border-color: rgba(139, 92, 246, 0.3);
        }

        .chat-text {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
            min-height: 17px;
            white-space: nowrap;
            overflow: hidden;
        }

        .typing-cursor {
            display: inline-block;
            width: 2px;
            height: 18px;
            background: #8B5CF6;
            margin-left: 2px;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Pulse effect for container */
        .pulse .messenger-icon {
            animation: icon-pulse 2s infinite;
        }

        .pulse .chat-text-bubble {
            animation: bubble-pulse 2s infinite;
        }

        @keyframes icon-pulse {
            0% {
                box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 6px 30px rgba(139, 92, 246, 0.7);
                transform: scale(1.05);
            }
            100% {
                box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
                transform: scale(1);
            }
        }

        @keyframes bubble-pulse {
            0% {
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 6px 25px rgba(139, 92, 246, 0.3);
                transform: scale(1.02);
            }
            100% {
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
                transform: scale(1);
            }
        }

        /* Sample content area */
        .content-area {
            padding: 100px 50px;
            color: white;
            text-align: center;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .content-card h2 {
            font-size: 28px;
            margin-bottom: 15px;
            color: #FFD700;
        }

        .content-card p {
            font-size: 16px;
            line-height: 1.6;
            opacity: 0.9;
        }

        /* Online indicator */
        .online-indicator {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #10B981;
            border: 2px solid white;
            border-radius: 50%;
            animation: online-pulse 2s infinite;
        }

        @keyframes online-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
<!-- Sample page content -->
<div class="content-area">
    <div class="content-card">
        <h2>KHOÁ HỌC LUYỆN THI</h2>
        <p>Khóa học chất lượng cao với đội ngũ giảng viên kinh nghiệm. Đăng ký ngay để nhận ưu đãi đặc biệt!</p>
    </div>

    <div class="content-card">
        <h2>HỖ TRỢ 24/7</h2>
        <p>Chúng tôi luôn sẵn sàng hỗ trợ bạn mọi lúc, mọi nơi. Hãy chat với chúng tôi để được tư vấn miễn phí.</p>
    </div>
</div>

<!-- Fixed chat widget -->
<div class="chat-widget-container" id="chatWidgetContainer">
    <div class="messenger-icon" id="messengerIcon">
        <div class="online-indicator"></div>
        <svg viewBox="0 0 24 24">
            <path d="M12,2C6.48,2 2,6.48 2,12C2,13.93 2.61,15.72 3.64,17.19L2.05,21.95L6.81,20.36C8.28,21.39 10.07,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2M12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20C10.37,20 8.87,19.5 7.62,18.64L6.93,18.16L5.65,18.5L5.99,17.22L5.51,16.53C4.65,15.28 4.15,13.78 4.15,12.15C4.15,7.74 7.74,4.15 12.15,4.15L12,4M8.5,9.5A1,1 0 0,0 7.5,10.5V13.5A1,1 0 0,0 8.5,14.5H9.5A1,1 0 0,0 10.5,13.5V10.5A1,1 0 0,0 9.5,9.5H8.5M14.5,9.5A1,1 0 0,0 13.5,10.5V13.5A1,1 0 0,0 14.5,14.5H15.5A1,1 0 0,0 16.5,13.5V10.5A1,1 0 0,0 15.5,9.5H14.5Z"/>
        </svg>
    </div>
    <div class="chat-text-bubble" id="chatTextBubble">
        <div class="chat-text" id="chatText">
            Nhắn tin cho Hùng ngay!
        </div>
    </div>
</div>

<script>
    // Create confetti
    function createConfetti() {
        for(let i = 0; i < 30; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.left = Math.random() * 100 + '%';
            confetti.style.animationDelay = Math.random() * 3 + 's';
            confetti.style.animationDuration = (Math.random() * 4 + 3) + 's';
            document.body.appendChild(confetti);

            // Remove confetti after animation
            setTimeout(() => {
                confetti.remove();
            }, 7000);
        }
    }

    // Continuously create confetti
    createConfetti();
    setInterval(createConfetti, 2000);

    const chatWidgetContainer = document.getElementById('chatWidgetContainer');
    const messengerIcon = document.getElementById('messengerIcon');
    const chatTextBubble = document.getElementById('chatTextBubble');
    const chatText = document.getElementById('chatText');

    const messages = [
        "Nhắn tin cho Hùng ngay!",
        "Liên hệ hỗ trợ",
        "Chat với chúng tôi",
        "Tư vấn miễn phí",
        "Hỗ trợ 24/7",
        "Đăng ký khóa học",
        "Nhận ưu đãi đặc biệt"
    ];

    let currentMessageIndex = 0;
    let isTyping = false;

    function eraseText(callback) {
        const currentText = chatText.textContent;
        let i = currentText.length;

        const cursor = document.createElement('span');
        cursor.className = 'typing-cursor';
        chatText.appendChild(cursor);

        const eraseInterval = setInterval(() => {
            if (i > 0) {
                chatText.textContent = currentText.substring(0, i - 1);
                chatText.appendChild(cursor);
                i--;
            } else {
                clearInterval(eraseInterval);
                chatText.innerHTML = '';
                if (callback) callback();
            }
        }, 40);
    }

    function typeWriter(text, callback) {
        let i = 0;
        const cursor = document.createElement('span');
        cursor.className = 'typing-cursor';
        chatText.appendChild(cursor);

        const typeInterval = setInterval(() => {
            if (i < text.length) {
                chatText.insertBefore(document.createTextNode(text.charAt(i)), cursor);
                i++;
            } else {
                clearInterval(typeInterval);
                setTimeout(() => {
                    if (cursor.parentNode) {
                        cursor.remove();
                    }
                    isTyping = false;
                    if (callback) callback();
                }, 1500);
            }
        }, 60);
    }

    function changeMessage() {
        if (isTyping) return;
        isTyping = true;

        chatWidgetContainer.classList.add('pulse');

        eraseText(() => {
            currentMessageIndex = (currentMessageIndex + 1) % messages.length;
            typeWriter(messages[currentMessageIndex], () => {
                chatWidgetContainer.classList.remove('pulse');
            });
        });
    }

    // Auto change message every 5 seconds (start after initial typing)
    setTimeout(() => {
        setInterval(changeMessage, 5000);
    }, 3000); // Wait 3 seconds after initial typing

    // Click handlers for both icon and text bubble
    messengerIcon.addEventListener('click', changeMessage);
    chatTextBubble.addEventListener('click', changeMessage);

    // Initial typing animation
    setTimeout(() => {
        isTyping = true;
        typeWriter(messages[0]);
    }, 1500);

    // Add click ripple effect for both elements
    function addRippleEffect(element) {
        element.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(139, 92, 246, 0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;

            this.style.position = 'relative';
            this.style.overflow = 'hidden';
            this.appendChild(ripple);

            setTimeout(() => ripple.remove(), 600);
        });
    }

    addRippleEffect(messengerIcon);
    addRippleEffect(chatTextBubble);

    // Add ripple animation
    const style = document.createElement('style');
    style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
    document.head.appendChild(style);
</script>
</body>
</html>

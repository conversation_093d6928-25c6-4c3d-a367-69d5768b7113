<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();
        $this->call([
//            TryLearningSettingSeeder::class,
//            UpdateSepayPaymentGatewaySeeder::class,
            AddonSeeder::class,
            BuilderPagesSeeder::class
//            LiveClassAddonSeeder::class,
//            LanguagePhrasesSeeder::class,
//            DeviceSettingsSeeder::class
            // ... other seeders
        ]);
        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
    }
}

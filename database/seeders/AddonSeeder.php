<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Addon;
use Illuminate\Support\Facades\DB;

class AddonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define addons data
        $addons = [
            [
                'id' => 1,
                'name' => 'Affiliate',
                'author' => 'TOPID',
                'name_addons' => 'my.affiliate',
                'description' => 'Tính năng Affiliate cho phép người dùng chia sẻ khóa học và nhận hoa hồng khi có người đăng ký qua liên kết của họ.',
                'permission_keys' => json_encode(['admin.affiliate', 'admin.affiliate.withdraws', 'admin.affiliate.settings', 'admin.affiliate.status', 'admin.affiliate.withdraw.status', 'admin.affiliate.setting.store']),
                'price' => 0, // Miễn phí
                'required_package' => 'PRO',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 2,
                'name' => 'Live Class',
                'author' => 'Zoom',
                'name_addons' => 'my.live_class',
                'description' => 'Tính năng Live Class tích hợp Zoom cho phép giảng viên tổ chức các buổi học trực tuyến trực tiếp ngay trên nền tảng khóa học. Học viên có thể tham gia các buổi học live mà không cần cài đặt phần mềm Zoom riêng biệt.',
                'price' => 0,
                'required_package' => 'PREMIUM',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 3,
                'name' => 'Học Tuần Tự',
                'author' => 'TOPID',
                'name_addons' => 'my.sequential_learning',
                'description' => 'Cài đặt khóa các bài học tiếp theo cho đến khi học viên hoàn thành bài học hiện tại, đảm bảo việc học theo đúng lộ trình.',
                'price' => 2000000,
                'required_package' => 'PRO',

                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 4,
                'name' => 'Cấp chứng chỉ online',
                'author' => 'TOPID',
                'name_addons' => 'my.certificate',
                'description' => 'Cấp chứng chỉ tự động sau khi học viên học xong lộ trình',
                'price' => 2000000,
                'required_package' => 'PRO',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 5,
                'name' => 'Video Heatmap & Progress',
                'author' => 'TOPID',
                'name_addons' => 'my.video_heatmap',
                'description' => 'Tính năng theo dõi tiến độ xem video và hiển thị heatmap tương tự như YouTube. Giúp giảng viên nắm được các phần nội dung được xem nhiều nhất, điểm dừng/tua lại thường xuyên, và phân tích hành vi người học khi xem video bài giảng.',
                'price' => 2000000,
                'required_package' => 'PRO',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 6,
                'name' => 'Video Advertisement',
                'author' => 'TOPID',
                'name_addons' => 'my.video_advertisement',
                'description' => 'Tính năng quảng cáo trong video cho phép giảng viên cấu hình nhiều popup quảng cáo xuất hiện tại các thời điểm cụ thể trong video bài giảng. Popup có thể được tùy chỉnh để hiển thị hình ảnh, liên kết và có thể định cấu hình nhiều popup khác nhau cho cùng một video, mỗi popup xuất hiện tại một thời điểm khác nhau.',
                'price' => 1000000, // 1 triệu đồng
                'required_package' => 'PREMIUM',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 7,
                'name' => 'Thông Báo Ảo',
                'author' => 'TOPID',
                'name_addons' => 'my.fake_notifications',
                'description' => 'Tính năng thông báo ảo giúp tạo ra cảm giác trang web có nhiều người hoạt động bằng cách tự động hiển thị các thông báo về đăng ký, mua khóa học, hoàn thành bài học... Những thông báo này có vẻ như đến từ người dùng thật nhưng được tạo tự động để tăng tính tương tác và tâm lý đám đông.',
                'price' => 1000000, // 1 triệu đồng
                'required_package' => 'PRO',
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'id' => 8,
                'name' => 'Nhúng Form Đăng Ký',
                'author' => 'TOPID',
                'permission_keys' => json_encode(['admin.embed.settings']),
                'name_addons' => 'my.embed_registration',
                'description' => 'Công cụ nhúng form đăng ký khóa học vào các trang web, landing page như WordPress, Ladipage và các nền tảng khác. Tính năng này giúp tăng khả năng thu thập thông tin đăng ký từ nhiều kênh, hỗ trợ tùy chỉnh nội dung hiển thị và chức năng chuyển hướng linh hoạt sau khi đăng ký thành công.',
                'price' => 1000000, // 1 triệu đồng
                'required_package' => 'PRO',
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        // Upsert each addon based on name_addons
        foreach ($addons as $addon) {
            Addon::updateOrCreate(
                ['name_addons' => $addon['name_addons']], // Condition to check if exists
                $addon // Data to update or create
            );
        }
    }
}

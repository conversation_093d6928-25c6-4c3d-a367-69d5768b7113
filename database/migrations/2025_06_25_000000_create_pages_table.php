<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // Tiêu đề trang
            $table->string('slug')->unique(); // Đường dẫn URL thân thiện
            $table->longText('content'); // Nội dung HTML
            $table->text('excerpt')->nullable(); // Tóm tắt ngắn
            $table->string('meta_title')->nullable(); // SEO title
            $table->text('meta_description')->nullable(); // SEO description
            $table->string('meta_keywords')->nullable(); // SEO keywords
            $table->enum('status', ['published', 'draft', 'private'])->default('draft'); // Trạng thái
            $table->string('featured_image')->nullable(); // Ảnh đại diện
            $table->integer('sort_order')->default(0); // Thứ tự sắp xếp
            $table->boolean('show_in_menu')->default(false); // Hiển thị trong menu
            $table->string('template')->default('default'); // Template sử dụng
            $table->json('custom_fields')->nullable(); // Các trường tùy chỉnh
            $table->unsignedBigInteger('created_by'); // Người tạo
            $table->unsignedBigInteger('updated_by')->nullable(); // Người cập nhật
            $table->timestamps();

            // Indexes
            $table->index('slug');
            $table->index('status');
            $table->index('sort_order');
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pages');
    }
};

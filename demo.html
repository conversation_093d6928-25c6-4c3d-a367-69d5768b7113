<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TinyMCE Editor - Copy từ Word</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.2/tinymce.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .editor-container {
            margin-bottom: 20px;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976D2;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .buttons {
            text-align: center;
            margin-top: 20px;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #1976D2;
        }
        .preview {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .preview h4 {
            margin-top: 0;
            color: #333;
        }
        #content-preview {
            min-height: 100px;
            background: white;
            padding: 10px;
            border: 1px solid #eee;
            border-radius: 3px;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>📝 TinyMCE Editor - Hỗ trợ Copy từ Word</h1>

    <div class="instructions">
        <h3>🔧 Hướng dẫn sử dụng:</h3>
        <ul>
            <li><strong>Copy từ Word:</strong> Chọn nội dung trong Word → Ctrl+C → Paste vào editor</li>
            <li><strong>Giữ định dạng:</strong> Editor sẽ tự động giữ nguyên font, màu sắc, table, list...</li>
            <li><strong>Làm sạch HTML:</strong> Sử dụng nút "Clean HTML" để loại bỏ code thừa</li>
            <li><strong>Xem kết quả:</strong> Nhấn "Xem HTML" để kiểm tra code được tạo</li>
        </ul>
    </div>

    <div class="editor-container">
            <textarea id="mytextarea" placeholder="Paste nội dung từ Word vào đây...">
                <h2>Chào mừng đến với TinyMCE Editor!</h2>
                <p>Bạn có thể copy nội dung từ <strong>Microsoft Word</strong> và paste vào đây để giữ nguyên định dạng.</p>
                <ul>
                    <li>Định dạng <em>italic</em> và <strong>bold</strong></li>
                    <li>Màu sắc và font chữ</li>
                    <li>Bảng biểu và danh sách</li>
                    <li>Hình ảnh (nếu có)</li>
                </ul>
            </textarea>
    </div>

    <div class="buttons">
        <button onclick="getContent()">📄 Xem HTML</button>
        <button onclick="setContent()">📝 Thêm nội dung mẫu</button>
        <button onclick="clearContent()">🗑️ Xóa nội dung</button>
        <button onclick="cleanHtml()">🧹 Clean HTML</button>
    </div>

    <div class="preview">
        <h4>🔍 HTML Output:</h4>
        <div id="content-preview"></div>
    </div>
</div>

<script>
    // Khởi tạo TinyMCE với cấu hình tối ưu cho Word
    tinymce.init({
        selector: '#mytextarea',
        height: 500,

        // Plugins hỗ trợ copy từ Word
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste',
            'importcss', 'autosave', 'save', 'textcolor', 'colorpicker'
        ],

        // Toolbar với đầy đủ chức năng
        toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | ' +
            'forecolor backcolor | align lineheight | ' +
            'bullist numlist outdent indent | removeformat | ' +
            'table tabledelete | tableprops tablerowprops tablecellprops | ' +
            'tableinsertrowbefore tableinsertrowafter tabledeleterow | ' +
            'tableinsertcolbefore tableinsertcolafter tabledeletecol | ' +
            'link image media | code preview fullscreen',

        // Cấu hình Paste - quan trọng nhất
        paste_data_images: true,
        paste_word_valid_elements: "b,strong,i,em,h1,h2,h3,h4,h5,h6,p,ol,ul,li,a[href],span,color,font-size,font-weight,text-decoration,text-align",
        paste_retain_style_properties: "color font-size font-weight text-decoration text-align",
        paste_remove_styles_if_webkit: false,
        paste_merge_formats: true,
        paste_convert_word_fake_lists: true,
        paste_webkit_styles: "color font-size font-weight text-decoration text-align",

        // Giữ nguyên định dạng từ Word
        paste_preprocess: function(plugin, args) {
            console.log('Paste preprocess:', args.content);
            // Giữ lại các style cần thiết
            args.content = args.content.replace(/mso-[^;]+;/gi, '');
            args.content = args.content.replace(/margin: 0[^;]*;/gi, '');
            args.content = args.content.replace(/font-family: [^;]*;/gi, '');
        },

        paste_postprocess: function(plugin, args) {
            console.log('Paste postprocess:', args.node);
            // Xử lý sau khi paste
        },

        // Font và style
        font_family_formats: 'Arial=arial,helvetica,sans-serif; Times New Roman=times new roman,times,serif; Courier New=courier new,courier,monospace; Verdana=verdana,geneva,sans-serif; Georgia=georgia,serif; Palatino=palatino linotype,book antiqua,palatino,serif; Tahoma=tahoma,arial,helvetica,sans-serif; Trebuchet MS=trebuchet ms,geneva,sans-serif; Lucida Sans=lucida sans unicode,lucida grande,sans-serif; Impact=impact,chicago,sans-serif; Comic Sans MS=comic sans ms,cursive',

        font_size_formats: '8pt 9pt 10pt 11pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 26pt 28pt 30pt 32pt 34pt 36pt 38pt 40pt 42pt 44pt 46pt 48pt 50pt 52pt 54pt 56pt 58pt 60pt 62pt 64pt 66pt 68pt 70pt 72pt 74pt 76pt 78pt 80pt',

        // Cấu hình bảng
        table_default_attributes: {
            border: '1'
        },
        table_default_styles: {
            'border-collapse': 'collapse',
            'width': '100%'
        },

        // Cấu hình khác
        menubar: 'file edit view insert format tools table help',
        contextmenu: 'link image table',
        branding: false,
        promotion: false,

        // Xử lý khi editor sẵn sàng
        setup: function(editor) {
            editor.on('init', function() {
                console.log('TinyMCE đã sẵn sàng!');
            });

            editor.on('paste', function(e) {
                console.log('Đã paste nội dung');
                setTimeout(function() {
                    getContent();
                }, 100);
            });
        }
    });

    // Hàm lấy nội dung HTML
    function getContent() {
        const content = tinymce.get('mytextarea').getContent();
        document.getElementById('content-preview').innerHTML = '<pre><code>' +
            content.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</code></pre>';
    }

    // Hàm thêm nội dung mẫu
    function setContent() {
        const sampleContent = `
                <h2 style="color: #2196F3;">Tiêu đề mẫu</h2>
                <p>Đây là một đoạn văn bản mẫu với <strong>định dạng đậm</strong> và <em>định dạng nghiêng</em>.</p>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <thead>
                        <tr style="background-color: #f0f0f0;">
                            <th>Cột 1</th>
                            <th>Cột 2</th>
                            <th>Cột 3</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Dữ liệu 1</td>
                            <td>Dữ liệu 2</td>
                            <td>Dữ liệu 3</td>
                        </tr>
                    </tbody>
                </table>
                <ul>
                    <li>Mục danh sách 1</li>
                    <li>Mục danh sách 2</li>
                    <li>Mục danh sách 3</li>
                </ul>
            `;
        tinymce.get('mytextarea').setContent(sampleContent);
    }

    // Hàm xóa nội dung
    function clearContent() {
        tinymce.get('mytextarea').setContent('');
        document.getElementById('content-preview').innerHTML = '';
    }

    // Hàm làm sạch HTML
    function cleanHtml() {
        let content = tinymce.get('mytextarea').getContent();

        // Loại bỏ các style không cần thiết
        content = content.replace(/mso-[^;]*;?/gi, '');
        content = content.replace(/margin:\s*[^;]*;?/gi, '');
        content = content.replace(/font-family:\s*[^;]*;?/gi, '');
        content = content.replace(/style="\s*"/gi, '');
        content = content.replace(/class="[^"]*"/gi, '');

        tinymce.get('mytextarea').setContent(content);
        alert('HTML đã được làm sạch!');
    }
</script>
</body>
</html>

@extends('layouts.default')
@push('title', get_phrase('My profile'))
@push('meta')@endpush
@push('css')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    /* Custom styling for Select2 */
    .select2-container--default {
        width: 100% !important;
    }

    .select2-container--default .select2-selection--single {
        height: 45px;
        border-radius: 5px;
        border: 1px solid #ced4da;
        padding: 0;
        font-size: 1rem;
        line-height: 1.5;
        background-color: #fff;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #495057;
        line-height: 45px;
        padding-left: 15px;
        font-size: 15px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 43px;
        right: 8px;
    }

    .select2-dropdown {
        border: 1px solid #ced4da;
        border-radius: 5px;
    }

    .select2-search--dropdown .select2-search__field {
        padding: 8px;
        border: 1px solid #ced4da;
        border-radius: 3px;
        font-size: 15px;
    }

    .select2-container--default .select2-results__option--highlighted[aria-selected] {
        background-color: #6c5ce7;
    }

    .select2-container--default .select2-results__option {
        padding: 10px 15px;
        font-size: 15px;
    }

    .select2-results__option {
        padding: 10px 15px;
        font-size: 15px;
    }
</style>
@endpush
@section('content')
    <!------------ My profile area start  ------------>
    <section class="course-content">
        <div class="profile-banner-area"></div>
        <div class="container profile-banner-area-container">
            <div class="row">
                @include('frontend.default.student.left_sidebar')
                <div class="col-lg-9">
                    <h4 class="g-title mb-5">Thông tin cá nhân</h4>
                    <div class="my-panel message-panel edit_profile">
                        <form action="{{ route('update.profile', $user_details->id) }}" method="POST">@csrf
                            <div class="row">
                                <div class="col-lg-12 mb-20">
                                    <div class="form-group">
                                        <label for="name" class="form-label">Họ và tên</label>
                                        <input type="text" class="form-control" name="name"
                                            value="{{ $user_details->name }}" id="name">
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-20">
                                    <div class="form-group">
                                        <label for="email" class="form-label">Địa chỉ email</label>
                                        <input type="email" class="form-control" name="email"
                                            value="{{ $user_details->email }}" id="email">
                                    </div>
                                </div>
                                <div class="col-lg-6 mb-20">
                                    <div class="form-group">
                                        <label for="phone" class="form-label">Số điện thoại</label>
                                        <input type="tel" class="form-control" name="phone"
                                            value="{{ $user_details->phone }}" id="phone">
                                    </div>
                                </div>

                                <div class="col-lg-6 mb-20">
                                    <div class="form-group">
                                        <label for="old_password" class="form-label">Mật khẩu cũ</label>
                                        <input type="password" class="form-control" name="old_password" id="old_password">
                                        <small class="text-muted">Để trống nếu không muốn đổi mật khẩu</small>
                                    </div>
                                </div>

                                <div class="col-lg-6 mb-20">
                                    <div class="form-group">
                                        <label for="new_password" class="form-label">Mật khẩu mới</label>
                                        <input type="password" class="form-control" name="new_password" id="new_password">
                                        <small class="text-muted">Để trống nếu không muốn đổi mật khẩu</small>
                                    </div>
                                </div>

                            </div>
                            <div class="row" id="bank-info">
                                <div class="col-lg-12 mb-20">
                                    <h4 class="capitalize g-title">Thông Tin Ngân Hàng</h4>
                                </div>
                                <div class="col-lg-12 mb-20">
                                    <div class="form-group">
                                        <label for="bank_name" class="form-label">Tên ngân hàng</label>
                                        <select class="form-control" name="bank_name" id="bank_select">
                                            <option value="">Chọn Ngân hàng</option>
                                        </select>
                                        <input type="hidden" name="bank_code" id="bank_code" value="">
                                    </div>
                                </div>
                                <div class="col-lg-12 mb-20">
                                    <div class="form-group">
                                        <label for="bank_account_number" class="form-label">Số tài khoản ngân hàng</label>
                                        <input type="text" class="form-control" name="bank_account_number"
                                            value="{{ $user_details->bank_account_number }}" id="bank_account_number">
                                    </div>
                                </div>
                                <div class="col-lg-12 mb-20">
                                    <div class="form-group">
                                        <label for="bank_account_name" class="form-label">Tên Tài Khoản Ngân Hàng</label>
                                        <input type="text" class="form-control" tname="bank_account_name"
                                            value="{{ $user_details->bank_account_name }}" id="bank_account_name">
                                    </div>
                                </div>

                                <button class="eBtn btn gradient mt-10">Lưu thay đổi</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!------------ My profile area end  ------------>
@endsection
@push('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        // Bank data from PHP helper
        var bankData = @json(getBankData());

        // Initialize Select2
        $('#bank_select').select2({
            placeholder: "Chọn Ngân hàng",
            allowClear: true,
            data: bankData.map(function(bank) {
                var fullBankInfo = bank.code + ' - ' + bank.name;
                return {
                    id: fullBankInfo,
                    text: fullBankInfo,
                    code: bank.code,
                    name: bank.name
                };
            }),
            templateResult: formatBank,
            templateSelection: formatBankSelection
        });

        // Format bank display in dropdown
        function formatBank(bank) {
            if (!bank.id) {
                return bank.text;
            }

            // Đảm bảo bank.code và bank.name tồn tại
            var code = bank.code || '';
            var name = bank.name || '';

            // Nếu không có code và name, thử tách từ text
            if (!code && !name && bank.text) {
                var parts = bank.text.split(' - ');
                if (parts.length >= 2) {
                    code = parts[0];
                    name = parts.slice(1).join(' - ');
                }
            }

            var $bank = $(
                '<div class="bank-item">' +
                    '<strong class="bank-code">' + code + '</strong> - ' +
                    '<span class="bank-name">' + name + '</span>' +
                '</div>'
            );

            return $bank;
        }

        // Format bank selection display
        function formatBankSelection(bank) {
            if (!bank.id) {
                return bank.text;
            }

            // Đảm bảo bank.code và bank.name tồn tại
            var code = bank.code || '';
            var name = bank.name || '';

            // Nếu không có code và name, thử tách từ text
            if (!code && !name && bank.text) {
                var parts = bank.text.split(' - ');
                if (parts.length >= 2) {
                    code = parts[0];
                    name = parts.slice(1).join(' - ');
                }
            }

            return code + ' - ' + name;
        }

        // Set initial value if there is one
        var currentBankName = "{{ $user_details->bank_name }}";
        var currentBankCode = "{{ $user_details->bank_code ?? '' }}";

        if (currentBankName) {
            var foundBank;

            // First try to find by code if it exists
            if (currentBankCode) {
                foundBank = bankData.find(function(bank) {
                    return bank.code === currentBankCode;
                });
            }

            // If not found by code, try to find by name
            if (!foundBank) {
                foundBank = bankData.find(function(bank) {
                    return bank.name === currentBankName ||
                           bank.code === currentBankName ||
                           currentBankName.includes(bank.code);
                });
            }

            if (foundBank) {
                var fullBankInfo = foundBank.code + ' - ' + foundBank.name;
                var option = new Option(fullBankInfo, fullBankInfo, true, true);
                $('#bank_select').append(option).trigger('change');
                $('#bank_code').val(foundBank.code);
            } else {
                // If the current value doesn't match any bank in the list, add it as a custom option
                var option = new Option(currentBankName, currentBankName, true, true);
                $('#bank_select').append(option).trigger('change');
            }
        }

        // When a bank is selected, update the hidden field
        $('#bank_select').on('select2:select', function(e) {
            var selectedBank = e.params.data;
            $('#bank_code').val(selectedBank.code || '');
        });
    });
</script>
@endpush

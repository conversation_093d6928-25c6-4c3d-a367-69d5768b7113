@extends('layouts.default')
@push('title', get_phrase('My Affiliate'))
@push('meta')@endpush
@push('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css"/>
    <style>
        .tab-button {
            background: transparent;
            border: none;
            padding: 10px 20px;
            font-weight: 500;
            color: #6E798A;
            border-bottom: 2px solid transparent;
        }

        .tab-button.active {
            color: #5d2fdf;
            border-bottom: 2px solid #5d2fdf;
        }

        .affiliate-card {
            border-radius: 15px;
            background-color: #fff;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .copy-link-aff-coupon {
            cursor: pointer;
        }

        .affiliate-card:hover {
            box-shadow: 0 8px 25px rgba(93, 47, 223, 0.1);
            transform: translateY(-5px);
        }

        .balance-card {
            position: relative;
            overflow: hidden;
        }

        .balance-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #5d2fdf, #a291e5);
            border-radius: 5px 5px 0 0;
        }

        .balance-title {
            color: #6E798A;
            font-size: 16px;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .balance-amount {
            color: #2d3748;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }

        .balance-amount .currency-icon {
            margin-right: 5px;
            color: #5d2fdf;
            font-size: 28px;
        }

        .balance-trend {
            font-size: 14px;
            color: #38b2ac;
            margin-top: 5px;
            display: flex;
            align-items: center;
        }

        .balance-trend i {
            margin-right: 5px;
        }

        .tab-content {
            margin-top: 20px;
        }

        .request-btn {
            background-color: #5d2fdf;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(93, 47, 223, 0.2);
        }

        .request-btn:hover {
            background-color: #4a25b3;
            box-shadow: 0 6px 15px rgba(93, 47, 223, 0.3);
            transform: translateY(-2px);
        }

        .request-btn:disabled {
            background-color: #a79ad0;
            cursor: not-allowed;
            box-shadow: none;
        }

        .export-btn {
            background-color: #6E798A;
            color: white;
            border: none;
            border-radius: 5px;
            padding: 5px 15px;
            font-weight: 500;
            font-size: 14px;
        }

        .export-btn.pdf {
            background-color: #5d2fdf;
        }

        /* Enhanced Table Styling */
        .table-container {
            margin-top: 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
            background-color: #fff;
        }

        .table {
            width: 100%;
            margin-bottom: 0;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 14px;
        }

        .table thead th {
            background-color: #f8f9ff;
            color: #4a4a4a;
            font-weight: 600;
            padding: 15px;
            border-bottom: 2px solid #eaecf0;
            text-transform: uppercase;
            font-size: 12px;
            letter-spacing: 0.5px;
            position: relative;
        }

        .table thead th:after {
            content: '';
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #5d2fdf 0%, rgba(93, 47, 223, 0.2) 100%);
            transform: scaleX(0);
            transform-origin: left;
            transition: transform 0.3s ease;
        }

        .table thead th:hover:after {
            transform: scaleX(1);
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: #f8f9ff;
        }

        .table tbody td {
            padding: 15px;
            vertical-align: middle;
            border-bottom: 1px solid #eaecf0;
            color: #4a4a4a;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Badge styling */
        .badge {
            padding: 6px 12px;
            font-weight: 500;
            font-size: 12px;
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .bg-success {
            background-color: #e6f7ee !important;
            color: #00a173 !important;
        }

        .bg-warning {
            background-color: #fff8e9 !important;
            color: #e69500 !important;
        }

        .bg-danger {
            background-color: #fee8ec !important;
            color: #dc3545 !important;
        }

        .table .text-muted {
            color: #8c98a4 !important;
            font-size: 12px;
        }

        /* Pagination styling */
        .pagination {
            display: flex;
            padding-left: 0;
            list-style: none;
            border-radius: 0.25rem;
            justify-content: center;
            margin-top: 20px;
        }

        .pagination .page-item .page-link {
            position: relative;
            display: block;
            padding: 0.5rem 0.75rem;
            margin-left: -1px;
            line-height: 1.25;
            color: #5d2fdf;
            background-color: #fff;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease;
        }

        .pagination .page-item.active .page-link {
            z-index: 3;
            color: #fff;
            background-color: #5d2fdf;
            border-color: #5d2fdf;
        }

        .pagination .page-item .page-link:hover {
            z-index: 2;
            color: #4a25b3;
            text-decoration: none;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }

        .pagination .page-item:first-child .page-link {
            margin-left: 0;
            border-top-left-radius: 0.25rem;
            border-bottom-left-radius: 0.25rem;
        }

        .pagination .page-item:last-child .page-link {
            border-top-right-radius: 0.25rem;
            border-bottom-right-radius: 0.25rem;
        }

        /* Empty state styling */
        .table tbody tr td.text-center {
            padding: 30px 15px;
            color: #8c98a4;
            font-style: italic;
        }

        .commission-tag {
            color: #6E798A;
            font-size: 14px;
        }

        .currency-input {
            position: relative;
        }

        .currency-input input {
            padding-left: 25px;
            text-align: right;
            font-size: 18px;
            font-weight: 500;
        }

        .currency-input:before {
            content: 'đ';
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            font-size: 18px;
            font-weight: 500;
            color: #495057;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }

        .withdrawal-action-btn {
            font-size: 12px;
            padding: 6px 12px;
            margin-left: 8px;
            border-radius: 20px;
            transition: all 0.2s ease;
        }

        .withdrawal-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

        .gap-2 {
            gap: 0.5rem !important;
        }

        #copyButton {
            padding: 0px 10px;
            font-size: 20px;
        }

        /* New Styles for Metrics */
        .metrics-card {
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
            text-align: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            background: #fff;
        }

        .metrics-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(93, 47, 223, 0.1);
        }

        .metrics-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            border-radius: 50%;
            font-size: 24px;
            color: white;
        }

        .metrics-clicks .metrics-icon {
            background: linear-gradient(45deg, #4a25b3, #5d2fdf);
        }

        .metrics-purchases .metrics-icon {
            background: linear-gradient(45deg, #2d8cf0, #0072ff);
        }

        .metrics-conversion .metrics-icon {
            background: linear-gradient(45deg, #00c48d, #00a173);
        }

        .metrics-value {
            font-size: 28px;
            font-weight: 700;
            margin: 5px 0;
            color: #2d3748;
        }

        .metrics-label {
            color: #6E798A;
            font-size: 14px;
            font-weight: 500;
        }

        .metrics-trend {
            font-size: 12px;
            margin-top: 5px;
        }

        .metrics-trend.up {
            color: #00c48d;
        }

        .metrics-trend.down {
            color: #f5365c;
        }

        /* Exclusive Discount Alert */
        .exclusive-alert {
            border-radius: 15px;
            border-left: 5px solid #f5b014;
            background-color: #fff8eb;
            padding: 20px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }

        .exclusive-alert::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(245, 176, 20, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
            z-index: 1;
        }

        .exclusive-alert-title {
            font-size: 18px;
            font-weight: 600;
            color: #e69500;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .exclusive-alert-title i {
            margin-right: 10px;
            font-size: 24px;
        }

        .exclusive-alert-content {
            color: #755500;
            font-size: 14px;
            line-height: 1.5;
        }

        .exclusive-alert-footer {
            margin-top: 15px;
            font-size: 13px;
            color: #997300;
            font-style: italic;
        }

        .bank-update-btn {
            background-color: #5d2fdf;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 15px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(93, 47, 223, 0.2);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
        }

        .bank-update-btn:hover {
            background-color: #4a25b3;
            box-shadow: 0 6px 15px rgba(93, 47, 223, 0.3);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }

        .bank-update-btn i {
            font-size: 14px;
        }

        .bank-update-btn.btn-lg {
            padding: 12px 24px;
            font-size: 16px;
        }

        .bank-update-btn.btn-lg i {
            font-size: 18px;
        }

        .modal-footer .btn-secondary {
            background-color: #6E798A;
            color: white;
            border: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(110, 121, 138, 0.2);
        }

        .modal-footer .btn-secondary:hover {
            background-color: #5a6474;
            box-shadow: 0 6px 15px rgba(110, 121, 138, 0.3);
            transform: translateY(-2px);
        }

        .modal-footer .btn-primary {
            background-color: #5d2fdf;
            color: white;
            border: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(93, 47, 223, 0.2);
        }

        .modal-footer .btn-primary:hover {
            background-color: #4a25b3;
            box-shadow: 0 6px 15px rgba(93, 47, 223, 0.3);
        }

        .affiliate-info-box {
            background-color: #f8f9ff;
            border-left: 3px solid #5d2fdf;
            padding: 12px 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-size: 14px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .affiliate-info-box .info-icon {
            color: #5d2fdf;
            font-size: 18px;
            margin-top: 3px;
        }

        .affiliate-info-box .info-content {
            flex: 1;
        }

        .affiliate-info-box .info-title {
            font-weight: 600;
            color: #5d2fdf;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .affiliate-info-box .info-text {
            color: #6E798A;
            margin-bottom: 0;
            line-height: 1.4;
        }

        /* Table row transition effects */
        .table tbody tr {
            transform: translateY(0);
            opacity: 1;
            transition: transform 0.3s ease, opacity 0.3s ease, background-color 0.2s ease;
        }

        .table tbody tr:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
            z-index: 1;
            position: relative;
        }

        /* Improved responsive table */
        @media (max-width: 767px) {
            .table-responsive {
                border-radius: 12px;
                overflow: hidden;
            }

            .table thead th,
            .table tbody td {
                white-space: nowrap;
                padding: 12px 10px;
            }
        }

        /* Subtle row stripes for better readability */
        .table tbody tr:nth-child(even) {
            background-color: #fbfbfd;
        }

        /* Affiliate Section Styling */
        .affiliate-section {
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .section-header {
            flex-shrink: 0;
        }

        .affiliate-course-container,
        .affiliate-link-container {
            flex: 1;
            display: flex;
            align-items: flex-end;
        }

        .affiliate-course-container .form-select,
        .affiliate-link-container .input-group {
            width: 100%;
        }

        .form-select {
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            background-color: #fff;
        }

        .form-select:focus {
            border-color: #5d2fdf;
            box-shadow: 0 0 0 0.2rem rgba(93, 47, 223, 0.25);
            outline: none;
        }

        .input-group .form-control {
            border-radius: 8px 0 0 8px;
            border: 1px solid #e2e8f0;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .input-group .form-control:focus {
            border-color: #5d2fdf;
            box-shadow: 0 0 0 0.2rem rgba(93, 47, 223, 0.25);
            outline: none;
        }

        .input-group .btn-outline-secondary {
            border-radius: 0 8px 8px 0;
            border: 1px solid #e2e8f0;
            border-left: none;
            background-color: #f8f9fa;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .input-group .btn-outline-secondary:hover {
            background-color: #5d2fdf;
            border-color: #5d2fdf;
            color: white;
        }

        /* Responsive adjustments */
        @media (max-width: 991px) {
            .affiliate-section {
                margin-bottom: 1.5rem;
            }

            .section-header h5 {
                font-size: 1.1rem;
            }

            .section-header p {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 576px) {
            .affiliate-card {
                padding: 20px 15px;
            }

            .section-header h5 {
                font-size: 1rem;
            }

            .form-select,
            .input-group .form-control {
                padding: 10px 12px;
                font-size: 13px;
            }
        }
    </style>
@endpush

@section('content')
    <!------------ My profile area start  ------------>
    <section class="course-content">
        <div class="profile-banner-area"></div>
        <div class="container profile-banner-area-container">
            <div class="row">
                @include('frontend.default.student.left_sidebar')
                <div class="col-lg-9">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    @if(get_settings('affiliate_notification') == 1)

                    <!-- New Exclusive Discount Alert -->
                        <div class="exclusive-alert">
                            <div class="exclusive-alert-title">
                                <i class="fas fa-gift"></i>
                                {{ get_phrase('Exclusive Discount Available!') }}
                            </div>
                            <div class="exclusive-alert-content">
                                <p>{{ get_phrase('Contact the administrator to receive your exclusive discount code for your referrals.') }}
                                    {{ get_phrase('This special code will increase conversion rates when your referred users purchase courses.') }}</p>
                            </div>
                            <div class="exclusive-alert-footer">
                                <span
                                    class="text-danger">⚠️</span> {{ get_phrase('Note: These exclusive codes only work when buyers use your referral link.') }}
                            </div>
                        </div>
                    @endif

                    <div class="affiliate-card mb-4">
                        <div class="row g-4">
                            <div class="col-lg-6">
                                <div class="affiliate-section">
                                    <div class="section-header mb-3">
                                        <h5 class="mb-2">{{ get_phrase('Select a course') }}</h5>
                                        <p class="text-muted mb-0">Chọn khóa học để gửi link giới thiệu</p>
                                    </div>
                                    <div class="affiliate-course-container">
                                        <select name="course_id" id="choose_course" class="form-control form-select">
                                            <option value="">{{ get_phrase('Select a course ...') }}</option>
                                            @foreach($courses as $course)
                                                <option value="{{ $course->id }}"
                                                        data-link_affiliate="{{ route("course.details", ["slug"=>$course->slug,"ref"=> bin2hex("KH-".auth()->id())]) }}">{{ $course->title }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="affiliate-section">
                                    <div class="section-header mb-3">
                                        <h5 class="mb-2">{{ get_phrase('Your Affiliate Link') }}</h5>
                                        <p class="text-muted mb-0">{{ get_phrase('Share this link to earn commission from course purchases') }}</p>
                                    </div>
                                    <div class="affiliate-link-container">
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="affiliateLink" readonly
                                                   value="{{ route('home') }}?ref={{ bin2hex("KH-".auth()->id()) }}">
                                            <button class="btn btn-outline-secondary" type="button" id="copyButton">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="affiliate-info-box mt-3">
                            <div class="info-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="info-content">
                                <p class="info-title">{{ get_phrase('Commission Notifications') }}</p>
                                <p class="info-text">
                                    Khi người giới thiệu của bạn hoàn tất giao dịch mua, thông báo hoa hồng sẽ được gửi
                                    đến địa chỉ email đã đăng ký của bạn. Vui lòng kiểm tra thư mục thư rác và đánh dấu
                                    email của chúng tôi là "Không phải thư rác" để đảm bảo bạn nhận được những thông báo
                                    này một cách đáng tin cậy.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- New Metrics Row -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="metrics-card metrics-clicks">
                                <div class="metrics-icon">
                                    <i class="fas fa-mouse-pointer"></i>
                                </div>
                                <div class="metrics-value">{{ $total_clicks }}</div>
                                <div class="metrics-label">{{ get_phrase('Total Clicks') }}</div>
                            <!-- <div class="metrics-trend up">
                                    <i class="fas fa-arrow-up"></i> {{ $clicks_growth ?? '12%' }} {{ get_phrase('compared to last month') }}
                                </div> -->
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metrics-card metrics-purchases">
                                <div class="metrics-icon">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="metrics-value">{{ $total_purchases }}</div>
                                <div class="metrics-label">{{ get_phrase('Total Purchases') }}</div>
                            <!-- <div class="metrics-trend up">
                                    <i class="fas fa-arrow-up"></i> {{ $purchases_growth ?? '8%' }} {{ get_phrase('compared to last month') }}
                                </div> -->
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="metrics-card metrics-conversion">
                                <div class="metrics-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="metrics-value">{{ $conversion_rate }}%</div>
                                <div class="metrics-label">{{ get_phrase('Conversion Rate') }}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="affiliate-card balance-card h-100">
                                <p class="balance-title">{{ get_phrase('Total affiliate earnings') }} :</p>
                                <h2 class="balance-amount"><span
                                        class="currency-icon"></span>{{ $affiliate_earning_amount ?? 0}}</h2>
                                <div class="affiliate-info-box mt-3">
                                    <div class="info-icon">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <div class="info-content">
                                        <p class="info-title">{{ get_phrase('Earnings Release Policy') }}</p>
                                        <p class="info-text">{{ get_phrase('Commission from affiliate sales will be transferred to your available balance after a '.$affiliate_payout_days .'-day holding period. This allows time for any refunds or disputes to be processed.') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="affiliate-card balance-card d-flex flex-column justify-content-between h-100">
                                <div>
                                    <p class="balance-title">{{ get_phrase('Available balance to withdrawal') }}</p>
                                    <h2 class="balance-amount"><span
                                            class="currency-icon"></span>{{ currency($affiliate_balance_confirmed_amount ?? 0) }}
                                    </h2>
                                    <p class="text-muted small mt-2"><i class="fas fa-info-circle"></i> Số tiền rút tối
                                        thiểu: {{ currency($affiliate_payout_min_amount) }}</p>

                                </div>
                                <div class="text-end mt-3">
                                    <button class="request-btn" data-bs-toggle="modal"
                                            data-bs-target="#withdrawalModal" {{ ($affiliate_balance_confirmed_amount <= 0 || $affiliate_balance_confirmed_amount < $affiliate_payout_min_amount) ? 'disabled' : '' }}>
                                        <i class="fas fa-wallet me-2"></i>{{ get_phrase('Request a withdrawal') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="affiliate-card mt-4">
                        <div class="d-flex border-bottom pb-3 justify-content-between align-items-center">
                            <div>
                                <button class="tab-button active"
                                        id="earning-tab">{{ get_phrase('Earning history') }}</button>
                                <button class="tab-button"
                                        id="withdrawal-tab">{{ get_phrase('Withdrawal history') }}</button>
                                <button class="tab-button" id="coupon-tab">{{ get_phrase('My Coupons') }}</button>
                            </div>
                            <div>
                                <a href="{{ route('my.profile') }}#bank-info" class="bank-update-btn">
                                    <i class="fas fa-university"></i> {{ get_phrase('Update Bank Information') }}
                                </a>
                            </div>
                        </div>

                        <div class="tab-content">
                            <div id="earning-content" class="active-tab">
                                <div class="table-container">
                                    <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <th>{{ get_phrase('#') }}</th>
                                            <th>{{ get_phrase('Date') }}</th>
                                            <th>{{ get_phrase('Course') }}</th>
                                            <th>{{ get_phrase('Actual Amount') }}</th>
                                            <th>{{ get_phrase('Earned Amount') }}</th>
                                            <th>{{ get_phrase('Status') }}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @forelse($affiliate_histories as $i=>$affiliate_history)
                                            <tr>
                                                <td>{{ ++$i }}</td>
                                                <td>{{ $affiliate_history->created_at->format('H:i -d/m/Y') }}</td>
                                                <td>
                                                    {{ @$affiliate_history->course->title }}
                                                    <p class="mb-0 text-muted small">{{ 'Mua bởi:' }}:
                                                        {{ @$affiliate_history->user->name }}</p>
                                                </td>
                                                <td>{{ currency(@$affiliate_history->total_amount) }}</td>
                                                <td>
                                                    {{ currency($affiliate_history->affiliate_amount) }}
                                                </td>
                                                <td>
                                                    <span
                                                        class="badge bg-{{ $affiliate_history->is_approve_affiliate == 1 ? 'success' : 'warning' }}">
                                                        {{ $affiliate_history->is_approve_affiliate == 1 ? get_phrase('Approved') : get_phrase('Pending') }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @empty
                                            <!-- Example Data -->
                                            <tr>
                                                <td colspan="6" class="text-center">
                                                    Không có dữ liệu
                                                </td>

                                            </tr>
                                        @endforelse
                                        </tbody>
                                    </table>
                                    </div>
                                </div>


                                @if($affiliate_histories->hasPages())
                                    <div class="mt-4">
                                        {{ $affiliate_histories->links() }}
                                    </div>
                                @endif
                            </div>

                            <div id="withdrawal-content" class="d-none">
                                <div class="table-container">
                                    <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <th>{{ get_phrase('#') }}</th>
                                            <th>{{ get_phrase('Date') }}</th>
                                            <th>{{ get_phrase('Amount') }}</th>
                                            <th>{{ get_phrase('Bank Info') }}</th>
                                            <th>{{ get_phrase('Processed Date') }}</th>
                                            <th>{{ get_phrase('Status') }}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @forelse($withdrawals as $k=>$withdrawal)
                                            <tr>
                                                <td>{{ ++$k }}</td>
                                                <td>
                                                    @if(is_int($withdrawal->request_date))
                                                        {{ date('H:i - d/m/Y', $withdrawal->request_date) }}
                                                    @elseif(is_string($withdrawal->request_date))
                                                        {{ $withdrawal->request_date }}
                                                    @elseif($withdrawal->request_date instanceof \DateTime || $withdrawal->request_date instanceof \Carbon\Carbon)
                                                        {{ $withdrawal->request_date->format('H:i - m/d/Y') }}
                                                    @else
                                                        {{ get_phrase('N/A') }}
                                                    @endif
                                                </td>
                                                <td>{{ currency($withdrawal->amount) }}</td>
                                                <td>
                                                    @if(!empty($withdrawal->note))
                                                        <small>{!! nl2br(e($withdrawal->note)) !!}</small>
                                                    @else
                                                        <small class="text-muted">{{ get_phrase('N/A') }}</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($withdrawal->processed_date)
                                                        @if(is_int($withdrawal->processed_date))
                                                            {{ date('H:i - d/m/Y', $withdrawal->processed_date) }}
                                                        @elseif(is_string($withdrawal->processed_date))
                                                            {{ $withdrawal->processed_date }}
                                                        @else
                                                            {{ \Carbon\Carbon::parse($withdrawal->processed_date)->format('H:i - m/d/Y') }}
                                                        @endif
                                                    @else
                                                        <small class="text-muted">{{ get_phrase('N/A') }}</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($withdrawal->status == 1)
                                                        <span class="badge bg-success">
                                                                    <i class="fas fa-check-circle me-1"></i>
                                                                    {{ get_phrase('Completed') }}
                                                                </span>
                                                    @elseif($withdrawal->status == 2)
                                                        <span class="badge bg-danger">
                                                                    <i class="fas fa-times-circle me-1"></i>
                                                                    {{ get_phrase('Cancelled') }}
                                                                </span>
                                                    @else
                                                        <div class="d-flex align-items-center gap-2">
                                                                    <span class="badge bg-warning">
                                                                        <i class="fas fa-clock me-1"></i>
                                                                        {{ get_phrase('Pending') }}
                                                                    </span>
                                                            <button type="button"
                                                                    class="btn btn-danger btn-sm withdrawal-action-btn cancel-withdrawal-btn"
                                                                    data-id="{{ $withdrawal->id }}"
                                                                    data-amount="{{ currency($withdrawal->amount) }}">
                                                                <i class="fas fa-times me-1"></i>
                                                                {{ get_phrase('Cancel') }}
                                                            </button>
                                                        </div>
                                                    @endif
                                                </td>
                                            </tr>
                                        @empty
                                            <!-- Example Data -->
                                            <tr>
                                                <td colspan="6" class="text-center">
                                                    Không có dữ liệu
                                                </td>

                                            </tr>
                                        @endforelse

                                        </tbody>
                                    </table>
                                    </div>
                                </div>
                                @if($withdrawals->hasPages())
                                    <div class="mt-4">
                                        {{ $withdrawals->links() }}
                                    </div>
                                @endif
                            </div>

                            <div id="coupon-content" class="d-none">
                                <div class="table-container">
                                    <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <th>{{ get_phrase('#') }}</th>
                                            <th>Khóa học</th>
                                            <th>{{ get_phrase('Code') }}</th>
                                            <th>{{ get_phrase('Discount (%)') }}</th>
                                            <th>{{ get_phrase('Expired Date') }}</th>
                                            <th>{{ get_phrase('Quantity') }}</th>
                                            <th>{{ get_phrase('Share Link') }}</th>
                                            <th>Trạng thái</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @forelse($coupons as $c=>$coupon)
                                            <tr>
                                                <td>{{ ++$c }}</td>
                                                @if($coupon->course_id)
                                                    <td>Áp dụng cho khóa học: <a
                                                            href="{{ route('course.details', $coupon->course->slug) }}">{{ $coupon->course->title }}</a>
                                                    </td>
                                                @else
                                                    <td>Áp dụng cho khóa học: <a href="{{ route('home') }}">Tất cả khóa
                                                            học</a></td>
                                                @endif
                                                <td>{{ $coupon->code }}</td>
                                                <td>{{ $coupon->discount }}</td>
                                                <td>
                                                    @if(is_string($coupon->expiry))
                                                        {{ date('d/m/Y', $coupon->expiry) }}

                                                    @elseif($coupon->expiry instanceof \DateTime || $coupon->expiry instanceof \Carbon\Carbon)
                                                        {{ $coupon->expiry->format('d/m/Y') }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>{{ $coupon->quantity===null?"Không giới hạn" :  number_format($coupon->quantity) }}</td>
                                                @if($coupon->course_id)
                                                    <td>
                                                        <span class="copy-link-aff-coupon"
                                                              data-url="{{ route("course.details", ["slug"=>@$coupon->course->slug,"ref" => bin2hex("KH-".auth()->id()),"coupon"=>$coupon->code]) }}">
                                                                <i class="fas fa-copy"></i> {{ get_phrase('Copy Link') }}
                                                            </span>
                                                    </td>
                                                @else
                                                    <td>
                                                        <span class="copy-link-aff-coupon"
                                                              data-url="{{ route('home') }}?ref={{ bin2hex("KH-".auth()->id()) }}&coupon={{ $coupon->code }}">
                                                                <i class="fas fa-copy"></i> {{ get_phrase('Copy Link') }}
                                                            </span>
                                                    </td>
                                                @endif
                                                <td>
                                                    @if($coupon->status == 1)
                                                        <span class="badge bg-success text-white">{{ get_phrase('Public') }}</span>
                                                    @elseif($coupon->status == 2)
                                                        <span class="badge bg-warning text-white">{{ get_phrase('Private') }}</span>
                                                    @else
                                                        <span class="badge bg-danger text-white">{{ get_phrase('Disable') }}</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @empty
                                            <!-- Example Data -->
                                            <tr>
                                                <td colspan="7" class="text-center">
                                                    Không có dữ liệu
                                                </td>

                                            </tr>
                                        @endforelse
                                        </tbody>
                                    </table>
                                    </div>
                                </div>
                                @if($coupons->hasPages())
                                    <div class="mt-4">
                                        {{ $coupons->links() }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Withdrawal Modal -->
    <div class="modal fade" id="withdrawalModal" tabindex="-1" aria-labelledby="withdrawalModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="withdrawalModalLabel">{{ get_phrase('Request Withdrawal') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                @if(!auth()->user()->bank_name || !auth()->user()->bank_account_number || !auth()->user()->bank_account_name)
                    <div class="modal-body">
                        <div class="text-center mb-4">
                            <i class="fas fa-exclamation-circle text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <div class="alert alert-warning">
                            <p>{{ get_phrase('Please configure your bank information before requesting a withdrawal.') }}</p>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ route('my.profile') }}#bank-info" class="bank-update-btn btn-lg">
                                <i class="fas fa-university"></i> {{ get_phrase('Update Bank Information') }}
                            </a>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>
                            {{ get_phrase('Close') }}
                        </button>
                    </div>
                @else
                    <form action="{{ route('request.withdrawal') }}" method="post">
                        @csrf
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="amount" class="form-label">{{ get_phrase('Amount') }}</label>
                                <div class="input-group currency-input">
                                    <input type="text" class="form-control" id="amount" name="amount"
                                           min="{{ $affiliate_payout_min_amount }}"
                                           max="{{ $affiliate_balance_confirmed_amount }}" step="1000" required
                                           placeholder="0"
                                           autocomplete="off">

                                </div>
                                @if(session('error_withdraw'))
                                    <div class="mt-2 alert alert-danger alert-dismissible fade show" role="alert">
                                        {{ session('error_withdraw') }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"
                                                aria-label="Close"></button>
                                    </div>
                                @endif
                                <div class="d-flex justify-content-between mt-2">
                                    <small
                                        class="form-text text-muted">{{ get_phrase('Minimum: '.currency($affiliate_payout_min_amount).' - Maximum: ') }}{{ currency($affiliate_balance_confirmed_amount) }}</small>
                                    <a href="#" id="withdrawAllBtn"
                                       class="text-primary">{{ get_phrase('Withdraw all available balance') }}</a>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary btn-lg"
                                    data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>
                                {{ get_phrase('Cancel') }}
                            </button>
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>
                                {{ get_phrase('Submit Withdraw Request') }}
                            </button>
                        </div>
                    </form>
                @endif
            </div>
        </div>
    </div>

    <!-- Cancel Withdrawal Confirmation Modal -->
    <div class="modal fade" id="cancelWithdrawalModal" tabindex="-1" aria-labelledby="cancelWithdrawalModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"
                        id="cancelWithdrawalModalLabel">{{ get_phrase('Cancel Withdrawal Request') }}
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <form action="" method="post" id="cancelWithdrawalForm">
                    @csrf
                    <div class="modal-body">
                        <div class="text-center mb-4">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        </div>
                        <p class="mb-3">{{ get_phrase('Are you sure you want to cancel this withdrawal request?') }}</p>
                        <p class="mb-0">{{ get_phrase('Amount') }}: <span id="cancelAmount" class="fw-bold"></span></p>
                        <p class="small text-muted mt-2">
                            {{ get_phrase('This amount will be returned to your available balance.') }}</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            {{ get_phrase('No, keep my request') }}
                        </button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times me-1"></i>
                            {{ get_phrase('Yes, cancel request') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
@push('js')

    <script>
        $(document).ready(function () {
            $('#choose_course').change(function () {
                var course_id = $(this).val();
                var link_affiliate = "{{ route('home') }}?ref={{ bin2hex("KH-".auth()->id()) }}";
                if (course_id) {
                    link_affiliate = $(this).find('option:selected').data('link_affiliate');
                }
                $('#affiliateLink').val(link_affiliate);

            });
        });
    </script>
    <script>
        $(document).ready(function () {
            // Tab switching
            $('#earning-tab').click(function () {
                $(this).addClass('active');
                $('#withdrawal-tab').removeClass('active');
                $('#coupon-tab').removeClass('active');
                $('#earning-content').removeClass('d-none');
                $('#withdrawal-content').addClass('d-none');
                $('#coupon-content').addClass('d-none');
            });

            $('#withdrawal-tab').click(function () {
                $(this).addClass('active');
                $('#earning-tab').removeClass('active');
                $('#coupon-tab').removeClass('active');
                $('#withdrawal-content').removeClass('d-none');

                $('#earning-content').addClass('d-none');
                $('#coupon-content').addClass('d-none');
            });

            $('#coupon-tab').click(function () {
                $(this).addClass('active');
                $('#coupon-content').removeClass('d-none');
                $('#coupon-content').addClass('active');

                $('#withdrawal-tab').removeClass('active');
                $('#withdrawal-content').addClass('d-none');

                $('#earning-tab').removeClass('active');
                $('#earning-content').addClass('d-none');
            });

            // Affiliate link copy functionality
            const affiliateLink = document.getElementById('affiliateLink');
            const copyButton = document.getElementById('copyButton');


            function copyToClipboard() {
                // Sử dụng Clipboard API hiện đại thay vì document.execCommand
                navigator.clipboard.writeText(affiliateLink.value)
                    .then(() => {
                        // Show success feedback
                        const originalText = copyButton.innerHTML;
                        copyButton.innerHTML = '<i class="fas fa-check"></i> copied';
                        setTimeout(() => {
                            copyButton.innerHTML = originalText;
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Could not copy text: ', err);
                        // Fallback method for older browsers
                        try {
                            affiliateLink.select();
                            document.execCommand('copy');
                        } catch (err) {
                            console.error('Fallback copy method failed: ', err);
                            alert('{{ get_phrase("Could not copy link. Please manually select and copy the link.") }}');
                        }
                    });
            }

            if (copyButton) {
                copyButton.addEventListener('click', copyToClipboard);
            }

            // Copy affiliate coupon link functionality
            $('.copy-link-aff-coupon').on('click', function () {
                const url = $(this).data('url');
                navigator.clipboard.writeText(url)
                    .then(() => {
                        // Show success feedback
                        const originalText = $(this).html();
                        $(this).html('<i class="fas fa-check"></i> Copy thành công!');
                        setTimeout(() => {
                            $(this).html(originalText);
                        }, 2000);
                    })
                    .catch(err => {
                        console.error('Could not copy text: ', err);
                        // Fallback method for older browsers
                        try {
                            const tempInput = document.createElement('input');
                            tempInput.value = url;
                            document.body.appendChild(tempInput);
                            tempInput.select();
                            document.execCommand('copy');
                            document.body.removeChild(tempInput);

                            // Show success feedback
                            const originalText = $(this).html();
                            $(this).html('<i class="fas fa-check"></i> Copy thành công!');
                            setTimeout(() => {
                                $(this).html(originalText);
                            }, 2000);
                        } catch (err) {
                            console.error('Fallback copy method failed: ', err);
                            alert('{{ get_phrase("Could not copy link. Please manually select and copy the link.") }}');
                        }
                    });
            });

            // Animation for metrics cards
            function animateMetrics() {
                $('.metrics-card').each(function () {
                    $(this).addClass('animate__animated animate__fadeInUp');

                    // Add a small delay between each card animation
                    let delay = $(this).index() * 100;
                    $(this).css('animation-delay', delay + 'ms');
                });
            }

            // Call the animation when page loads
            animateMetrics();

            // Add hover effects for metrics cards
            $('.metrics-card').hover(
                function () {
                    $(this).addClass('animate__pulse');
                },
                function () {
                    $(this).removeClass('animate__pulse');
                }
            );

            // Xử lý sự kiện khi nhấn nút hủy yêu cầu rút tiền
            $('.cancel-withdrawal-btn').on('click', function () {
                const withdrawalId = $(this).data('id');
                const amount = $(this).data('amount');

                // Cập nhật thông tin trong modal
                $('#cancelAmount').text(amount);

                // Cập nhật action của form
                const formAction = "{{ route('cancel.withdrawal', ':id') }}".replace(':id', withdrawalId);
                $('#cancelWithdrawalForm').attr('action', formAction);

                // Hiển thị modal xác nhận
                $('#cancelWithdrawalModal').modal('show');
            });

            // Format tiền tệ cho input số tiền rút
            const amountInput = document.getElementById('amount');
            const maxAmount = parseFloat('{{ $affiliate_balance_confirmed_amount }}');

            // Định dạng số với dấu phẩy ngăn cách hàng nghìn (không có phần thập phân)
            function formatNumber(number) {
                // Chuyển đổi thành số nguyên và định dạng
                return Math.floor(Number(number)).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }

            // Chuyển đổi từ chuỗi được định dạng sang số
            function parseFormattedNumber(formattedNumber) {
                return parseInt(formattedNumber.replace(/,/g, '')) || 0;
            }

            // Rút toàn bộ số dư
            $('#withdrawAllBtn').click(function (e) {
                e.preventDefault();
                amountInput.value = formatNumber(maxAmount);
            });

            if (amountInput) {
                let cursorPosition = 0;
                let prevValue = '';

                // Format số tiền trong input
                function formatAmountInput() {
                    // Lưu vị trí con trỏ
                    cursorPosition = amountInput.selectionStart;

                    // Lưu độ dài trước khi định dạng
                    const oldLength = amountInput.value.length;

                    // Loại bỏ các ký tự không phải số
                    let value = amountInput.value.replace(/[^\d]/g, '');

                    // Định dạng số tiền với dấu phẩy
                    let formattedValue = value;
                    if (value !== '') {
                        formattedValue = formatNumber(value);
                    }

                    // Chỉ cập nhật nếu giá trị thay đổi để tránh vấn đề với việc di chuyển con trỏ
                    if (prevValue !== formattedValue) {
                        amountInput.value = formattedValue;
                        prevValue = formattedValue;

                        // Điều chỉnh vị trí con trỏ
                        const newLength = amountInput.value.length;
                        const lengthDiff = newLength - oldLength;
                        amountInput.setSelectionRange(cursorPosition + lengthDiff, cursorPosition + lengthDiff);
                    }
                }

                // Xử lý sự kiện input
                amountInput.addEventListener('input', function (e) {
                    formatAmountInput();
                });

                // Xử lý sự kiện keydown để giữ vị trí con trỏ
                amountInput.addEventListener('keydown', function (e) {
                    cursorPosition = amountInput.selectionStart;
                    prevValue = amountInput.value;
                });

                // Format khi blur
                amountInput.addEventListener('blur', function (e) {
                    if (e.target.value.trim() !== '') {
                        try {
                            const numValue = parseFormattedNumber(e.target.value);
                            if (numValue > 0) {
                                e.target.value = formatNumber(numValue);
                            } else {
                                e.target.value = '';
                            }
                        } catch (error) {
                            console.error('Error formatting number:', error);
                            e.target.value = '';
                        }
                    }
                });

                // Kiểm tra giá trị tối đa khi submit
                document.querySelector('form').addEventListener('submit', function (e) {
                    const enteredAmount = parseFormattedNumber(amountInput.value);

                    if (enteredAmount <= 0) {
                        e.preventDefault();
                        alert('{{ get_phrase("Please enter a valid amount") }}');
                        amountInput.focus();
                        return;
                    }

                    if (enteredAmount > maxAmount) {
                        e.preventDefault();
                        let formattedMax = formatNumber(maxAmount);
                        amountInput.value = formattedMax;
                        amountInput.focus();
                    }
                });

                // Khi mở modal, reset form
                $('#withdrawalModal').on('shown.bs.modal', function () {
                    amountInput.value = '';
                    setTimeout(function () {
                        amountInput.focus();
                    }, 500);
                });
            }
            @if(session('error_withdraw'))
            $('#withdrawalModal').modal('show');
            @endif
        });
    </script>
@endpush

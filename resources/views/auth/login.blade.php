@extends('layouts' . '.' . get_frontend_settings('theme'))
@push('title', get_phrase('Log In'))
@push('meta')@endpush
@push('css')
    <style>
        .form-icons .right {
            right: 20px;
            cursor: pointer !important;
        }

        .login-with-google-btn span {
            transition: background-color .3s, box-shadow .3s;
            padding: 12px 16px 12px 42px;
            border: none;
            border-radius: 3px;
            color: #757575;
            font-size: 14px;
            font-weight: 500;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
            background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBkPSJNMTcuNiA5LjJsLS4xLTEuOEg5djMuNGg0LjhDMTMuNiAxMiAxMyAxMyAxMiAxMy42djIuMmgzYTguOCA4LjggMCAwIDAgMi42LTYuNnoiIGZpbGw9IiM0Mjg1RjQiIGZpbGwtcnVsZT0ibm9uemVybyIvPjxwYXRoIGQ9Ik05IDE4YzIuNCAwIDQuNS0uOCA2LTIuMmwtMy0yLjJhNS40IDUuNCAwIDAgMS04LTIuOUgxVjEzYTkgOSAwIDAgMCA4IDV6IiBmaWxsPSIjMzRBODUzIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNNCAxMC43YTUuNCA1LjQgMCAwIDEgMC0zLjRWNUgxYTkgOSAwIDAgMCAwIDhsMy0yLjN6IiBmaWxsPSIjRkJCQzA1IiBmaWxsLXJ1bGU9Im5vbnplcm8iLz48cGF0aCBkPSJNOSAzLjZjMS4zIDAgMi41LjQgMy40IDEuM0wxNSAyLjNBOSA5IDAgMCAwIDEgNWwzIDIuNGE1LjQgNS40IDAgMCAxIDUtMy43eiIgZmlsbD0iI0VBNDMzNSIgZmlsbC1ydWxlPSJub256ZXJvIi8+PHBhdGggZD0iTTAgMGgxOHYxOEgweiIvPjwvZz48L3N2Zz4=);
            background-color: white;
            background-repeat: no-repeat;
            background-position: 12px 15px;
        }

        .login-with-google-btn:hover {
            box-shadow: 0 -1px 0 rgba(0, 0, 0, .04), 0 2px 4px rgba(0, 0, 0, .25);
        }

        .login-with-google-btn:active {
            background-color: #eeeeee;
        }

        .login-with-google-btn:focus {
            outline: none;
            box-shadow: 0 -1px 0 rgba(0, 0, 0, .04),
            0 2px 4px rgba(0, 0, 0, .25),
            0 0 0 3px #c8dafc;
        }

        .login-with-google-btn:disabled {
            filter: grayscale(100%);
            background-color: #ebebeb;
            box-shadow: 0 -1px 0 rgba(0, 0, 0, .04), 0 1px 1px rgba(0, 0, 0, .25);
            cursor: not-allowed;
        }
    </style>
@endpush
@section('content')
    @if (get_frontend_settings('theme') == 'default')
        <!------------------- Login Area Start  ------>
        <section class="login-area">
            <div class="container">
                <div class="row">
                    <div class="col-lg-7 col-md-6">
                        <div class="login-img">
                            <img src="{{ asset('assets/frontend/default/image/login.gif') }}" alt="...">
                        </div>
                    </div>
                    <div class="col-lg-5 col-md-6">
                        <form action="{{ route('login') }}" class="global-form login-form mt-25" id="login-form"
                              method="POST">
                            @csrf
                            <h4 class="g-title">{{ get_phrase('Login') }}</h4>
                            <p class="description">{{ get_phrase('See your growth and get consulting support!') }} </p>
                            <div class="form-group">
                                <label for="email" class="form-label">{{ get_phrase('Email') }}</label>
                                <input type="email" id="email" name="email" class="form-control"
                                       placeholder="{{ get_phrase('Your Email') }}">
                            </div>
                            <div class="form-group">
                                <label for="" class="form-label">{{ get_phrase('Password') }}</label>
                                <input type="password" id="password" name="password" class="form-control"
                                       placeholder="*********">
                            </div>
                            <div class="form-group mb-25 d-flex justify-content-between align-items-center remember-me">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="" id="flexCheckChecked"
                                           checked>
                                    <label class="form-check-label"
                                           for="flexCheckChecked">{{ get_phrase('Remember Me') }}</label>
                                </div>
                                <a href="{{route('password.request')}}">{{ get_phrase('Forget Password?') }}</a>
                            </div>

                            @if (get_frontend_settings('google_status'))
                                <div class="p-2">
                                    <a href="{{ route('auth.google.redirect') }}"
                                       class="login-with-google-btn eBtn w-100">
                                        <span>Login with Google</span>
                                    </a>
                                </div>
                            @endif

                            @if(get_frontend_settings('recaptcha_status'))
                                <button class="eBtn gradient w-100 g-recaptcha"
                                        data-sitekey="{{ get_frontend_settings('recaptcha_sitekey') }}"
                                        data-callback='onLoginSubmit'
                                        data-action='submit'>{{ get_phrase('Login') }}</button>
                            @else
                                <button type="submit" class="eBtn gradient w-100">{{ get_phrase('Login') }}</button>
                            @endif

                            <p class="mt-20">{{ get_phrase('Not have an account yet?') }}
                                <a href="{{ route('register.form') }}">{{ get_phrase('Create Account') }}</a>
                            </p>

                            {{-- <p class="my-3">Login As -</p>
                            <button type="button" class="eBtn gradient w-100 mb-3 py-3 custom-btn" id="admin">Admin</button>
                            <button type="button" class="eBtn gradient w-100 mb-3 py-3 custom-btn" id="student">Student</button>
                            <button type="button" class="eBtn gradient w-100 mb-3 py-3 custom-btn" id="instructor">Instructor</button> --}}
                        </form>
                    </div>
                </div>
            </div>
        </section>
        <!------------------- Login Area End  ------>
    @endif
@endsection
@push('js')

    <script>
        "use strict";

        $(document).ready(function () {
            $('.custom-btn').on('click', function (e) {
                e.preventDefault();

                var role = $(this).attr('id');
                if (role == 'admin') {
                    $('#email').val('<EMAIL>');
                    $('#password').val('12345678');
                } else if (role == 'student') {
                    $('#email').val('<EMAIL>');
                    $('#password').val('12345678');
                } else {
                    $('#email').val('<EMAIL>');
                    $('#password').val('12345678');
                }
                $('#login').trigger('click');
            });
        });

        $(document).ready(function () {
            $('#showpassword').on('click', function (e) {
                e.preventDefault();
                const type = $('#password').attr('type');

                if (type == 'password') {
                    $('#password').attr('type', 'text');
                } else {
                    $('#password').attr('type', 'password');
                }
            });
        });

        function onLoginSubmit(token) {
            document.getElementById("login-form").submit();
        }

    </script>
@endpush

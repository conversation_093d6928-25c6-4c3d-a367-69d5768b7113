    @php $current_route_name = Route::currentRouteName(); @endphp
@php

    if (session('home')) {
        $home_page = App\Models\Builder_page::where('id', session('home'))->firstOrNew();
    } else {
        $home_page = App\Models\Builder_page::where('status', 1)->firstOrNew();
    }

    if(!empty($course_details->builder_ids)){
        $page_id = $course_details->builder_ids;
    }else{
        $page_id = $home_page->id;
    }
    $page = App\Models\Builder_page::find($page_id);
    $disable_bootstrap = false;
    if(!empty($page->disable_bootstrap)){
        $disable_bootstrap = $page->disable_bootstrap;
    }

@endphp
    <!DOCTYPE html>
<html lang="en">
<head>
@include('layouts.seo')
@stack('meta')

<!-- fav icon -->
    <link rel="shortcut icon" href="{{ asset(get_frontend_settings('favicon')) }}"/>

    <!-- owl carousel -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/owl.carousel.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/owl.theme.default.min.css') }}">

    <!-- Jquery Ui Css -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/jquery-ui.css') }}">

    <!-- Nice Select Css -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/nice-select.css') }}">

    <!-- Fontawasome Css -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/all.min.css') }}">

    {{-- New Css Link --}}
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/vendors/swiper/swiper-bundle.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/vendors/slick/slick.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/vendors/slick/slick-theme.css') }}">

    <!-- Flat Pickr -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/vendors/flatpickr/flatpickr.min.css') }}">

    <!-- FlatIcons Css -->
    <link rel="stylesheet" href="{{ asset('assets/global/icons/uicons-bold-rounded/css/uicons-bold-rounded.css') }}"/>
    <link rel="stylesheet"
          href="{{ asset('assets/global/icons/uicons-regular-rounded/css/uicons-regular-rounded.css') }}"/>
    <link rel="stylesheet" href="{{ asset('assets/global/icons/uicons-solid-rounded/css/uicons-solid-rounded.css') }}"/>

    <!-- Custom Fonts -->
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/custome-front/custom-fronts.css') }}">

    <!-- Player Css -->
    {{--    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/plyr.css') }}">--}}

<!-- Bootstrap Css -->
    {{--        <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/bootstrap.min.css') }}">--}}

<!-- Main Css -->
    {{--    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/responsive.css') }}">--}}

<!-- Yaireo Tagify -->
    {{--    <link rel="stylesheet" type="text/css" href="{{ asset('assets/global/tagify-master/dist/tagify.css') }}"--}}
    {{--          rel="stylesheet" type="text/css"/>--}}

<!-- Custom Style -->
    {{--    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/new_responsive.css') }}">--}}
    {{--    <link rel="stylesheet" href="{{ asset('assets/global/global.css') }}"/>--}}

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"/>

    <!-- Jquery Js -->
    <script src="{{ asset('assets/frontend/default/js/jquery-3.7.1.min.js') }}"></script>


    {{--Caaus hinhf cuar theme --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css">

    <link rel="stylesheet" href="{{ asset('assets/frontend/dau-xanh/assets/js/fancybox/dist/jquery.fancybox.css')}}">
    <!--them-->
    <link rel="stylesheet" href="{{ asset('assets/frontend/dau-xanh/assets/fonts/awesome6/css/all.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/frontend/dau-xanh/assets/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/frontend/dau-xanh/assets/css/app.css') }}">
    <style>

    </style>
    @stack('css')

    @if (get_frontend_settings('recaptcha_status'))
        <script
            src="https://www.google.com/recaptcha/api.js?render={{ get_frontend_settings('recaptcha_sitekey') }}"></script>
    @endif
<!-- Custom Scripts (Head) -->
    {!! get_settings('custom_script_head') !!}
</head>

<body>
<!-- Custom Scripts (Body Start) -->
{!! get_settings('custom_script_body_start') !!}
@yield('content')

@if(!$disable_bootstrap)
    <!-- Bootstrap Js -->
    {{--    <script src="{{ asset('assets/frontend/default/js/bootstrap.bundle.min.js') }}"></script>--}}
@endif

<!-- nice select js -->
<script src="{{ asset('assets/frontend/default/js/jquery.nice-select.min.js') }}"></script>

{{-- New Js Link  --}}
<script src="{{ asset('assets/frontend/default/vendors/swiper/swiper-bundle.min.js') }}"></script>
<script src="{{ asset('assets/frontend/default/vendors/counterup/jquery.counterup.min.js') }}"></script>
<script src="{{ asset('assets/frontend/default/vendors/counterup/jquery.waypoints.js') }}"></script>
<script src="{{ asset('assets/frontend/default/vendors/slick/slick.min.js') }}"></script>

<script src="{{ asset('assets/frontend/default/vendors/flatpickr/flatpickr.min.js') }}"></script>

<!-- owl carousel js -->
<script src="{{ asset('assets/frontend/default/js/owl.carousel.min.js') }}"></script>


<!-- Player Js -->
<script src="{{ asset('assets/frontend/default/js/plyr.js') }}"></script>


<!-- Yaireo Tagify -->
<script src="{{ asset('assets/global/tagify-master/dist/tagify.min.js') }}"></script>


<!-- Jquery Ui Js -->
<script src="{{ asset('assets/frontend/default/js/jquery-ui.min.js') }}"></script>


<!-- price range Js -->
<script src="{{ asset('assets/frontend/default/js/price_range_script.js') }}"></script>


<!-- Main Js -->
<script src="{{ asset('assets/frontend/default/js/script.js') }}"></script>


<script src="https://www.google.com/recaptcha/api.js" async defer></script>
{{--Theme--}}

<script src="{{ asset('assets/frontend/dau-xanh/assets/js/popper.min.js') }}"></script>
<script src="{{ asset('assets/frontend/dau-xanh/assets/js/bootstrap.min.js') }}"></script>
{{--<script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>--}}

<script src="{{ asset('assets/frontend/dau-xanh/assets/js/fancybox/dist/jquery.fancybox.js')}}"></script><!--them-->
<script src="{{ asset('assets/frontend/dau-xanh/assets/js/moment/moment.js') }}"></script>
<script src="{{ asset('assets/frontend/dau-xanh/assets/js/app.js') }}"></script>
<script>
    $(function () {
        let endTime = moment($('#countdown').data('time'));
        let now = moment();
        var current_date = new Date().getTime();
        setInterval(function () {
            now = now.add(1, "seconds");
            let diff = endTime.diff(now, "seconds");
            if (diff < 0) {
                $('#countdown').hide();
                $('#countdown').text('Khuyến mại đã kết thúc');
            } else {
                let days = Math.floor(diff / 86400).toString().padStart(2, 0);
                let hours = Math.floor((diff - (days * 86400)) / 3600).toString().padStart(2, 0);
                let minutes = Math.floor((diff - (days * 86400) - (hours * 3600)) / 60).toString().padStart(2, 0);
                let seconds = Math.floor(diff - (days * 86400) - (hours * 3600) - (minutes * 60)).toString().padStart(2, 0);

                let text = "";
                if (days > 0) {
                    text += `<div><label id="days">${days}</label></div>`
                }
                text += `<div><label id="hour">${hours}</label></div><div><label id="minute">${minutes}</label></div><div><label id="second">${seconds}</label></div>`
                $('#countdown').html(text);
            }
        }, 1000);
    });
    $(function () {
        let endTime = moment($('#countdown2').data('time'));
        let now = moment();
        var current_date = new Date().getTime();
        setInterval(function () {
            now = now.add(1, "seconds");
            let diff = endTime.diff(now, "seconds");
            if (diff < 0) {
                $('#countdown2').hide();
                $('#countdown2').text('Khuyến mại đã kết thúc');
            } else {
                let days = Math.floor(diff / 86400).toString().padStart(2, 0);
                let hours = Math.floor((diff - (days * 86400)) / 3600).toString().padStart(2, 0);
                let minutes = Math.floor((diff - (days * 86400) - (hours * 3600)) / 60).toString().padStart(2, 0);
                let seconds = Math.floor(diff - (days * 86400) - (hours * 3600) - (minutes * 60)).toString().padStart(2, 0);

                let text = "";
                if (days > 0) {
                    text += `<div><label id="days">${days}</label></div>`
                }
                text += `<div><label id="hour">${hours}</label></div><div><label id="minute">${minutes}</label></div><div><label id="second">${seconds}</label></div>`
                $('#countdown2').html(text);
            }
        }, 1000);
    });
</script>
@stack('js')

<!-- Custom Scripts (Footer) -->
{{--{!! get_settings('custom_script_footer') !!}--}}
</body>

</html>

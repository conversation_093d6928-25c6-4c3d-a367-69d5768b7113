@php $current_route_name = Route::currentRouteName(); @endphp
@php

    if (session('home')) {
        $home_page = App\Models\Builder_page::where('id', session('home'))->firstOrNew();
    } else {
        $home_page = App\Models\Builder_page::where('status', 1)->firstOrNew();
    }

    if(!empty($course_details->builder_ids)){
        $page_id = $course_details->builder_ids;
    }else{
        $page_id = $home_page->id;
    }
    $page = App\Models\Builder_page::find($page_id);
    $disable_bootstrap = false;
    if(!empty($page->disable_bootstrap)){
        $disable_bootstrap = $page->disable_bootstrap;
    }

@endphp
    <!DOCTYPE html>
<html lang="en">
<head>
@include('layouts.seo')
@stack('meta')

<!-- fav icon -->
    <link rel="shortcut icon" href="{{ asset(get_frontend_settings('favicon')) }}"/>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <script src="https://cdn.tailwindcss.com"></script>
    <link
        href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
        rel="stylesheet"
    />
    <link rel="stylesheet" href="{{ asset('assets/global/global.css') }}"/>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&display=swap"
        rel="stylesheet"
    />
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/modal-auth.css') }}">
    <link
        rel="stylesheet"
        href="https://maikimquy.com/assets/frontend/mst-academy/assets/libs/aos/aos-*******-beta.6.css"
    />
    <link
        rel="stylesheet"
        type="text/css"
        href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"
    />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"/>
    <!-- Jquery Ui Js -->
    <script src="https://maikimquy.com/assets/frontend/default/js/jquery-3.7.1.min.js"></script>
    <script src="https://maikimquy.com/assets/frontend/default/js/jquery-ui.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    maxWidth: {
                        "6xl": "1200px",
                    },
                    spacing: {
                        15: "60px",
                    },
                    colors: {
                        red: {
                            600: "#B4332B",
                        },
                        orange: {
                            500: "#c18338",
                        },
                        amber: {
                            900: "#4a251c",
                        },
                    },

                    backgroundSize: {
                        fill: "100% 100%",
                    },

                    backgroundPosition: {
                        "center-left": "50% 0",
                    },
                },
            },
        };
    </script>

    <link rel="stylesheet" href="{{ asset('assets/frontend/dieu-thu/assets/styles.css') }}" />
    @stack('css')

    @if (get_frontend_settings('recaptcha_status'))
        <script
            src="https://www.google.com/recaptcha/api.js?render={{ get_frontend_settings('recaptcha_sitekey') }}"></script>
    @endif
<!-- Custom Scripts (Head) -->
    {!! get_settings('custom_script_head') !!}
</head>

<body>
<!-- Custom Scripts (Body Start) -->
{!! get_settings('custom_script_body_start') !!}
@yield('content')

@if(!$disable_bootstrap)
    <!-- Bootstrap Js -->
    {{--    <script src="{{ asset('assets/frontend/default/js/bootstrap.bundle.min.js') }}"></script>--}}
@endif


<!-- Main Js -->
<script src="{{ asset('assets/frontend/home/<USER>/script/script.js') }}"></script>

<script src="{{ asset('assets/frontend/mst-academy/assets/libs/aos/aos-*******-beta.6.js') }}"></script>
<script
    type="text/javascript"
    src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"
></script>

<!-- Slider JavaScript -->
<script>
    $(document).ready(function () {
        $("#sliderTrack").slick({
            prevArrow: $(".slide-prev"),
            nextArrow: $(".slide-next"),
            zIndex: 1,
            autoplay: true,
            dots: false,
            autoplaySpeed: 5000,
        });

        const videoContainers = document.querySelectorAll(".youtube-video-container");

        videoContainers.forEach((container) => {
            const videoSrc = container.getAttribute("data-video-src");
            const posterWrapper = container.querySelector(".poster-wrapper");
            const iframeContainer = container.querySelector(".iframe-container");

            // Make the entire poster wrapper clickable
            posterWrapper.addEventListener("click", function () {
                // Hide poster
                posterWrapper.style.display = "none";

                // Show and populate iframe
                iframeContainer.classList.remove("hidden");
                iframeContainer.classList.add("active");

                // Create and add iframe
                const iframe = document.createElement("iframe");
                iframe.src = videoSrc;
                iframe.className = "absolute inset-0 w-full h-full";
                iframe.setAttribute("frameborder", "0");
                iframe.setAttribute("allowfullscreen", "");
                iframe.setAttribute(
                    "allow",
                    "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",
                );

                iframeContainer.appendChild(iframe);
            });
        });
    });
</script>

<script>
    AOS.init({
        once: true,
        duration: 650,
    });

    $(window).on("load", function () {
        $("[data-src]").each(function (ifram) {
            $(this).attr("src", $(this).data("src"));
        });

        $("[data-x-aos]").each(function name(i, element) {
            $(element).attr("data-aos", $(element).data("x-aos"));
        });

        AOS.refreshHard();

        // Mobile menu toggle functionality
        const menuToggle = document.querySelector(".menu-toggle");
        const mobileMenu = document.querySelector("#mobile-menu");

        if (menuToggle && mobileMenu) {
            menuToggle.addEventListener("click", function () {
                mobileMenu.classList.toggle("hidden");
            });
        }
    });
</script>
@stack('js')

<!-- Custom Scripts (Footer) -->
{{--{!! get_settings('custom_script_footer') !!}--}}
</body>

</html>

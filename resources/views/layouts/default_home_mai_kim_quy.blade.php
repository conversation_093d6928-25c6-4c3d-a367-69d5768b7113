@php $current_route_name = Route::currentRouteName(); @endphp
@php

    if (session('home')) {
        $home_page = App\Models\Builder_page::where('id', session('home'))->firstOrNew();
    } else {
        $home_page = App\Models\Builder_page::where('status', 1)->firstOrNew();
    }

    if(!empty($course_details->builder_ids)){
        $page_id = $course_details->builder_ids;
    }else{
        $page_id = $home_page->id;
    }
    $page = App\Models\Builder_page::find($page_id);
    $disable_bootstrap = false;
    if(!empty($page->disable_bootstrap)){
        $disable_bootstrap = $page->disable_bootstrap;
    }

@endphp
    <!DOCTYPE html>
<html lang="en">
<head>
@include('layouts.seo')
@stack('meta')

<!-- fav icon -->
    <link rel="shortcut icon" href="{{ asset(get_frontend_settings('favicon')) }}"/>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <script src="https://cdn.tailwindcss.com"></script>
    <link
        href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
        rel="stylesheet"
    />
    <link rel="stylesheet" href="{{ asset('assets/global/global.css') }}"/>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Exo:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet"
    />
    <link rel="stylesheet" href="{{ asset('assets/frontend/default/css/modal-auth.css') }}">
    <link
        rel="stylesheet"
        href="{{ asset('assets/frontend/default/libs/aos/aos-*******-beta.6.css') }}"
    />
    <link
        rel="stylesheet"
        href="{{ asset('assets/frontend/default/vendors/slick/slick.css') }}"
    />
    <link
        rel="stylesheet"
        href="{{ asset('assets/frontend/default/vendors/slick/slick-theme.css') }}"
    />
    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"/>
    <!-- Jquery Ui Js -->
    <script src="{{ asset('assets/frontend/default/js/jquery-3.7.1.min.js') }}"></script>
    <script src="{{ asset('assets/frontend/default/js/jquery-ui.min.js') }}"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    spacing: {
                        15: "60px",
                        18: "72px",
                        22: "88px",
                    },
                },
            },
        };
    </script>

    <link rel="stylesheet" href="{{ asset('assets/frontend/maikimquy/assets/css/styles.css') }}" />
    @stack('css')

    @if (get_frontend_settings('recaptcha_status'))
        <script
            src="https://www.google.com/recaptcha/api.js?render={{ get_frontend_settings('recaptcha_sitekey') }}"></script>
    @endif
<!-- Custom Scripts (Head) -->
    {!! get_settings('custom_script_head') !!}
</head>

<body>
<!-- Custom Scripts (Body Start) -->
{!! get_settings('custom_script_body_start') !!}
@yield('content')

@if(!$disable_bootstrap)
    <!-- Bootstrap Js -->
    {{--    <script src="{{ asset('assets/frontend/default/js/bootstrap.bundle.min.js') }}"></script>--}}
@endif


<!-- Main Js -->
<script src="{{ asset('assets/frontend/home/<USER>/script/script.js') }}"></script>

<script src="{{ asset('assets/frontend/mst-academy/assets/libs/aos/aos-*******-beta.6.js') }}"></script>
<script
    type="text/javascript"
    src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"
></script>


<script>
    // Generic function to initialize all YouTube players
    document.addEventListener("DOMContentLoaded", function () {
        const videoContainers = document.querySelectorAll(".youtube-video-container");

        videoContainers.forEach((container) => {
            const videoSrc = container.getAttribute("data-video-src");
            const posterWrapper = container.querySelector(".poster-wrapper");
            const iframeContainer = container.querySelector(".iframe-container");

            // Make the entire poster wrapper clickable
            posterWrapper.addEventListener("click", function () {
                // Hide poster
                posterWrapper.style.display = "none";

                // Show and populate iframe
                iframeContainer.classList.remove("hidden");
                iframeContainer.classList.add("active");

                // Create and add iframe
                const iframe = document.createElement("iframe");
                iframe.src = videoSrc;
                iframe.className = "absolute inset-0 w-full h-full";
                iframe.setAttribute("frameborder", "0");
                iframe.setAttribute("allowfullscreen", "");
                iframe.setAttribute(
                    "allow",
                    "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",
                );

                iframeContainer.appendChild(iframe);
            });
        });
    });
</script>

<script>
    function toggleFAQ(index) {
        const faqItems = document.querySelectorAll(".faq-item");
        const currentItem = faqItems[index - 1];
        const answer = currentItem.querySelector(".faq-answer");
        const icon = currentItem.querySelector(".faq-icon");

        // Close all other FAQ items
        faqItems.forEach((item, i) => {
            if (i !== index - 1) {
                item.classList.remove("active"); // Add background colo
                const otherAnswer = item.querySelector(".faq-answer");
                const otherIcon = item.querySelector(".faq-icon");
                otherAnswer.classList.add("hidden");
                otherIcon.classList.remove("rotate-180");
            }
        });

        // Toggle current FAQ item
        if (answer.classList.contains("hidden")) {
            currentItem.classList.add("active"); // Add background colo
            answer.classList.remove("hidden");
            icon.classList.add("rotate-180");
        } else {
            answer.classList.add("hidden");
            icon.classList.remove("rotate-180");
            currentItem.classList.remove("active"); // Add background colo
        }
    }
</script>
<script>
    AOS.init({
        duration: 650,
        once: true,
    });

    document.addEventListener("DOMContentLoaded", function () {
        // Get the header element
        const header = document.querySelector(".header");
        let headerHeight = header.offsetHeight; // Get the header height
        let ticking = false; // For scroll performance

        // Function to handle scroll events
        function handleScroll() {
            const currentScrollTop = window.scrollY;

            // Add or remove sticky class based on scroll position
            if (currentScrollTop > 50) {
                // If scrolling down past threshold and header is not already sticky
                if (!header.classList.contains("sticky-header")) {
                    // Remove returning class if it exists
                    header.classList.remove("returning");
                    // Add sticky class
                    header.classList.add("sticky-header");
                }
            } else {
                // If at top or scrolling back up to top
                if (header.classList.contains("sticky-header")) {
                    // Add returning class for animation
                    header.classList.add("returning");

                    // Wait for animation to complete before removing sticky
                    setTimeout(() => {
                        header.classList.remove("sticky-header");
                        header.classList.remove("returning");
                    }, 100); // Match this with the animation duration
                }
            }

            // Keep the scrolled class for visual changes (if needed)
            if (currentScrollTop > 50) {
                header.classList.add("scrolled");
            } else {
                header.classList.remove("scrolled");
            }

            ticking = false;
        }

        // Add scroll event listener with requestAnimationFrame for performance
        window.addEventListener("scroll", function () {
            if (!ticking) {
                window.requestAnimationFrame(function () {
                    handleScroll();
                });
                ticking = true;
            }
        });

        // Initial check
        handleScroll();

        // Recalculate header height on window resize
        window.addEventListener("resize", function () {
            headerHeight = header.offsetHeight;
            handleScroll();
        });

        // Mobile menu toggle functionality
        const menuToggle = document.querySelector(".menu-toggle");
        const mobileMenu = document.querySelector("#mobile-menu");

        if (menuToggle && mobileMenu) {
            menuToggle.addEventListener("click", function () {
                mobileMenu.classList.toggle("hidden");
                header.classList.toggle("active");
            });
        }
    });

    $(window).on("load", function () {
        $(".feedback .slider-container").slick({
            zIndex: 1,
            autoplay: true,
            arrows: false,
            dots: true,
            slidesToShow: 3,
            slidesToScroll: 3,
            autoplaySpeed: 5000,

            responsive: [
                {
                    breakpoint: 1280,
                    settings: {
                        slidesToShow: 2,
                    },
                },

                {
                    breakpoint: 480,
                    settings: {
                        centerMode: true,
                        centerPadding: "0",
                        slidesToShow: 1,
                    },
                },
            ],
        });

        $(".news .slider-container").slick({
            arrows: false,
            zIndex: 1,
            autoplay: true,
            dots: true,
            slidesToShow: 3,
            slidesToScroll: 3,
            autoplaySpeed: 5000,
            responsive: [
                {
                    breakpoint: 1280,
                    settings: {
                        slidesToShow: 2,
                    },
                },

                {
                    breakpoint: 480,
                    settings: {
                        centerMode: true,
                        centerPadding: "0",
                        slidesToShow: 1,
                    },
                },
            ],
        });

        $("[data-x-aos]").each(function name(i, element) {
            $(element).attr("data-aos", $(element).data("x-aos"));
        });

        AOS.refreshHard();
    });
</script>
@stack('js')

</body>

</html>

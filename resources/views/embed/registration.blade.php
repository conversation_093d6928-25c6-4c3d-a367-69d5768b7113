<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title><PERSON><PERSON><PERSON> ký khóa học</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    @if (get_frontend_settings('recaptcha_status'))
    <script src="https://www.google.com/recaptcha/api.js?render={{ get_frontend_settings('recaptcha_sitekey') }}"></script>
    @endif
    <style>
        .grecaptcha-badge { 
    visibility: hidden !important;
}
        :root {
            --primary-color: #3B82F6;
            --primary-dark: #2563EB;
            --primary-light: #EFF6FF;
            --accent-color: #F59E0B;
            --accent-dark: #D97706;
            --success-color: #10B981;
            --error-color: #EF4444;
            --text-dark: #1F2937;
            --text-light: #6B7280;
            --border-color: #E5E7EB;
            --bg-color: #F9FAFB;
        }

        body {
            font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: transparent;
            color: var(--text-dark);
        }

        .form-container {
            max-width: 500px;
            margin: 0 auto;
        }

        .form-section {
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color);
            background: white;
        }

        .form-header {
            background: linear-gradient(135deg, var(--primary-light), white);
            border-bottom: 2px solid var(--primary-color);
            position: relative;
            overflow: hidden;
        }

        .form-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
            opacity: 0.1;
            border-radius: 50%;
            transform: translate(30%, -30%);
        }

        .form-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 20px;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, var(--accent-color) 0%, transparent 70%);
            opacity: 0.1;
            border-radius: 50%;
            transform: translate(-30%, 30%);
        }

        .form-content {
            background-color: rgba(255, 249, 235, 0.2);
            border: 1px solid rgba(251, 191, 36, 0.3);
            border-radius: 0.75rem;
        }

        .input-group {
            position: relative;
            margin-bottom: 1.25rem;
        }

        .input-label {
            position: absolute;
            top: 0;
            left: 0.75rem;
            transform: translateY(-50%);
            background-color: white;
            padding: 0 0.5rem;
            font-size: 0.875rem;
            color: var(--text-light);
            transition: all 0.2s;
            pointer-events: none;
        }

        .input-field {
            display: block;
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            background-color: white;
            color: var(--text-dark);
            font-size: 1rem;
            transition: all 0.2s;
        }

        .input-field:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .input-field.error {
            border-color: var(--error-color);
        }

        .input-field.error:focus {
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .error-feedback {
            color: var(--error-color);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .tel-group {
            display: flex;
        }

        .tel-prefix {
            display: flex;
            align-items: center;
            padding: 0 0.75rem;
            background-color: #F3F4F6;
            border: 1px solid var(--border-color);
            border-right: none;
            border-top-left-radius: 0.5rem;
            border-bottom-left-radius: 0.5rem;
            color: var(--text-light);
        }

        .tel-input {
            flex: 1;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .toggle-password {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--text-light);
            transition: color 0.2s;
        }

        .toggle-password:hover {
            color: var(--primary-color);
        }

        .submit-button {
            background: linear-gradient(135deg, var(--accent-color), var(--accent-dark));
            color: white;
            border: none;
            border-radius: 0.5rem;
            padding: 1rem 2rem;
            font-weight: 600;
            font-size: 1.125rem;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .submit-button::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: -100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: 0.5s;
        }

        .submit-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .submit-button:hover::after {
            left: 100%;
        }

        .loading-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2.5rem 0;
        }

        .spinner {
            width: 4rem;
            height: 4rem;
            border: 4px solid rgba(251, 191, 36, 0.2);
            border-radius: 50%;
            border-left-color: var(--accent-color);
            border-top-color: var(--accent-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        .success-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2.5rem 0;
        }

        .success-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 5rem;
            height: 5rem;
            background-color: rgba(16, 185, 129, 0.1);
            border-radius: 50%;
            margin-bottom: 1rem;
            position: relative;
        }

        .success-icon svg {
            width: 2.5rem;
            height: 2.5rem;
            color: var(--success-color);
        }

        .success-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--success-color);
            margin-bottom: 0.5rem;
        }

        .success-message {
            color: var(--text-light);
            margin-bottom: 1rem;
        }

        .error-alert {
            background-color: #FEF2F2;
            border-left: 4px solid var(--error-color);
            color: var(--error-color);
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .retry-button {
            background-color: white;
            color: var(--error-color);
            border: 1px solid var(--error-color);
            border-radius: 0.375rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .retry-button:hover {
            background-color: #FEF2F2;
        }
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body class="bg-transparent">
    <div class="form-container p-4">
        <div class="form-section rounded-xl overflow-hidden">
            <div class="form-header text-center p-6">
                <h2 class="text-lg lg:text-2xl font-bold text-blue-900">
                    {{ $main_title }}
                    <span class="text-blue-600">{{ $highlight_title }}</span>
                </h2>
                <p class="text-gray-700 mt-1">{{ $subtitle }}</p>
            </div>

            <div class="p-6">
                <div class="form-content p-5">
                    <form id="registration-form">
                        <input type="hidden" name="course_slug" value="{{ $course_slug }}">
                        <input type="hidden" name="redirect_url" value="{{ $redirect_url }}">
                        <input type="hidden" name="utm_source" id="utm_source" value="">

                        <div id="form-content">
                            <div class="input-group">
                                <input type="text" name="name" class="input-field" required id="name-field">
                                <label for="name-field" class="input-label">Họ và tên <span class="text-red-500">*</span></label>
                                <div class="error-feedback hidden"></div>
                            </div>

                            <div class="input-group">
                                <input type="email" name="email" class="input-field" required id="email-field">
                                <label for="email-field" class="input-label">Email <span class="text-red-500">*</span></label>
                                <div class="error-feedback hidden"></div>
                            </div>

                            <div class="input-group">
                                <input type="password" name="password" class="input-field" required id="password-field">
                                <label for="password-field" class="input-label">Mật khẩu <span class="text-red-500">*</span></label>
                                <div class="toggle-password">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </div>
                                <div class="error-feedback hidden"></div>
                            </div>

                            <div class="input-group">
                                <label class="block text-sm text-gray-700 mb-1">Số điện thoại <span class="text-red-500">*</span></label>
                                <div class="tel-group">
                                    <div class="tel-prefix">
                                        <img src="https://upload.wikimedia.org/wikipedia/commons/2/21/Flag_of_Vietnam.svg" alt="Vietnam flag" class="h-4 w-6 mr-1" />
                                        +84
                                    </div>
                                    <input type="tel" name="phone" class="input-field tel-input" required id="phone-field">
                                </div>
                                <div class="error-feedback hidden"></div>
                            </div>

                            <button type="submit" id="submit-btn" class="submit-button mt-6">
                                <span>{{ $button_text }}</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>

                        <div id="loading" class="loading-indicator hidden">
                            <div class="spinner"></div>
                            <p class="mt-4 text-gray-700 font-medium">Đang xử lý...</p>
                        </div>

                        <div id="success-message" class="success-indicator hidden">
                            <div class="success-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <h3 class="success-title">Đăng ký thành công!</h3>
                            <p class="success-message">Đang chuyển hướng đến khóa học...</p>
                        </div>

                        <div id="error-message" class="hidden">
                            <div class="error-alert" role="alert">
                                <div class="flex items-center mb-2">
                                    <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                    <strong class="font-bold">Lỗi đăng ký!</strong>
                                </div>
                                <p class="error-text mb-3">Đã có lỗi xảy ra. Vui lòng thử lại sau.</p>
                                <button type="button" class="retry-button" onclick="hideError()">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline-block mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                    </svg>
                                    Thử lại
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Extract UTM parameters from URL on page load
            function getUTMParameters() {
                let params = {};
                let searchParams = new URLSearchParams(window.location.search);
                
                // List of UTM parameters to extract
                const utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term'];
                
                // Extract each parameter if it exists
                utmParams.forEach(param => {
                    if (searchParams.has(param)) {
                        params[param] = searchParams.get(param);
                    }
                });
                
                return params;
            }
            
            // Set UTM values to hidden fields
            const utmParams = getUTMParameters();
            if (utmParams.utm_source) {
                console.log('UTM Source found:', utmParams.utm_source);
                $('#utm_source').val(utmParams.utm_source);
            }
            
            // Focus event for input fields
            $('.input-field').on('focus', function() {
                $(this).parent().find('.input-label').addClass('text-blue-600');
            });

            $('.input-field').on('blur', function() {
                $(this).parent().find('.input-label').removeClass('text-blue-600');
            });

            // Toggle password visibility
            $('.toggle-password').click(function() {
                var passwordInput = $(this).parent().find('input');
                var type = passwordInput.attr('type') === 'password' ? 'text' : 'password';
                passwordInput.attr('type', type);

                // Change icon based on password visibility
                if (type === 'text') {
                    $(this).html('<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" /></svg>');
                } else {
                    $(this).html('<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>');
                }
            });

            // Validate form on submit
            $('#registration-form').submit(function(e) {
                e.preventDefault();

                // Reset previous errors
                $('.error-feedback').addClass('hidden').html('');
                $('.input-field').removeClass('error');

                // Validate form fields
                let isValid = true;

                // Validate name
                const name = $('input[name="name"]').val().trim();
                if (!name) {
                    showFieldError('name', 'Vui lòng nhập họ và tên');
                    isValid = false;
                }

                // Validate email
                const email = $('input[name="email"]').val().trim();
                if (!email) {
                    showFieldError('email', 'Vui lòng nhập địa chỉ email');
                    isValid = false;
                } else if (!isValidEmail(email)) {
                    showFieldError('email', 'Email không hợp lệ');
                    isValid = false;
                }

                // Validate password
                const password = $('input[name="password"]').val();
                if (!password) {
                    showFieldError('password', 'Vui lòng nhập mật khẩu');
                    isValid = false;
                } else if (password.length < 6) {
                    showFieldError('password', 'Mật khẩu phải có ít nhất 6 ký tự');
                    isValid = false;
                }

                // Validate phone
                const phone = $('input[name="phone"]').val().trim();
                if (!phone) {
                    showFieldError('phone', 'Vui lòng nhập số điện thoại');
                    isValid = false;
                } else if (!isValidPhone(phone)) {
                    showFieldError('phone', 'Số điện thoại không hợp lệ');
                    isValid = false;
                }

                if (!isValid) {
                    return false;
                }

                // Show loading
                $('#form-content').addClass('hidden');
                $('#loading').removeClass('hidden');
                $('#error-message').addClass('hidden');

                // Get form data
                var formData = {
                    name: name,
                    email: email,
                    password: password,
                    phone: phone,
                    course_slug: $('input[name="course_slug"]').val(),
                    redirect_url: $('input[name="redirect_url"]').val(),
                    _token: $('meta[name="csrf-token"]').attr('content')
                };

                // Add UTM source from hidden field
                const utmSource = $('#utm_source').val();
                if (utmSource) {
                    console.log('Adding UTM source to form data:', utmSource);
                    formData.utm_source = utmSource;
                }

                @if (get_frontend_settings('recaptcha_status'))
                // Execute reCAPTCHA v3
                grecaptcha.ready(function () {
                    grecaptcha.execute('{{ get_frontend_settings('recaptcha_sitekey') }}', {action: 'submit'}).then(function (token) {
                        // Add token to form data
                        formData['g-recaptcha-response'] = token;

                        // Submit the form with the token
                        submitFormWithRecaptcha(formData);
                    }).catch(function (error) {
                        console.error('reCAPTCHA error:', error);
                        $('#loading').addClass('hidden');
                        $('#form-content').removeClass('hidden');
                        $('#error-message').removeClass('hidden');
                        $('.error-text').text('Lỗi xác thực captcha. Vui lòng thử lại.');
                    });
                });
                @else
                // Submit without reCAPTCHA
                submitFormWithRecaptcha(formData);
                @endif
            });

            // Helper functions
            function showFieldError(fieldName, message) {
                const field = $(`input[name="${fieldName}"]`);
                field.addClass('error');

                // Find the error container and show message
                let errorContainer;
                if (fieldName === 'phone') {
                    errorContainer = field.closest('.input-group').find('.error-feedback');
                } else {
                    errorContainer = field.siblings('.error-feedback');
                }

                errorContainer.html(message).removeClass('hidden');
            }

            function isValidEmail(email) {
                const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                return re.test(String(email).toLowerCase());
            }

            function isValidPhone(phone) {
                // Accept numbers only, minimum 9 digits
                const re = /^[0-9]{9,}$/;
                return re.test(String(phone));
            }
            
            function submitFormWithRecaptcha(formData) {
                // Log form data for debugging
                console.log('Submitting form with data:', formData);
                
                // Send AJAX request
                $.ajax({
                    url: '/embed/register/' + formData.course_slug,
                    type: 'POST',
                    data: formData,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    beforeSend: function (xhr) {
                        xhr.setRequestHeader('Accept', 'application/json');
                        console.log('Sending registration request with UTM source:', formData.utm_source);
                    },
                    success: function(response) {
                        $('#loading').addClass('hidden');
                        console.log('Registration response:', response);

                        if (response.success) {
                            $('#success-message').removeClass('hidden');

                            // Redirect after a short delay
                            setTimeout(function() {
                                // If we're in an iframe, use postMessage to communicate with parent
                                if (window.parent !== window) {
                                    window.parent.postMessage({
                                        type: 'registration_success',
                                        redirectUrl: response.redirect_url
                                    }, '*');
                                } else {
                                    // Direct redirect if not in iframe
                                    window.location.href = response.redirect_url;
                                }
                            }, 1500);
                        } else {
                            // Show error
                            console.error('Registration error:', response.message);
                            $('.error-text').text(response.message);
                            $('#error-message').removeClass('hidden');
                            $('#form-content').removeClass('hidden');

                            // Notify parent window of error if in iframe
                            if (window.parent !== window) {
                                window.parent.postMessage({
                                    type: 'registration_error',
                                    message: response.message
                                }, '*');
                            }
                        }
                    },
                    error: function(xhr) {
                        $('#loading').addClass('hidden');

                        // Show error message
                        var errorMessage = 'Đã có lỗi xảy ra. Vui lòng thử lại sau.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        $('.error-text').text(errorMessage);
                        $('#error-message').removeClass('hidden');
                        $('#form-content').removeClass('hidden');

                        // Notify parent window of error if in iframe
                        if (window.parent !== window) {
                            window.parent.postMessage({
                                type: 'registration_error',
                                message: errorMessage
                            }, '*');
                        }
                    }
                });
            }
        });

        function hideError() {
            $('#error-message').addClass('hidden');
            $('#form-content').removeClass('hidden');
        }
    </script>
</body>
</html>

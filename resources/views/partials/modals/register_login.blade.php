
@if(!Auth()->check())
    <div class="modal fade" id="modal-auth" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true"
         data-bs-backdrop="static">
        <div class="modal-dialog modal-dialog-centered modal-auth">
            <div class="modal-content">
                <!-- Header -->
                <div class="modal-header">
                    <div class="w-100 text-center">
                        <h5 class="modal-title">[ ĐĂNG KÝ NGAY - MIỄN PHÍ 100% ]</h5>
                        <div class="d-flex align-items-center gap-1 justify-content-center">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/images/target-icon.png') }}"/>
                            <span class="text-white">Chỉ 1 bước - Vào học ngay, không cần thêm thông tin</span>
                        </div>
                    </div>
                    <button type="button" class="close-btn" data-bs-dismiss="modal" aria-label="Close">
                        <i class="bi bi-x-circle"></i>
                    </button>
                </div>

                <!-- Body -->
                <div class="modal-body p-0">

                    @if (get_frontend_settings('google_status'))
                    <!-- Google Login Button -->
                        <a class="google-btn" href="{{route('auth.google.redirect')}}">
                            <svg width="20" height="20" viewBox="0 0 533.5 544.3" xmlns="http://www.w3.org/2000/svg"
                                 class="me-2">
                                <path
                                    d="M533.5 278.4c0-18.5-1.5-37.1-4.7-55.3H272.1v104.8h147c-6.1 33.8-25.7 63.7-54.4 82.7v68h87.7c51.5-47.4 81.1-117.4 81.1-200.2z"
                                    fill="#4285f4"/>
                                <path
                                    d="M272.1 544.3c73.4 0 135.3-24.1 180.4-65.7l-87.7-68c-24.4 16.6-55.9 26-92.6 26-71 0-131.2-47.9-152.8-112.3H28.9v70.1c46.2 91.9 140.3 149.9 243.2 149.9z"
                                    fill="#34a853"/>
                                <path
                                    d="M119.3 324.3c-11.4-33.8-11.4-70.4 0-104.2V150H28.9c-38.6 76.9-38.6 167.5 0 244.4l90.4-70.1z"
                                    fill="#fbbc04"/>
                                <path
                                    d="M272.1 107.7c38.8-.6 76.3 14 104.4 40.8l77.7-77.7C405 24.6 340.1 0 272.1 0 169.2 0 75.1 58 28.9 150l90.4 70.1c21.5-64.5 81.8-112.4 152.8-112.4z"
                                    fill="#ea4335"/>
                            </svg>
                            Đăng nhập với Google
                        </a>
                    @endif
                    <div class="form-login">
                        <ul class="nav nav-tabs tab-switch-auth" id="authTabs" role="tablist">

                            <li class="nav-item tab" role="presentation">
                                <div class="nav-link active" id="login-tab" data-bs-toggle="tab"
                                     data-bs-target="#login-tab-pane" type="button" role="tab"
                                     aria-controls="login-tab-pane"
                                     aria-selected="true">
                                    <img
                                        src="{{ asset('assets/frontend/course-shopee/assets/images/tab-active-left.png') }}"/>

                                    <span class="position-relative">ĐĂNG NHẬP</span>
                                </div>
                            </li>
                            <li class="nav-item tab" role="presentation">
                                <div class="nav-link" id="register-tab" data-bs-toggle="tab"
                                     data-bs-target="#register-tab-pane" type="button" role="tab"
                                     aria-controls="register-tab-pane" aria-selected="false">
                                    <img
                                        src="{{ asset('assets/frontend/course-shopee/assets/images/tab-active-right.png') }}"/>
                                    <span class="position-relative">ĐĂNG KÝ</span>
                                </div>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="authTabsContent">

                            <!-- Login Tab -->
                            <div class="tab-pane fade  show active" id="login-tab-pane" role="tabpanel"
                                 aria-labelledby="login-tab" tabindex="0">
                                <p class="mb-4">
                                    Chào mừng bạn đã quay lại.
                                    <span class="red-text">Đăng nhập để bắt đầu học ngay!</span>
                                </p>
                                <form method="POST" id="login-form">
                                    @csrf
                                    <div class="mb-4 form-wrap">
                                        <label for="login-email" class="form-label">Email:</label>
                                        <input type="email" name="email" value="{{ old('email') }}" class="form-control"
                                               id="login-email"/>

                                    </div>

                                    <div class="mb-4 form-wrap">
                                        <label for="login-password" class="form-label">Mật khẩu:</label>
                                        <div class="password-field">
                                            <input type="password" name="password" class="form-control"
                                                   id="login-password"/>
                                            <span class="password-toggle">
                                                <i class="bi bi-eye-slash"></i>
                                            </span>
                                        </div>

                                    </div>

                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <div>
                                                <input type="checkbox" name="remember_me" id="login-rememberMe"/>
                                                <label for="login-rememberMe">Ghi nhớ tài khoản</label>
                                            </div>

                                        </div>
                                        <div>
                                            <a href="{{route('password.request')}}" class="red-text">Quên mật khẩu?</a>
                                        </div>
                                    </div>


                                    <button
                                        class="signup-btn d-flex align-items-center py-3 g-recaptcha g-recaptcha-login"
                                        id="login-btn" type="button"
                                        data-sitekey="{{ get_frontend_settings('recaptcha_sitekey') }}"
                                        data-callback="onLoginSubmit" data-action="submit">

                                        <img
                                            src="{{ asset('assets/frontend/course-shopee/assets/images/free-tag.png') }}"
                                            style="width: 56px; height: 56px"/>
                                        <div class="btn-text">
                                            <div class="btn-title">ĐĂNG NHẬP HỌC NGAY</div>
                                            <div class="btn-subtitle">
                                                Hoàn toàn MIỄN PHÍ | Hiệu quả cao
                                            </div>
                                        </div>
                                    </button>
                                </form>
                            </div>
                            <!-- Register Tab -->
                            <div class="tab-pane fade" id="register-tab-pane" role="tabpanel"
                                 aria-labelledby="register-tab"
                                 tabindex="0">
                                <form method="POST" id="register-form">
                                @csrf
                                <!-- Input course -->
                                    <p class="mb-4">
                                        Không có thêm bước nào cả.
                                        <span class="red-text">Đăng ký là xem được ngay!</span>
                                    </p>
                                    <div class="mb-4 form-wrap">
                                        <label for="register-email" class="form-label">Email:</label>
                                        <input type="email" name="email" value="{{ old('email') }}" class="form-control"
                                               id="register-email"/>

                                    </div>

                                    <div class="d-flex gap-2 col-password">

                                        <div class="mb-4 form-wrap w-50">
                                            <label for="register-password" class="form-label">Mật khẩu:</label>
                                            <div class="password-field">
                                                <input type="password" name="password" value="{{ old('password') }}"
                                                       class="form-control" id="register-password"/>
                                                <span class="password-toggle">
                                                    <i class="bi bi-eye-slash"></i>
                                                </span>
                                            </div>
                                        </div>

                                        <div class="mb-4 form-wrap w-50">
                                            <label for="register-confirm_password" class="form-label">Xác nhận mật
                                                khẩu:</label>
                                            <div class="password-field">
                                                <input type="password" name="confirm_password"
                                                       value="{{ old('confirm_password') }}" class="form-control"
                                                       id="register-confirm_password"/>
                                                <span class="password-toggle">
                                                    <i class="bi bi-eye-slash"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>


                                    <button
                                        class="signup-btn d-flex align-items-center py-3 g-recaptcha g-recaptcha-register"
                                        id="register-btn"
                                        data-sitekey="{{ get_frontend_settings('recaptcha_sitekey') }}"
                                        data-callback="onRegisterSubmit" data-action="submit" type="button">
                                        <img
                                            src="{{ asset('assets/frontend/course-shopee/assets/images/free-tag.png') }}"
                                            style="width: 56px; height: 56px"/>
                                        <div class="btn-text">
                                            <div class="btn-title">ĐĂNG KÝ HỌC THỬ NGAY</div>
                                            <div class="btn-subtitle">
                                                Hoàn toàn MIỄN PHÍ | Hiệu quả cao
                                            </div>
                                        </div>
                                    </button>
                                </form>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>

        function onLoginSubmit(token) {
            if (!token) {
                showMessage('Vui lòng xác thực captcha', true);
                return false;
            }
            // Bổ xung loading khi click vào button đăng nhập
            $('#login-btn').html(`<span class="spinner-border m-3 p-3" role="status" aria-hidden="true"></span><span>Đang đăng nhập ...</span>`);

            removeInvalid()
            let formData = $('#login-form').serialize();

            $.post('{{ route("login.course") }}', formData)
                .done(function (response) {
                    if (!response.error) {
                        handleSuccessfulAuth(response);
                    } else {
                        // showMessage(response.message, true);
                    }
                    $("#login-btn").html(`<img src="{{ asset('assets/frontend/course-shopee/assets/images/free-tag.png') }}"
                                                    style="width: 56px; height: 56px" />
                                                <div class="btn-text">
                                                    <div class="btn-title">ĐĂNG NHẬP HỌC NGAY</div>
                                                    <div class="btn-subtitle">
                                                        Hoàn toàn MIỄN PHÍ | Hiệu quả cao
                                                    </div>
                                                </div`);
                }).fail(function (xhr) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response && response.errors) {
                        if (response.errors.email) {
                            $('#login-email').addClass('is-invalid');
                            $('#login-email').after(`<div class="invalid-feedback">${response.errors.email[0]}</div>`);
                        }
                        if (response.errors.password) {
                            $('#login-password').addClass('is-invalid');
                            $('#login-password').after(`<div class="invalid-feedback">${response.errors.password[0]}</div>`);
                        }
                        if (response.errors.captcha) {
                            $('.g-recaptcha-login').addClass('is-invalid');
                            $('.g-recaptcha-login').after(`<div class="invalid-feedback">${response.errors.captcha[0]}</div>`);
                        }
                    }
                    if (response && response.error && response.message) {
                        // Hiển thị lỗi chung nếu có
                        if (typeof response.message === 'string') {
                            showMessage(response.message, true);
                        }

                        // Hiển thị lỗi vào form theo từng input tương ứng
                        if (response.message.email) {
                            $('#login-email').addClass('is-invalid');
                            $('#login-email').after(`<div class="invalid-feedback">${response.errors.email[0]}</div>`);
                        }
                        if (response.message.password) {
                            $('#login-password').addClass('is-invalid');
                            $('#login-password').after(`<div class="invalid-feedback">${response.errors.password[0]}</div>`);
                        }

                        if (response.message.captcha) {
                            $('.g-recaptcha-login').addClass('is-invalid');
                            $('.g-recaptcha-login').after(`<div class="invalid-feedback">${response.message.captcha}</div>`);
                        }
                    }
                } catch (e) {
                    // Xử lý lỗi CSRF token hết hạn
                    if (xhr.status === 419) {
                        showMessage('Phiên làm việc đã hết hạn. Vui lòng làm mới trang và thử lại.', true);
                        // Tự động làm mới trang sau 3 giây
                        setTimeout(function () {
                            window.location.reload();
                        }, 3000);
                    } else {
                        showMessage('Đã xảy ra lỗi. Vui lòng thử lại sau.', true);
                    }
                }
                // quay về nút ban đầu

                $("#login-btn").html(`<img src="{{ asset('assets/frontend/course-shopee/assets/images/free-tag.png') }}"
                                                    style="width: 56px; height: 56px" />
                                                <div class="btn-text">
                                                    <div class="btn-title">ĐĂNG NHẬP HỌC NGAY</div>
                                                    <div class="btn-subtitle">
                                                        Hoàn toàn MIỄN PHÍ | Hiệu quả cao
                                                    </div>
                                                </div`);
            });
        }

        function onRegisterSubmit(token) {

            if (!token) {
                showMessage('Vui lòng xác thực captcha', true);
                return false;
            }
            removeInvalid()

            // Bổ xung loading khi click vào button đăng nhập
            $('#register-btn').html(`<span class="spinner-border m-3 p-3" role="status" aria-hidden="true"></span><span>Đang đăng ký ...</span>`);
            let formData = $('#register-form').serialize();
            $.post('{{ route("register.incourse") }}', formData)
                .done(function (response) {
                    if (!response.error) {
                        handleSuccessfulAuth(response);
                    } else {
                        showMessage(response.message || 'Có lỗi xảy ra', true);
                    }
                    $('#register-btn').html(`<img src="{{ asset('assets/frontend/course-shopee/assets/images/free-tag.png') }}"
                                                style="width: 56px; height: 56px" />
                                            <div class="btn-text">
                                                <div class="btn-title">ĐĂNG KÝ HỌC THỬ NGAY</div>
                                                <div class="btn-subtitle">
                                                    Hoàn toàn MIỄN PHÍ | Hiệu quả cao
                                                </div>
                                            </div>`);
                }).fail(function (xhr) {
                const response = JSON.parse(xhr.responseText);
                try {
                    if (response.error) {
                        // Hiển thị lỗi vào form theo từng input tương ứng
                        if (response.message.email) {
                            $('#register-email').addClass('is-invalid');
                            $('#register-email').after(`<div class="invalid-feedback">${response.message.email}</div>`);
                        }
                        if (response.message.password) {
                            $('#register-password').addClass('is-invalid');
                            $('#register-password').after(`<div class="invalid-feedback">${response.message.password}</div>`);
                        }
                        if (response.message.confirm_password) {
                            $('#register-confirm_password').addClass('is-invalid');
                            $('#register-confirm_password').after(`<div class="invalid-feedback">${response.message.confirm_password}</div>`);
                        }

                        if (response.message.captcha) {
                            $('.g-recaptcha-register').addClass('is-invalid');
                            $('.g-recaptcha-register').after(`<div class="invalid-feedback">${response.message.captcha}</div>`);
                        }
                    }
                } catch (e) {
                    // Xử lý lỗi CSRF token hết hạn
                    if (xhr.status === 419) {
                        showMessage('Phiên làm việc đã hết hạn. Vui lòng làm mới trang và thử lại.', true);
                        // Tự động làm mới trang sau 3 giây
                        setTimeout(function () {
                            window.location.reload();
                        }, 3000);
                    } else {
                        showMessage('Đã xảy ra lỗi. Vui lòng thử lại sau.', true);
                    }
                }
                $('#register-btn').html(`<img src="{{ asset('assets/frontend/course-shopee/assets/images/free-tag.png') }}"
                                                style="width: 56px; height: 56px" />
                                            <div class="btn-text">
                                                <div class="btn-title">ĐĂNG KÝ HỌC THỬ NGAY</div>
                                                <div class="btn-subtitle">
                                                    Hoàn toàn MIỄN PHÍ | Hiệu quả cao
                                                </div>
                                            </div>`);
            });
        }

        // Thêm hàm xóa trạng thái invalid
        function removeInvalid() {
            $('.invalid-feedback').remove();
            $('.is-invalid').removeClass('is-invalid');
        }

        // Hàm hiển thị thông báo
        function showMessage(message, isError = true) {
            if (typeof toastr !== 'undefined') {
                if (isError) {
                    toastr.error(message);
                } else {
                    toastr.success(message);
                }
            } else {
                alert(message);
            }
        }

        // Hàm xử lý đóng modal và cập nhật giao diện
        function handleSuccessfulAuth(response) {
            // Loại bỏ focus khỏi các phần tử trước khi đóng modal
            document.activeElement.blur();
            // Hiển thị thông báo thành công
            showMessage(response.message || 'Thao tác thành công!', false);
            window.location.reload();
        }

        // Xử lý khi modal ẩn đi
        $('#modal-auth').on('hide.bs.modal', function () {
            // Đảm bảo rằng không còn phần tử nào giữ focus khi modal đóng
            document.activeElement.blur();
            $('body').trigger('focus');
        });

    </script>
@endif
{{--@if (get_frontend_settings('recaptcha_status'))--}}
    @push('js')
        <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    @endpush
{{--@endif--}}

<style>
    .google-btn {
        margin-bottom: 1rem;
        padding: 0.75rem;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        color: #222;
        border-radius: 10px;
        border: 1px solid #ffe6e8;
        background: linear-gradient(180deg, #fff 0%, #ffeff0 100%);
    }

    .scanner {
        position: absolute;
        width: 100%;
        height: 2px;
        background: linear-gradient(to right,
        rgba(255, 140, 0, 0) 0%,
        rgba(255, 140, 0, 0.4) 50%,
        rgba(255, 140, 0, 0) 100%);
        box-shadow: 0 0 4px rgba(255, 140, 0, 0.4), 0 0 8px rgba(255, 140, 0, 0.3);
        animation: scan 3s infinite linear;
        z-index: 10;
        opacity: 0.6;
    }

    @keyframes scan {
        0% {
            top: 0;
        }

        50% {
            top: calc(100% - 2px);
        }

        100% {
            top: 0;
        }
    }
</style>
<script>

    // Constants
    const ROUTES = {
        applyCouponCode: '{{ route('applyCoupon') }}',
        check_payment_success_order: '{{ route('check.payment.success.order') }}',
        checkout_course: '{{ route('checkout.course') }}',
    };
    const $price = $('#price');
    const $cancelCouponButton = $('#cancel_coupon');

    // Biến lưu trữ giá ban đầu của khóa học
    let originalPrice = {{ $course_details->discounted_price ?? 0 }};

    // Tab hiện tại
    let currentTab = 1;

    /**
     * Hiển thị tab tương ứng
     * @param {number} tabNumber - Số tab cần hiển thị
     */
    function showTab(tabNumber, timeRemaining = null) {
        // Ẩn tất cả các tab trong modal-regis
        $("#modal-regis .tab-content").hide();

        // Hiển thị tab hiện tại
        $("#modal-regis #tab-" + tabNumber).show();

        // Cập nhật trạng thái các bước
        $("#modal-regis .step").removeClass("active completed");

        for (let i = 1; i <= 3; i++) {
            if (i < tabNumber) {
                $("#modal-regis #step-" + i).addClass("completed");
            } else if (i === tabNumber) {
                $("#modal-regis #step-" + i).addClass("active");
            }
        }

        // Nếu đang ở tab thanh toán (tab 2) và đã chuyển tab, bắt đầu đếm ngược
        if (tabNumber === 2) {
            startPaymentTimer(timeRemaining);
        }
    }

    // Hiển thị tab đầu tiên khi tải trang
    $(document).ready(function () {
        showTab(currentTab);
    });

    /**
     * Utility Functions
     */
    // Viết lại hàm đổi giao diện Userprofile khi click nút đăng nhập đăng ký
    function changeUserProfile(html_profile) {
        @if(!Auth::check())
        $('.user-profile.dropdown').html(html_profile);
        @endif
    }

    /**
     * Hiển thị thông báo
     * @param {string} message - Nội dung thông báo
     * @param {boolean} isError - Có phải là thông báo lỗi hay không
     */
    function showMessage(message, isError = true) {
        // Kiểm tra xem toastr có được định nghĩa hay không
        if (typeof toastr !== 'undefined') {
            if (isError) {
                toastr.error(message);
            } else {
                toastr.success(message);
            }
        } else {
            // Fallback nếu không có toastr
            // alert(message);
        }
    }

    // remove invalid
    function removeInvalid() {
        $('.invalid-feedback').remove();
        $('.is-invalid').removeClass('is-invalid');
    }

    /**
     * Kiểm tra mã giảm giá
     * @param {string} couponCode - Mã giảm giá cần kiểm tra
     * @returns {Promise} - Promise từ request
     */
    function applyCoupon(couponCode) {
        return $.post(ROUTES.applyCouponCode, {
            _token: '{{ csrf_token() }}',
            coupon_code: couponCode,
            course_id: {{ $course_details->id }}
        });
    }

    /**
     * Kiểm tra trạng thái thanh toán
     * @param {string} orderId - ID của đơn hàng
     * @param {string} courseId - ID của khóa học
     * @returns {Promise} - Promise từ request
     */
    function checkPaymentStatus(orderId, courseId) {
        return $.post(ROUTES.check_payment_success_order, {
            _token: '{{ csrf_token() }}',
            order_id: orderId,
            course_id: courseId
        });
    }


    /**
     * Xử lý kiểm tra mã giảm giá
     * @param {Event} e - Sự kiện
     */
    function handleApplyCoupon(e) {
        e.preventDefault();

        const couponCode = $('#couponInput').val();

        if (!couponCode.trim()) {
            $('#coupon_code_hide').val("");
            $('.applied-coupon span').text('');
            $('.applied-coupon').hide();
            showMessage('Vui lòng nhập mã giảm giá');
            return;
        }

        // Hiển thị loading hoặc disabled button nếu cần
        const $applyBtn = $('#applyCoupon');
        const originalText = $applyBtn.html();
        $applyBtn.html('<i class="fas fa-spinner fa-spin"></i> Đang áp dụng...').prop('disabled', true);

        applyCoupon(couponCode).done(function (response) {
            // Khôi phục trạng thái button
            $applyBtn.html(originalText).prop('disabled', false);

            if (!response.error) {
                // Cập nhật giá sau khi áp dụng mã giảm giá
                const discountAmount = originalPrice * response.data.coupon.discount / 100;
                const finalPrice = originalPrice - discountAmount;

                $('#coupon_code_hide').val(response.data.coupon.code);
                // Cập nhật hiển thị giá
                $('#total_amount').html(formatCurrency(finalPrice));
                $price.val(finalPrice);

                // Hiển thị thông tin mã giảm giá đã áp dụng
                $('.applied-coupon span').text('Mã giảm giá "' + response.data.coupon.code + '" Sắp hết hạn!');
                $('.applied-coupon').show();

                $('.price-info-coupon').html(
                    `<span style="color: #105ce4"><span>Mã giảm giá</span> -
                            <span style="color: #dc3f2e; text-decoration: underline" id="cancel_coupon">Hủy bỏ</span></span>
                            <span class="price">${formatCurrency(discountAmount)}</span>`
                );
                $("#couponList").hide();
                // Ẩn form nhập coupon
                $('#coupon_form').hide();

                showMessage(response.message, false);
            } else {
                $('#coupon_code_hide').val("response.data.coupon.code");
                $('.applied-coupon span').text('');
                $('.applied-coupon').hide();
                showMessage(response.message || 'Mã giảm giá không hợp lệ');
            }
        }).fail(function (xhr) {
            // Khôi phục trạng thái button
            $('#coupon_code_hide').val("");
            $applyBtn.html(originalText).prop('disabled', false);

            let errorMessage = 'Lỗi server trong quá trình kiểm tra mã giảm giá';
            try {
                const response = JSON.parse(xhr.responseText);
                if (response && response.message) {
                    errorMessage = response.message;
                }
            } catch (e) {

            }

            $('#coupon_code_hide').val("");
            $('.applied-coupon span').text('');
            $('.applied-coupon').hide();
            showMessage(errorMessage);
        })
    }

    /**
     * Xử lý hủy mã giảm giá
     * @param {Event} e - Sự kiện
     */
    function handleCancelCoupon(e) {
        e.preventDefault();

        $('#coupon_code_hide').val("");

        // Khôi phục giá ban đầu
        $('#display_price').html(formatCurrency(originalPrice));
        $price.val(originalPrice);

        // Xóa thông tin mã giảm giá đã áp dụng
        $('.price-info-coupon').empty();

        // Hiển thị lại form nhập coupon
        $('#coupon_form').show();

        // Xóa giá trị coupon đã nhập
        $('#couponInput').val('');

        // Ẩn nút hủy coupon
        // $cancelCouponButton.html("");
        $("#total_amount").html(formatCurrency(originalPrice));
        $('.applied-coupon span').text('');
        $('.applied-coupon').hide();
        showMessage('Đã hủy mã giảm giá', false);
    }

    /**
     * Xử lý thanh toán
     * @param {Event} e - Sự kiện
     */
    function handlePaymentCheckStatus(order_id, courseId) {
        const checkInterval = 5000; // 5 giây

        // Kiểm tra trạng thái thanh toán mỗi 5 giây
        const paymentChecker = setInterval(function () {
            checkPaymentStatus(order_id, courseId)
                .done(function (response) {

                    if (!response.error) {
                        // Hiển thị thông báo thành công
                        // Kiểm tra xem modal regis đã được show chưa
                        if (!$('#modal-regis').is(':visible')) {
                            $('#modal-regis').modal('show');
                        }
                        $(".payment-header-b").hide();
                        showTab(3);


                        // Dừng việc kiểm tra
                        clearInterval(paymentChecker);

                    } else {
                        if (response.is_cancel) {
                            if (!$('#modal-regis').is(':visible')) {
                                $('#modal-regis').modal('show');
                            }
                            $(".payment-header-b").hide();
                            $('.payment-methods-container').addClass('text-center');
                            $('.payment-methods-container').text(response.message);
                            showTab(2);
                            clearInterval(paymentChecker);
                        }
                    }
                })
                .fail(function () {

                });
        }, checkInterval);
    }

    function nextTab(timeRemaining = null) {
        if (currentTab < 3) {
            currentTab++;
            showTab(currentTab, timeRemaining);

            // Start the timer when switching to payment tab
            if (currentTab === 2) {
                startPaymentTimer(timeRemaining);
            }
        }
    }

    // Định nghĩa hàm startPaymentTimer trực tiếp trong file này
    let paymentTimerInterval;

    function startPaymentTimer(timeRemaining = null) {
        const display = document.querySelector('.timer');
        if (!display) return;

        // Xóa interval cũ nếu có
        if (paymentTimerInterval) {
            clearInterval(paymentTimerInterval);
        }
        if (!timeRemaining) {
            // Thời gian 15 phút (tính bằng giây)
            timeLeft = 15 * 60
        } else {
            timeLeft = timeRemaining;
        }

        // Hiển thị thời gian ban đầu
        updateTimerDisplay();

        // Bắt đầu đếm ngược
        paymentTimerInterval = setInterval(function () {
            timeLeft--;

            if (timeLeft <= 0) {
                clearInterval(paymentTimerInterval);
                // Hiển thị thông báo hết hạn
                $(".payment-methods-container").html(`
                    <div class="alert alert-danger">
                        Thời gian thanh toán đã hết! Vui lòng tải lại trang và thử lại!
                    </div>
                `);
            } else {
                updateTimerDisplay();
            }
        }, 1000);

        function updateTimerDisplay() {
            // Tính toán giờ, phút, giây
            const hours = Math.floor(timeLeft / 3600);
            const minutes = Math.floor((timeLeft % 3600) / 60);
            const seconds = timeLeft % 60;

            // Format với số 0 đứng trước nếu cần
            const displayHours = hours < 10 ? '0' + hours : hours;
            const displayMinutes = minutes < 10 ? '0' + minutes : minutes;
            const displaySeconds = seconds < 10 ? '0' + seconds : seconds;

            // Cập nhật hiển thị
            display.textContent = `${displayHours}:${displayMinutes}:${displaySeconds}`;
        }
    }

    function showQr(total_amount = null, transaction_content = null) {
        // Get necessary values from context
        const bankCode = "{{$bank_code ?? ""}}";
        const accountNumber = "{{$account_number ?? ""}}";
        const accountName = "{{$account_name ?? ""}}";

        // Set default values if not provided
        const amount = total_amount ? total_amount.toString() : "0";
        const addInfo = transaction_content || "";

        // Create the VietQR image URL
        const qrImageUrl = `https://api.vietqr.io/image/${bankCode}-${accountNumber}-w8onyVi.jpg?accountName=${encodeURIComponent(accountName)}&amount=${amount}&addInfo=${encodeURIComponent(addInfo)}`;

        // Get the container
        const container = document.getElementById("qrcode");

        // Remove any existing img elements but keep the scanner div
        const existingImages = container.querySelectorAll("img");
        existingImages.forEach(img => img.remove());

        // Create and insert the image element
        const qrImageElement = document.createElement("img");
        qrImageElement.src = qrImageUrl;
        qrImageElement.style.display = "block";
        qrImageElement.className = "img-fluid"; // Adding img-fluid class to the image

        // Append the new QR image to the container (the scanner div will remain)
        container.appendChild(qrImageElement);
    }

    /**
     * Format số tiền thành chuỗi có định dạng tiền tệ
     * @param {number} amount - Số tiền cần định dạng
     * @returns {string} - Chuỗi đã định dạng
     */
    function formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
            minimumFractionDigits: 0
        }).format(amount);
    }

    /**
     * Format ngày thành chuỗi có định dạng
     * @param {string|number} dateValue - Giá trị ngày
     * @returns {string} - Chuỗi đã định dạng
     */
    function formatDate(dateValue) {
        try {
            // Xử lý trường hợp dateValue là timestamp
            const date = typeof dateValue === 'number'
                ? new Date(dateValue * 1000)
                : new Date(dateValue);

            return date.toLocaleDateString('vi-VN', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (e) {
            return dateValue;
        }
    }

    function onHandlecheckoutPayment(token) {
        if (!token) {
            showMessage('Vui lòng xác thực captcha', true);
            return false;
        }

        // mỗi khi click thì xóa hết các lỗi
        removeInvalid();
        const formData = $('#form-checkout-regis').serialize();
        // submit form
        $.post(ROUTES.checkout_course, formData, function (response) {
            if (!response.error) {

                changeUserProfile(response.data.html_profile);
                if (response.data.total_amount === 0) {
                    $(".payment-header-b").hide();
                    $('.payment-methods-container').addClass('text-center');
                    $('.payment-methods-container').text(response.message);

                    $('.hero-section .action-buttons').html(`
                        <a href="${response.data.url_play_course}"
                           class="text-center mt-3 start-course-btn m-auto">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}"
                                 style="filter: invert(1);     display: inline-block;"/>
                            VÀO HỌC NGAY!</a>
                        `);
                    $('#modal-regis .payment-header-t').hide();
                    $('#modal-regis .payment-body').addClass('text-center');
                    $('#modal-regis .payment-body').html(`<span class="text-danger">${response.message}</span>
                        <a href="${response.data.url_play_course}"
                           class="text-center mt-3 start-course-btn m-auto">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}"
                                 style="filter: invert(1);     display: inline-block;"/>
                            VÀO HỌC NGAY!</a>
                        `);
                    showTab(2);
                    clearInterval(paymentChecker);
                } else {
                    if (!response.data.url_play_course) {

                        $('.bank_total_amount').text(formatCurrency(response.data.total_amount));
                        $('.bank_transaction_content').text(response.data.transaction_content);
                        showQr(response.data.total_amount, response.data.transaction_content);

                        nextTab(response.data.time);
                        handlePaymentCheckStatus(response.data.order_id, response.data.course_id)
                    } else {
                        $('.hero-section .action-buttons').html(`
                        <a href="${response.data.url_play_course}"
                           class="text-center mt-3 start-course-btn m-auto">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}"
                                 style="filter: invert(1);    display: inline-block;"/>
                            VÀO HỌC NGAY!</a>
                        `);
                        $('#modal-regis .payment-header-t').hide();
                        $('#modal-regis .payment-body').addClass('text-center');
                        $('#modal-regis .payment-body').html(`<span class="text-danger">${response.message}</span>
                        <a href="${response.data.url_play_course}"
                           class="text-center mt-3 start-course-btn m-auto">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}"
                                 style="filter: invert(1);    display: inline-block;"/>
                            VÀO HỌC NGAY!</a>
                        `);
                    }
                    $('.payment-header-b').hide();
                }


            } else {
                showMessage(response.message, false);
            }
        }).fail(function (xhr) {
            // Kiểm tra xem response có phải là json hay không
            try {
                const response = JSON.parse(xhr.responseText);
                if (response && response.error && response.message) {
                    // Hiển thị lỗi vào form theo từng input tương ứng
                    if (response.message.email) {
                        $('#payment_register_email').addClass('is-invalid');
                        $('#payment_register_email').after(`<div class="invalid-feedback">${response.message.email}</div>`);
                    }
                    if (response.message.password) {
                        $('#payment_register_password').addClass('is-invalid');
                        $('#payment_register_password').after(`<div class="invalid-feedback">${response.message.password}</div>`);
                    }
                    if (response.message.phone) {
                        $('#payment_register_phone').addClass('is-invalid');
                        $('#payment_register_phone').parents(".phone-group").addClass('is-invalid');
                        $('#payment_register_phone').parents(".phone-group").after(`<div class="invalid-feedback">${response.message.phone}</div>`);
                    }
                    if (response.message.terms) {
                        $('#payment_register_terms').addClass('is-invalid');
                        $('#payment_register_terms').parent().addClass('is-invalid');
                        $('#payment_register_terms').parent().after(`<div class="invalid-feedback">${response.message.terms}</div>`);
                    }
                    if (response.message.captcha) {
                        $('.g-recaptcha-checkout').addClass('is-invalid');
                        $('.g-recaptcha-checkout').after(`<div class="invalid-feedback">${response.message.captcha}</div>`);
                    }
                }
            } catch (e) {
                // Xử lý lỗi CSRF token hết hạn
                if (xhr.status === 419) {
                    showMessage('Phiên làm việc đã hết hạn. Vui lòng làm mới trang và thử lại.', true);
                    // Tự động làm mới trang sau 3 giây
                    setTimeout(function () {
                        window.location.reload();
                    }, 3000);
                } else {
                    showMessage('Đã xảy ra lỗi. Vui lòng thử lại sau.', true);
                }
            }
        });
    }

    $(document).ready(function () {
        $("body").on('click', '.login-link', function (e) {
            e.preventDefault();

            removeInvalid();
            $(".is-show").hide();
            $(".change-login-register").html(`Bạn chưa có tài khoản? <a class="register-link-modal">Ấn vào đây để đăng ký</a><input type="hidden" value="1" name="is_login">`);
        });
        $("body").on('click', '.register-link-modal', function (e) {
            e.preventDefault();

            removeInvalid();
            $(".is-show").show();
            $(".change-login-register").html(`Bạn chưa có tài khoản? <a class="login-link">Ấn vào đây để đăng nhập</a>`);

        });


        // Event bindings
        $("body").on('click', "#applyCoupon", handleApplyCoupon);
        $("body").on('click', "#cancel_coupon", handleCancelCoupon);
        @if(isset($offline_payment) && is_object($offline_payment) && $offline_payment->status == 0)
        handlePaymentCheckStatus("{{ $offline_payment->id }}", "{{ $offline_payment->course_id }}");
        @endif

    });
</script>

<!-- Hiển thị modal khi có session show_payment_modal -->
@if (session('show_payment_modal'))
    <script>
        $(document).ready(function () {
            $('#modal-regis').modal('show');
            // $('#modal-regis').
        });
    </script>
@endif
<div class="modal fade modal-regis" id="modal-regis" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true"
     data-bs-backdrop="static" style="top:50px">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="payment-container position-relative">
                <button type="button" class="close-btn" data-bs-dismiss="modal" aria-label="Close">
                    <i class="bi bi-x-circle"></i>
                </button>
                @if($enroll_status !== 'valid')
                    <div class="payment-header-t">
                        <div class="progress-steps">
                            <div class="progress-line"></div>
                            <div class="step" id="step-1">
                                <div class="step-circle">1</div>
                                <div class="step-label">Thông tin</div>
                            </div>
                            <div class="step" id="step-2">
                                <div class="step-circle">2</div>
                                <div class="step-label">Chuyển khoản</div>
                            </div>
                            <div class="step" id="step-3">
                                <div class="step-circle">3</div>
                                <div class="step-label">Thành công</div>
                            </div>
                        </div>

                        <div class="payment-header-b">
                            <div class="payment-header">
                                <h5 class="mb-0 fw-bold">
                                    {{ $course_details->title }}
                                </h5>
                            </div>
                            @php
                                if ($course_details->discounted_price < $course_details->price){
                                    $price = $course_details->discounted_price;
                                }elseif (isset($course_details->is_paid) && $course_details->is_paid == 0){
                                    $price = 0;
                                }elseif (isset($course_details->discount_flag) && $course_details->discount_flag == 1){
                                    $price = $course_details->discounted_price;
                                }else{
                                    $price = $course_details->price;
                                }
                            @endphp
                            <div class="price-info">
                                <span>Giá gốc:</span>
                                <span class="text-decoration-line-through">{{ currency($course_details->price) }}</span>
                            </div>
                            <div class="price-info">
                                <span>Giá ưu đãi hôm nay:</span>
                                <strong>{{ currency($price) }}</strong>
                            </div>
                            @if(isset($coupon_affiliate) && is_object($coupon_affiliate))

                                <div class="price-info price-info-coupon"><span
                                        style="color: #105ce4"><span>Mã giảm giá</span>
                                        -
                                        <span style="color: #dc3f2e; text-decoration: underline" id="cancel_coupon">Hủy
                                            bỏ</span></span>
                                    <span class="price">{{ currency($discount_coupon) }}</span>
                                </div>
                                <div class="applied-coupon mt-4 mb-4">
                                    <span class="btn btn-lg text-white w-100"
                                          style="background-color: #fa8128; padding: 10px; font-weight: 500; font-size: 16px;">Mã
                                        giảm giá "{{ $coupon_affiliate->code }}" Sắp hết hạn!</span>
                                </div>
                                <div class="input-group mt-3" id="coupon_form" style="display: none;">
                                    <input type="text" class="form-control" id="couponInput" placeholder="Mã ưu đãi"/>
                                    <button class="btn code-btn text-white" type="button" id="applyCoupon">
                                        Áp dụng
                                    </button>
                                </div>
                            @else
                                <div class="price-info price-info-coupon">
                                </div>
                                <div class="applied-coupon mt-4 mb-4" style="display: none;">
                                    <span class="btn btn-lg text-white w-100"
                                          style="background-color: #fa8128; padding: 10px; font-weight: 500; font-size: 16px;"></span>
                                </div>

                                <div class="input-group mt-3" id="coupon_form">
                                    <input type="text" class="form-control" id="couponInput" placeholder="Mã ưu đãi"/>
                                    <button class="btn code-btn text-white" type="button" id="applyCoupon">
                                        Áp dụng
                                    </button>
                                </div>

                            @endif
                            {{--                            @if(session('show_payment_modal') && isset($message_coupon))--}}
                            {{--                                <div class="d-flex align-items-center mt-2">--}}
                            {{--                                    <span class="text-danger">{{ $message_coupon }}</span>--}}
                            {{--                                </div>--}}
                            {{--                            @endif--}}
                            <div class="d-flex align-items-center mt-2">
                                <i class="bi bi-tag-fill text-danger me-2"></i>
                                <a href="#" class="text-decoration-none text-danger" id="showCoupons">
                                    Xem danh sách mã giảm giá
                                </a>
                            </div>

                            <div class="coupon-container" id="couponList" style="display: none;">
                                @foreach ($coupons as $couponCode)
                                    <button class="coupon-btn" data-code="{{ $couponCode }}">
                                        {{ $couponCode }}
                                    </button>
                                @endforeach
                            </div>

                            <div class="total-price">
                                <span class="fw-bold">Tổng thanh toán:</span>
                                <span class="price"
                                      id="total_amount">{{ currency(isset($coupon_affiliate) && is_object($coupon_affiliate) ? $course_details->discounted_price - $course_details->discounted_price * $coupon_affiliate->discount / 100 : $course_details->discounted_price) }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="payment-body">
                        <!-- Tab 1: Information -->
                        <div class="tab-content" id="tab-1">
                            <form action="{{ route('checkout.course') }}" method="POST" id="form-checkout-regis">
                                @csrf
                                <input type="text" id="coupon_code_hide" name="coupon_code"
                                       value="{{ isset($coupon_affiliate) && is_object($coupon_affiliate) ? $coupon_affiliate->code : ''}}"
                                       hidden>
                                <input type="text" id="course_id_hide" name="course_id"
                                       value="{{ $course_details->id }}"
                                       hidden>
                                @if (!Auth::check())

                                    @if (get_frontend_settings('google_status'))
                                        <a class="google-btn" href="{{route('auth.google.redirect')}}">
                                            <svg width="20" height="20" viewBox="0 0 533.5 544.3"
                                                 xmlns="http://www.w3.org/2000/svg"
                                                 class="me-2">

                                                <path
                                                    d="M533.5 278.4c0-18.5-1.5-37.1-4.7-55.3H272.1v104.8h147c-6.1 33.8-25.7 63.7-54.4 82.7v68h87.7c51.5-47.4 81.1-117.4 81.1-200.2z"
                                                    fill="#4285f4"></path>
                                                <path
                                                    d="M272.1 544.3c73.4 0 135.3-24.1 180.4-65.7l-87.7-68c-24.4 16.6-55.9 26-92.6 26-71 0-131.2-47.9-152.8-112.3H28.9v70.1c46.2 91.9 140.3 149.9 243.2 149.9z"
                                                    fill="#34a853"></path>
                                                <path
                                                    d="M119.3 324.3c-11.4-33.8-11.4-70.4 0-104.2V150H28.9c-38.6 76.9-38.6 167.5 0 244.4l90.4-70.1z"
                                                    fill="#fbbc04"></path>
                                                <path
                                                    d="M272.1 107.7c38.8-.6 76.3 14 104.4 40.8l77.7-77.7C405 24.6 340.1 0 272.1 0 169.2 0 75.1 58 28.9 150l90.4 70.1c21.5-64.5 81.8-112.4 152.8-112.4z"
                                                    fill="#ea4335"></path>
                                            </svg>
                                            Đăng nhập với Google
                                        </a>
                                    @endif
                                    <div class="user-info-container">
                                        <h6 class="fw-bold mb-3">Thông tin của bạn:</h6>
                                        <p class="mb-4 change-login-register">
                                            Bạn đã có tài khoản?
                                            <a class="login-link">Ấn vào đây
                                                để đăng nhập</a>
                                        </p>

                                        <div class="mb-4 form-wrap">
                                            <label for="email" class="form-label">Email *:</label>
                                            <input type="email" value="{{ old('email') }}" class="form-control"
                                                   name="email"
                                                   id="payment_register_email" required/>
                                            @error('email')
                                            <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>

                                        <div class="mb-4 form-wrap">
                                            <label for="password" class="form-label">Mật khẩu *:</label>
                                            <div class="password-field">
                                                <input type="password" class="form-control" name="password"
                                                       id="payment_register_password" required/>
                                                <button class="password-toggle" type="button">
                                                    <i class="bi bi-eye-slash"></i>
                                                </button>
                                            </div>
                                            @error('password')
                                            <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>

                                        <div class="mb-4 form-wrap is-show">
                                            <label for="phone" class="form-label">Số điện thoại *:</label>
                                            <div class="input-group phone-group">
                                                <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown">
                                                    <img
                                                        src="{{ asset('assets/frontend/course-shopee/assets/images/vn.png') }}"
                                                        alt="Vietnam Flag" width="20" height="15"/>
                                                    +84
                                                </button>
                                                <input type="tel" value="{{ old('phone') }}" class="form-control"
                                                       name="phone"
                                                       id="payment_register_phone"/>

                                            </div>
                                            @error('phone')
                                            <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>

                                        <p class="small text-muted mb-3 is-show">
                                            Thông tin cá nhân của bạn sẽ được sử dụng để xử lý đơn hàng,
                                            tăng trải nghiệm sử dụng website, và cho các mục đích cụ thể
                                            khác đã được mô tả trong
                                            <a href="#" class="privacy-link">chính sách riêng tư</a>
                                            của chúng tôi.
                                        </p>
                                        <div class="is-show">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" name="terms" type="checkbox"
                                                       id="payment_register_terms"/>
                                                <label class="form-check-label" for="payment_register_terms">
                                                    Tôi đã đọc và đồng ý với
                                                    <a href="#" class="privacy-link">điều khoản và điều kiện</a>
                                                    của website
                                                </label>
                                            </div>
                                            @error('terms')
                                            <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>

                                        <button class="btn g-recaptcha g-recaptcha-checkout" id="btn-checkout-regis"
                                                data-sitekey="{{ get_frontend_settings('recaptcha_sitekey') }}"
                                                data-callback="onHandlecheckoutPayment" data-action="submit"
                                                type="button">
                                            Tiến hành thanh toán
                                        </button>

                                    </div>
                                @else

                                    <button class="btn g-recaptcha g-recaptcha-checkout" id="btn-checkout-regis"
                                            type="button"
                                            data-sitekey="{{ get_frontend_settings('recaptcha_sitekey') }}"
                                            data-callback="onHandlecheckoutPayment" data-action="submit" type="button">
                                        Tiến hành thanh toán
                                    </button>

                            @endif
                            <!-- <div class="nav-buttons">
                                                    <button class="nav-btn prev-btn" disabled>Quay lại</button>
                                                    <button class="nav-btn next-btn" onclick="nextTab()">
                                                        Tiếp tục
                                                    </button>
                                                </div> -->
                            </form>
                        </div>

                        <!-- Tab 2: Payment -->
                        <div class="tab-content" id="tab-2">
                            <div class="payment-methods-container">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="method-option">
                                            <div class="method-title">
                                                <strong>Cách 1:</strong> Mở app ngân hàng/Ví và
                                                <strong>Quét mã QR</strong>
                                            </div>
                                            <div class="qr-container">
                                                <img
                                                    src="{{ asset('assets/frontend/course-shopee/assets/images/border-bank.png') }}"
                                                    alt="Border Bank" class="border-bank img-fluid mb-2"/>


                                                <div id="qrcode" class="img-fluid mb-2 img-bank">
                                                    <div class="scanner"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="method-option">
                                            <div class="method-title">
                                                <strong>Cách 2:</strong> Chuyển khoản
                                                <strong>thủ công</strong> theo thông tin
                                            </div>
                                            <div class="body-method-option">

                                                <div class="bank-info">
                                                    <div class="bank-info-row">
                                                        <span class="bank-info-label">Ngân hàng:</span>
                                                        <span
                                                            class="bank-info-value">{!! getBankData($bank_code ?? "") !!}</span>
                                                    </div>
                                                    <div class="bank-info-row">
                                                        <span class="bank-info-label">Thụ hưởng:</span>
                                                        <span class="bank-info-value">{{$account_name ?? ""}}</span>
                                                    </div>
                                                    <div class="bank-info-row">
                                                        <span class="bank-info-label">Số tài khoản:</span>
                                                        <span class="bank-info-value">{{$account_number ?? ""}} <i
                                                                class="bi bi-copy copy-value text-black"
                                                                data-value="{{$account_number ?? ""}}"></i></span>
                                                    </div>
                                                    <div class="bank-info-row">
                                                        <span class="bank-info-label">Số tiền:</span>
                                                        <span
                                                            class="bank-info-value bank_total_amount">{{isset($total_amount) ? currency($total_amount) : ''}}</span>
                                                    </div>
                                                    <div class="bank-info-row">
                                                        <span class="bank-info-label">Nội dung CK:</span>
                                                        <div>
                                                            <span
                                                                class="bank-info-value bank_transaction_content">{{$transaction_content ?? ""}}</span>
                                                            <i class="bi bi-copy copy-value text-black"
                                                               data-value="{{$transaction_content ?? ""}}"></i>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="warning-box">
                                                    <i class="bi bi-exclamation-triangle-fill warning-icon"></i>
                                                    <span class="fw-bold">Lưu ý:</span>
                                                    Vui lòng giữ nguyên nội dung chuyển khoản
                                                    <strong
                                                        class="bank_transaction_content">{{$transaction_content ?? ""}}</strong>
                                                    để xác nhận thanh toán tự
                                                    động.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="timer-container">
                                    <div>Đơn hàng sẽ bị hủy sau:</div>
                                    <div class="timer">00:15:00</div>
                                </div>

                                <div class="status-container">
                                    <strong>Trạng thái:</strong> Chờ thanh toán
                                    <span class="loading-icon">
                                        <i class="bi bi-arrow-repeat"></i>
                                    </span>
                                </div>
                            </div>

                            <!-- <div class="nav-buttons">
                                                    <button class="nav-btn prev-btn" onclick="prevTab()">
                                                        Quay lại
                                                    </button>
                                                    <button class="nav-btn next-btn" onclick="nextTab()">
                                                        Tiếp tục
                                                    </button>
                                                </div> -->
                        </div>

                        <!-- Tab 3: Success -->
                        <div class="tab-content" id="tab-3">
                            <div class="user-info-container text-center">
                                <div class="success-icon">
                                    <i class="bi bi-check"></i>
                                </div>
                                <div class="success-title">Thanh toán thành công!</div>
                                <div class="success-text">
                                    Cảm ơn bạn đã đăng ký khóa học. Thanh toán của bạn đã thành
                                    công.<br/>
                                    Hóa đơn sẽ được gửi đến email bạn đã đăng ký.
                                </div>
                                <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                                   class="start-course-btn" style="margin: 0 auto;color: #fff">VÀO HỌC NGAY!</a>
                                <div class="contact-text">
                                    Cần hỗ trợ? Hãy liên hệ chúng tôi qua hotline
                                    <span class="hotline">{{get_frontend_settings('hotline')}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="text-center mt-3">
                        <span class="text-danger">Bạn đã đăng ký khóa học này</span>
                        <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                           class="text-center mt-3 start-course-btn m-auto mb-5">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}"
                                 style="filter: invert(1);    display: inline-block;"/>
                            VÀO HỌC NGAY!</a>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
    #qrcode {
        background-color: white;
        padding: 8px;
        border-radius: 4px;
    }

    .scanner {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(to right,
        rgba(255, 140, 0, 0) 0%,
        rgba(255, 140, 0, 0.4) 50%,
        rgba(255, 140, 0, 0) 100%);
        box-shadow: 0 0 4px rgba(255, 140, 0, 0.4), 0 0 8px rgba(255, 140, 0, 0.3);
        z-index: 10;
        animation: scan 3s infinite linear;
        opacity: 0.6;
    }

    @keyframes scan {
        0% {
            top: 0;
        }

        50% {
            top: calc(100% - 2px);
        }

        100% {
            top: 0;
        }
    }

    #qrcode::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom,
        rgba(255, 140, 0, 0.03) 0%,
        rgba(255, 140, 0, 0) 40%,
        rgba(255, 140, 0, 0) 60%,
        rgba(255, 140, 0, 0.03) 100%);
        pointer-events: none;
        z-index: 5;
    }
</style>

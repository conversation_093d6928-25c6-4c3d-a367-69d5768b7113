@extends('layouts.admin')
@push('title', 'Chỉnh sửa trang')
@push('meta')@endpush
@push('css')@endpush

@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-edit me-2"></i>
                    Chỉnh sửa trang: {{ $page->title }}
                </h4>

                <div class="d-flex gap-2">
                    <a href="{{ $page->url }}" target="_blank" class="btn ol-btn-outline-primary d-flex align-items-center cg-10px">
                        <span class="fi-rr-eye"></span>
                        <span>Xem trang</span>
                    </a>
                    <a href="{{ route('admin.pages.index') }}" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                        <span class="fi-rr-arrow-left"></span>
                        <span>Quay lại</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <form action="{{ route('admin.pages.update', $page->id) }}" method="POST" enctype="multipart/form-data">
        @csrf
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="ol-card radius-8px">
                    <div class="ol-card-body p-20px">
                        <!-- Title -->
                        <div class="mb-3">
                            <label for="title" class="form-label ol-form-label">Tiêu đề trang <span class="text-danger">*</span></label>
                            <input type="text" name="title" id="title" class="form-control ol-form-control @error('title') is-invalid @enderror" 
                                   value="{{ old('title', $page->title) }}" placeholder="Nhập tiêu đề trang" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Slug -->
                        <div class="mb-3">
                            <label for="slug" class="form-label ol-form-label">Đường dẫn URL</label>
                            <div class="input-group">
                                <span class="input-group-text">{{ url('/') }}/</span>
                                <input type="text" name="slug" id="slug" class="form-control ol-form-control @error('slug') is-invalid @enderror" 
                                       value="{{ old('slug', $page->slug) }}" placeholder="duong-dan-url">
                            </div>
                            <small class="form-text text-muted">Để trống để tự động tạo từ tiêu đề</small>
                            @error('slug')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Content -->
                        <div class="mb-3">
                            <label for="content" class="form-label ol-form-label">Nội dung HTML <span class="text-danger">*</span></label>
                            <textarea name="content" id="content" class="form-control ol-form-control @error('content') is-invalid @enderror"
                                      rows="20" required placeholder="Dán mã HTML của bạn vào đây...">{{ old('content', $page->content) }}</textarea>
                            <small class="form-text text-muted">Dán trực tiếp mã HTML hoàn chỉnh vào đây. Hỗ trợ tất cả thẻ HTML và CSS inline.</small>
                            @error('content')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>


                    </div>
                </div>


            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Page Info -->
                <div class="ol-card radius-8px">
                    <div class="ol-card-body p-20px">
                        <h5 class="mb-3">
                            <i class="fi-rr-info me-2"></i>
                            Thông tin trang
                        </h5>
                        
                        <div class="mb-2">
                            <small class="text-muted">Người tạo:</small><br>
                            <strong>{{ $page->creator->name ?? 'N/A' }}</strong>
                        </div>
                        
                        <div class="mb-2">
                            <small class="text-muted">Ngày tạo:</small><br>
                            <strong>{{ $page->created_at->format('d/m/Y H:i') }}</strong>
                        </div>
                        
                        @if($page->updated_at != $page->created_at)
                        <div class="mb-2">
                            <small class="text-muted">Cập nhật lần cuối:</small><br>
                            <strong>{{ $page->updated_at->format('d/m/Y H:i') }}</strong>
                            @if($page->updater)
                                <br><small>bởi {{ $page->updater->name }}</small>
                            @endif
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Publish Settings -->
                <div class="ol-card radius-8px mt-3">
                    <div class="ol-card-body p-20px">
                        <h5 class="mb-3">
                            <i class="fi-rr-settings me-2"></i>
                            Cài đặt xuất bản
                        </h5>

                        <div class="mb-3">
                            <label for="status" class="form-label ol-form-label">Trạng thái</label>
                            <select name="status" id="status" class="form-select ol-form-control @error('status') is-invalid @enderror" required>
                                <option value="draft" {{ old('status', $page->status) == 'draft' ? 'selected' : '' }}>Bản nháp</option>
                                <option value="published" {{ old('status', $page->status) == 'published' ? 'selected' : '' }}>Đã xuất bản</option>
                                <option value="private" {{ old('status', $page->status) == 'private' ? 'selected' : '' }}>Riêng tư</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>



                        <div class="mb-3">
                            <label for="template" class="form-label ol-form-label">Template</label>
                            <select name="template" id="template" class="form-select ol-form-control @error('template') is-invalid @enderror">
                                <option value="default" {{ old('template', $page->template) == 'default' ? 'selected' : '' }}>Mặc định</option>
                                <option value="full-width" {{ old('template', $page->template) == 'full-width' ? 'selected' : '' }}>Toàn màn hình</option>
                                <option value="sidebar" {{ old('template', $page->template) == 'sidebar' ? 'selected' : '' }}>Có sidebar</option>
                                <option value="blank" {{ old('template', $page->template) == 'blank' ? 'selected' : '' }}>Không có header và footer</option>
                            </select>
                            <small class="form-text text-muted">Template "Không có header và footer" sẽ hiển thị nội dung HTML thuần túy không có layout của hệ thống.</small>
                            @error('template')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>

                <!-- Featured Image -->
                <div class="ol-card radius-8px mt-3">
                    <div class="ol-card-body p-20px">
                        <h5 class="mb-3">
                            <i class="fi-rr-picture me-2"></i>
                            Ảnh đại diện
                        </h5>

                        @if($page->featured_image)
                        <div class="mb-3 text-center">
                            <img src="{{ $page->featured_image_url }}" class="img-fluid rounded" style="max-height: 200px;">
                            <div class="mt-2">
                                <small class="text-muted">Ảnh hiện tại</small>
                            </div>
                        </div>
                        @endif

                        <div class="mb-3">
                            <input type="file" name="featured_image" id="featured_image" 
                                   class="form-control ol-form-control @error('featured_image') is-invalid @enderror" 
                                   accept="image/*">
                            <small class="form-text text-muted">Chấp nhận: JPG, PNG, GIF. Tối đa 2MB</small>
                            @error('featured_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div id="image-preview" class="text-center" style="display: none;">
                            <img id="preview-img" src="" class="img-fluid rounded" style="max-height: 200px;">
                            <div class="mt-2">
                                <small class="text-muted">Ảnh mới</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="ol-card radius-8px mt-3">
                    <div class="ol-card-body p-20px">
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn ol-btn-primary flex-fill">
                                <i class="fi-rr-disk me-1"></i> Cập nhật trang
                            </button>
                            <a href="{{ route('admin.pages.index') }}" class="btn ol-btn-secondary flex-fill">
                                <i class="fi-rr-cross me-1"></i> Hủy bỏ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>


@endsection

@push('js')
<script>
$(document).ready(function() {

    // Auto generate slug from title (only if slug is empty)
    $('#title').on('keyup', function() {
        let currentSlug = $('#slug').val();
        if (!currentSlug) {
            let title = $(this).val();
            let slug = title.toLowerCase()
                .replace(/[^\w\s-]/g, '') // Remove special characters
                .replace(/\s+/g, '-') // Replace spaces with hyphens
                .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
                .trim('-'); // Remove leading/trailing hyphens

            // Convert Vietnamese characters
            slug = slug.replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a');
            slug = slug.replace(/[èéẹẻẽêềếệểễ]/g, 'e');
            slug = slug.replace(/[ìíịỉĩ]/g, 'i');
            slug = slug.replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o');
            slug = slug.replace(/[ùúụủũưừứựửữ]/g, 'u');
            slug = slug.replace(/[ỳýỵỷỹ]/g, 'y');
            slug = slug.replace(/đ/g, 'd');

            $('#slug').val(slug);
        }
    });

    // Image preview
    $('#featured_image').on('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#preview-img').attr('src', e.target.result);
                $('#image-preview').show();
            };
            reader.readAsDataURL(file);
        } else {
            $('#image-preview').hide();
        }
    });

});
</script>
@endpush

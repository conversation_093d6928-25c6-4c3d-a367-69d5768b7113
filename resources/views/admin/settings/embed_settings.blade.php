@if(!addon_check('my.embed_registration'))
<div class="alert alert-warning d-flex align-items-center" role="alert">
    <div class="flex-shrink-0 me-3">
        <i class="bi bi-exclamation-triangle-fill fs-4"></i>
    </div>
    <div>
        <h4 class="alert-heading mb-1">Tính năng chưa được kích hoạt!</h4>
        <p class="mb-0">Vui lòng kích hoạt addon <strong>"Nhúng Form Đăng Ký"</strong> để sử dụng tính năng này.</p>
   
    </div>
</div>
@else
<div class="main-section pt-24px">
    <div class="row">
        <div class="col-md-7 col-xl-8">
            <div class="ol-card mb-24px">
                <div class="ol-card-header d-flex align-items-center justify-content-between mb-20px border-bottom pb-3">
                    <h4 class="ol-card-header-title m-0">
                        <i class="bi bi-code-square me-2 text-primary"></i>
                        Công cụ nhúng Form đăng ký
                    </h4>
                </div>
                <div class="ol-card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group mb-4">
                                <label for="course_id" class="fw-bold mb-2 d-flex align-items-center">
                                    <i class="bi bi-mortarboard me-2 text-primary"></i>
                                    Chọn khóa học:
                                </label>
                                <select class="form-select custom-select shadow-none" id="course_id" name="course_id" onchange="updateEmbedCode()">
                                    <option value="">Chọn một khóa học</option>
                                    @foreach ($courses as $course)
                                        <option value="{{ $course->slug }}">{{ $course->title }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label class="fw-bold mb-3 d-flex align-items-center">
                                    <i class="bi bi-textarea-t me-2 text-primary"></i>
                                    Tùy chỉnh nội dung:
                                </label>
                                <div class="customization-section p-3 border rounded">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="main_title" class="form-label text-secondary mb-2">Tiêu đề chính:</label>
                                            <input type="text" class="form-control custom-input" id="main_title" placeholder="Lộ trình khóa học" value="Lộ trình khóa học" onchange="updateEmbedCode()">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="highlight_title" class="form-label text-secondary mb-2">Tiêu đề nhấn mạnh:</label>
                                            <input type="text" class="form-control custom-input" id="highlight_title" placeholder="E-learning Master" value="E-learning Master" onchange="updateEmbedCode()">
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <label for="subtitle" class="form-label text-secondary mb-2">Phụ đề:</label>
                                            <input type="text" class="form-control custom-input" id="subtitle" placeholder="Đăng ký nhận ưu đãi ngay!" value="Đăng ký nhận ưu đãi ngay!" onchange="updateEmbedCode()">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="button_text" class="form-label text-secondary mb-2">Nội dung nút đăng ký:</label>
                                            <input type="text" class="form-control custom-input" id="button_text" placeholder="ĐĂNG KÝ NGAY" value="ĐĂNG KÝ NGAY" onchange="updateEmbedCode()">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="utm_source" class="form-label text-secondary mb-2">
                                                Campaign Source (UTM):
                                                <i class="bi bi-info-circle text-info ms-1 tooltip-icon" data-bs-toggle="tooltip" title="Mã nguồn chiến dịch marketing, sẽ được lưu vào cột 'source' trong bảng user"></i>
                                            </label>
                                            <input type="text" class="form-control custom-input" id="utm_source" placeholder="website, facebook, tiktok..." value="" onchange="updateEmbedCode()">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="embed-info" class="d-none animate-fade-in">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="custom-alert alert-info d-flex align-items-center">
                                    <div class="alert-icon-container me-3">
                                        <i class="bi bi-info-circle-fill text-primary"></i>
                                    </div>
                                    <span>Sao chép và dán mã này vào trang web của bạn để nhúng biểu mẫu đăng ký.</span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-12">
                                <label for="embed_code" class="fw-bold mb-2 d-flex align-items-center">
                                    <i class="bi bi-code me-2 text-primary"></i>
                                    Mã nhúng:
                                </label>
                                <div class="code-container">
                                    <textarea id="embed_code" class="form-control font-monospace code-area" rows="12" readonly></textarea>
                                    <button class="copy-btn btn-primary d-flex align-items-center justify-content-center" type="button" onclick="copyEmbedCode()">
                                        <i class="bi bi-clipboard me-1"></i> Sao chép
                                    </button>
                                </div>
                                <small class="text-muted mt-2 d-block">Sao chép và dán toàn bộ đoạn mã bao gồm cả JavaScript để đảm bảo chức năng hoạt động đúng.</small>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="redirect_url" class="fw-bold mb-2 d-flex align-items-center">
                                        <i class="bi bi-link me-2 text-primary"></i>
                                        URL chuyển hướng tùy chọn:
                                        <i class="bi bi-info-circle text-info ms-2 tooltip-icon" data-bs-toggle="tooltip" title="Nếu được cung cấp, người dùng sẽ được chuyển hướng đến URL này sau khi đăng ký thành công thay vì trang khóa học mặc định."></i>
                                    </label>
                                    <div class="input-group custom-input-group">
                                        <span class="input-group-text bg-light"><i class="bi bi-link-45deg"></i></span>
                                        <input type="text" class="form-control custom-input" id="redirect_url" placeholder="https://website-cua-ban.com/trang-cam-on" onchange="updateEmbedCode()">
                                        <button class="btn custom-btn btn-outline-secondary d-flex align-items-center justify-content-center" type="button" onclick="resetRedirectUrl()">
                                            <i class="bi bi-x-lg me-1"></i> Đặt lại
                                        </button>
                                    </div>
                                    <small class="text-muted mt-2 d-block">Nếu được cung cấp, người dùng sẽ được chuyển hướng đến URL này sau khi đăng ký thành công.</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="accordion custom-accordion" id="embedInstructions">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseInstructions" aria-expanded="false">
                                                <i class="bi bi-book me-2 text-primary"></i> Hướng dẫn nhúng
                                            </button>
                                        </h2>
                                        <div id="collapseInstructions" class="accordion-collapse collapse" data-bs-parent="#embedInstructions">
                                            <div class="accordion-body pt-4">
                                                <h5 class="fw-bold d-flex align-items-center">
                                                    <span class="number-circle d-flex align-items-center justify-content-center me-2">1</span>
                                                    Hướng dẫn cơ bản:
                                                </h5>
                                                <ol class="mb-4 ps-4 custom-list">
                                                    <li class="mb-2">Sao chép mã nhúng ở trên.</li>
                                                    <li class="mb-2">Dán vào trang web của bạn tại vị trí bạn muốn hiển thị biểu mẫu đăng ký.</li>
                                                    <li>Điều chỉnh chiều rộng, chiều cao và các thông số khác nếu cần.</li>
                                                </ol>

                                                <h5 class="fw-bold d-flex align-items-center">
                                                    <span class="number-circle d-flex align-items-center justify-content-center me-2">2</span>
                                                    Triển khai nâng cao:
                                                </h5>
                                                <p class="mb-3">Để kiểm soát tốt hơn, bạn cũng có thể lắng nghe sự kiện đăng ký thành công bằng JavaScript:</p>
                                                <div class="code-sample">
                                                <pre class="mb-0"><code>&lt;script&gt;
window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'registration_success') {
        window.location.href = event.data.redirectUrl;
    }
    if (event.data && event.data.type === 'registration_error') {
        console.error('Lỗi đăng ký:', event.data.message);
    }
});
&lt;/script&gt;</code></pre>
                                                </div>
                                                <p class="mt-3">Để biết thêm chi tiết, hãy tham khảo tài liệu tại <code class="inline-code">public/embed-examples/README.md</code></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-5 col-xl-4">
            <div class="ol-card mb-24px">
                <div class="ol-card-header d-flex align-items-center justify-content-between mb-20px border-bottom pb-3">
                    <h4 class="ol-card-header-title m-0">
                        <i class="bi bi-display me-2 text-primary"></i>
                        Xem trước biểu mẫu
                    </h4>
                </div>
                <div class="ol-card-body">
                    <div id="preview-placeholder" class="text-center py-5">
                        <div class="preview-placeholder-content">
                            <img src="{{ asset('assets/img/preview-placeholder.svg') }}" alt="Chọn khóa học để xem trước" class="mb-3">
                            <p class="text-muted mb-0">Chọn một khóa học để xem trước biểu mẫu</p>
                        </div>
                    </div>
                    <div id="preview-frame" class="d-none animate-fade-in">
                        <div class="embed-responsive">
                            <iframe id="preview-iframe" class="embed-responsive-item w-100" src=""></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif

<style>
:root {
    --primary-color: #4361ee;
    --primary-light: #ebefff;
    --primary-dark: #3a56d4;
    --secondary-color: #6c757d;
    --accent-color: #3bc9db;
    --success-color: #37b24d;
    --warning-color: #f59f00;
    --danger-color: #f03e3e;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-color: #e9ecef;
    --border-radius: 14px;
    --border-radius-sm: 8px;
    --shadow-sm: 0 2px 5px rgba(0,0,0,0.04);
    --shadow-md: 0 4px 15px rgba(0,0,0,0.07);
    --shadow-lg: 0 10px 35px rgba(0,0,0,0.1);
    --transition-speed: 0.3s;
}

.ol-card {
    transition: all var(--transition-speed) ease;
    border-radius: var(--border-radius);
    overflow: hidden;
    background-color: #fff;
    border: none;
    box-shadow: var(--shadow-md);
}

.ol-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.ol-card-header {
    padding: 1.5rem 1.75rem;
    background: linear-gradient(to right, var(--primary-light), white);
    border-bottom: 1px solid var(--border-color);
}

.ol-card-header-title {
    font-weight: 600;
    color: var(--dark-color);
}

.ol-card-body {
    padding: 1.75rem;
}

.form-select, .form-control {
    border: 1px solid var(--border-color);
    padding: 0.75rem 1.25rem;
    font-size: 0.95rem;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-speed) ease;
}

.custom-select {
    height: 50px;
    background-color: white;
    box-shadow: var(--shadow-sm);
    border-color: var(--border-color);
}

.custom-select:focus, .custom-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.15);
}

.customization-section {
    background-color: var(--light-color);
    border-radius: var(--border-radius-sm);
    border-color: var(--border-color) !important;
}

.custom-alert {
    position: relative;
    padding: 1.25rem;
    border-radius: var(--border-radius-sm);
    background-color: var(--primary-light);
    color: var(--primary-dark);
    border-left: 5px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.alert-icon-container {
    background-color: white;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    box-shadow: var(--shadow-sm);
}

.alert-icon-container i {
    font-size: 1.2rem;
}

.code-container {
    position: relative;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.code-area {
    background-color: #f8f9fa;
    white-space: pre;
    overflow-x: auto;
    line-height: 1.6;
    padding: 1rem;
    border: 1px solid var(--border-color);
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.9rem;
    resize: none;
}

.copy-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    min-width: 100px;
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 1rem;
    z-index: 10;
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    border: none;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    opacity: 0.9;
}

.copy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
    opacity: 1;
}

.custom-input-group {
    box-shadow: var(--shadow-sm);
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.custom-input {
    border-radius: 0;
    height: 50px;
}

.custom-btn {
    min-width: 100px;
    transition: all var(--transition-speed) ease;
    border-radius: 0;
}

.custom-btn:hover {
    transform: translateY(-2px);
}

.tooltip-icon {
    cursor: pointer;
    font-size: 0.85rem;
}

.accordion-item {
    border: none;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
}

.accordion-button {
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    background-color: var(--light-color);
    border: none;
    color: var(--dark-color);
}

.accordion-button:not(.collapsed) {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-body {
    padding: 1.75rem;
    background-color: white;
}

.number-circle {
    width: 28px;
    height: 28px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    font-size: 14px;
    font-weight: 600;
}

.custom-list li {
    position: relative;
    padding-left: 0.5rem;
}

.code-sample {
    position: relative;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    background-color: #f8f9fa;
    border: 1px solid var(--border-color);
}

.code-sample::before {
    content: "JavaScript";
    position: absolute;
    top: 0;
    right: 10px;
    background: #e9ecef;
    padding: 2px 8px;
    font-size: 12px;
    border-radius: 0 0 0 4px;
    color: var(--secondary-color);
}

.code-sample pre {
    padding: 1.5rem;
    margin: 0;
    background-color: transparent;
    border: none;
}

.inline-code {
    background-color: #f8f9fa;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 0.9rem;
    color: var(--primary-dark);
}

.preview-placeholder-content {
    padding: 3rem 1.5rem;
    background-color: var(--light-color);
    border-radius: var(--border-radius-sm);
}

.preview-placeholder-content img {
    max-width: 100px;
    opacity: 0.5;
}

#preview-iframe {
    height: 650px;
    border: none;
    border-radius: var(--border-radius-sm);
    box-shadow: var(--shadow-sm);
}

.animate-fade-in {
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.btn-success {
    background: linear-gradient(45deg, var(--success-color), #40c057);
    border: none;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .ol-card-header, .ol-card-body {
        padding: 1.25rem;
    }
    
    .copy-btn {
        position: static;
        width: 100%;
        margin-top: 0.5rem;
    }
    
    .code-container {
        display: flex;
        flex-direction: column;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Hide embed info initially
    document.getElementById('embed-info').classList.add('d-none');
    document.getElementById('preview-placeholder').classList.remove('d-none');
    document.getElementById('preview-frame').classList.add('d-none');
    
    // Initialize tooltips
    try {
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        if (tooltipTriggerList.length > 0 && typeof bootstrap !== 'undefined') {
            const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => {
                return new bootstrap.Tooltip(tooltipTriggerEl, {
                    boundary: document.body
                });
            });
        }
    } catch (error) {
        console.error('Error initializing tooltips:', error);
    }
});

function updateEmbedCode() {
    const courseSlug = document.getElementById('course_id').value;
    const redirectUrl = document.getElementById('redirect_url').value;
    const mainTitle = document.getElementById('main_title').value;
    const highlightTitle = document.getElementById('highlight_title').value;
    const subtitle = document.getElementById('subtitle').value;
    const buttonText = document.getElementById('button_text').value;
    const utmSource = document.getElementById('utm_source').value;
    
    if (!courseSlug) {
        document.getElementById('embed-info').classList.add('d-none');
        document.getElementById('preview-placeholder').classList.remove('d-none');
        document.getElementById('preview-frame').classList.add('d-none');
        return;
    }
    
    document.getElementById('embed-info').classList.remove('d-none');
    document.getElementById('preview-placeholder').classList.add('d-none');
    document.getElementById('preview-frame').classList.remove('d-none');
    
    // Create base URL for embed with custom parameters
    const baseUrl = `${window.location.origin}/embed/register/${courseSlug}`;
    
    // Build query parameters
    const params = new URLSearchParams();
    if (redirectUrl) params.append('redirect_url', redirectUrl);
    if (mainTitle) params.append('main_title', mainTitle);
    if (highlightTitle) params.append('highlight_title', highlightTitle);
    if (subtitle) params.append('subtitle', subtitle);
    if (buttonText) params.append('button_text', buttonText);
    if (utmSource) params.append('utm_source', utmSource);
    
    const urlWithParams = params.toString() ? `${baseUrl}?${params.toString()}` : baseUrl;
    
    // Update iframe source
    document.getElementById('preview-iframe').src = urlWithParams;
    
    // Generate embed code - include both iframe and JavaScript handler
    const embedCode = `<!-- Form Đăng Ký Khóa Học -->
<iframe src="${urlWithParams}" width="100%" height="700" style="border: none; max-width: 500px; margin: 0 auto; display: block; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);"></iframe>

<!-- Tuỳ chọn: JavaScript để xử lý chuyển hướng tuỳ chỉnh -->
<script>
window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'registration_success') {
        window.location.href = event.data.redirectUrl;
    }
    if (event.data && event.data.type === 'registration_error') {
        console.error('Lỗi đăng ký:', event.data.message);
    }
});
<\/script>`;
    
    document.getElementById('embed_code').value = embedCode;
}

function copyEmbedCode() {
    const embedCodeElement = document.getElementById('embed_code');
    embedCodeElement.select();
    
    try {
        // Use the newer clipboard API if available
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(embedCodeElement.value)
                .then(showCopySuccess)
                .catch(err => {
                    console.error('Failed to copy: ', err);
                    // Fall back to execCommand
                    document.execCommand('copy');
                    showCopySuccess();
                });
        } else {
            // Use the older approach as fallback
            document.execCommand('copy');
            showCopySuccess();
        }
    } catch (err) {
        console.error('Copy failed: ', err);
    }
}

function showCopySuccess() {
    // Show success notification
    const copyBtn = document.querySelector('button.copy-btn');
    const originalContent = copyBtn.innerHTML;
    
    copyBtn.innerHTML = '<i class="bi bi-check-lg me-1"></i> Đã sao chép!';
    copyBtn.style.background = 'linear-gradient(45deg, #37b24d, #40c057)';
    
    setTimeout(() => {
        copyBtn.innerHTML = originalContent;
        copyBtn.style.background = 'linear-gradient(45deg, var(--primary-color), var(--primary-dark))';
    }, 2000);
}

function resetRedirectUrl() {
    document.getElementById('redirect_url').value = '';
    updateEmbedCode();
    
    // Show feedback
    const resetBtn = document.querySelector('button[onclick="resetRedirectUrl()"]');
    const originalHTML = resetBtn.innerHTML;
    
    resetBtn.innerHTML = '<i class="bi bi-check-lg me-1"></i> Đã đặt lại';
    resetBtn.classList.add('btn-success');
    resetBtn.classList.remove('btn-outline-secondary');
    
    setTimeout(() => {
        resetBtn.innerHTML = originalHTML;
        resetBtn.classList.remove('btn-success');
        resetBtn.classList.add('btn-outline-secondary');
    }, 1000);
}
</script> 
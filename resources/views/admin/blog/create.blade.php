@extends('layouts.admin')
@push('title', get_phrase('Add Blog'))
@push('meta')@endpush
@push('css')
    {{-- this is bootstrap tag --}}
    <link href="{{ asset('assets/backend/css/bootstrap-tagsinput.css') }}" rel="stylesheet" type="text/css" />
@endpush
@section('content')
    <style>
        .image_preview {
            width: 100%;
            height: 200px;
            margin-bottom: 12px;
            border-radius: 8px;
            overflow: hidden
        }

        img{
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
        }
    </style>
    <!-- Mani section header and breadcrumb -->
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    <span>{{ get_phrase('Blog') }}</span>
                </h4>
                <a href="{{ route('admin.blogs') }}" class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                    <span class="fi-rr-arrow-left"></span>
                    <span>{{ get_phrase('Back') }}</span>
                </a>
            </div>
        </div>
    </div>


    <div class="row ">
        <div class="col-md-8">
            <div class="ol-card p-4">
                <div class="ol-card-body">
                    <form action="{{ route('admin.blog.store') }}" method="post" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                            <div class="col-md-8">
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label" for="title">{{ get_phrase('Title') }}</label>
                                    <input type="text" class="form-control ol-form-control" name="title" id="title" placeholder="{{ get_phrase('Enter blog title') }}" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label" for="blog_category_id">{{ get_phrase('Category') }}</label>
                                    <select class="form-control ol-form-control ol-select2" data-toggle="select2" name="category_id" id="blog_category_id" required>
                                        <option value="">{{ get_phrase('Select a category') }}</option>
                                        @foreach ($category as $row)
                                            <option value="{{ $row->id }}">{{ $row->title }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label" for="slug">{{ get_phrase('URL Slug') }}</label>
                                    <input type="text" class="form-control ol-form-control" name="slug" id="slug" placeholder="url-slug-cua-bai-viet">
                                    <small class="text-muted">{{ get_phrase('Leave empty to auto-generate from title') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label" for="status">{{ get_phrase('Status') }}</label>
                                    <select class="form-control ol-form-control" name="status" id="status">
                                        <option value="0">{{ get_phrase('Draft') }}</option>
                                        <option value="1">{{ get_phrase('Published') }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="fpb-7 mb-3">
                            <label class="form-label ol-form-label" for="excerpt">{{ get_phrase('Excerpt') }}</label>
                            <textarea name="excerpt" class="form-control ol-form-control" id="excerpt" rows="3" placeholder="{{ get_phrase('Short description for preview...') }}"></textarea>
                            <small class="text-muted">{{ get_phrase('Brief summary displayed in blog list') }}</small>
                        </div>

                        <div class="fpb-7 mb-3">
                            <label class="form-label ol-form-label" for="keywords">{{ get_phrase('Keywords') }}</label>
                            <input type="text" name="keywords" class="tagify ol-form-control w-100" data-role="tagsinput">
                            <small class="text-muted">{{ get_phrase('Press Enter after each keyword') }}</small>
                        </div>

                        <div class="fpb-7 mb-3">
                            <label class="form-label ol-form-label" for="tinymce-description">{{ get_phrase('Description') }}</label>
                            <textarea name="description" class="form-control ol-form-control" id="tinymce-description"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label" for="thumbnail">{{ get_phrase('Featured Image') }}</label>
                                    <div class="image_preview">
                                        <img src="{{ get_image() }}" id="preview_thumbnail" alt="blog-thumbnail">
                                    </div>
                                    <input type="file" name="thumbnail" id="thumbnail" class="form-control image-upload" accept="image/*">
                                    <small class="text-muted">{{ get_phrase('Recommended size: 800x600px') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="fpb-7 mb-3">
                                    <label class="form-label ol-form-label">{{ get_phrase('Blog Settings') }}</label>

                                    <div class="d-flex align-items-center gap-3 mb-3">
                                        <input type="checkbox" id="is_popular" value="1" name="is_popular" class="form-check-input">
                                        <label for="is_popular" class="form-check-label">{{ get_phrase('Mark as Popular') }}</label>
                                    </div>

                                    <div class="fpb-7 mb-3">
                                        <label class="form-label ol-form-label" for="publish_date">{{ get_phrase('Publish Date') }}</label>
                                        <input type="datetime-local" class="form-control ol-form-control" name="publish_date" id="publish_date" value="{{ date('Y-m-d\TH:i') }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4">

                        <h3 class="title fs-16px mb-3">
                            <i class="fi-rr-search me-2"></i>
                            {{ get_phrase('SEO Settings') }}
                        </h3>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="fpb-7 mb-3">
                                    <label for="meta_title" class="form-label ol-form-label">{{ get_phrase('Meta Title') }}</label>
                                    <input class="form-control ol-form-control" id="meta_title" name="meta_title" type="text" placeholder="{{ get_phrase('SEO title for search engines') }}" />
                                    <small class="text-muted">{{ get_phrase('Recommended: 50-60 characters') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="fpb-7 mb-3">
                                    <label for="meta_keywords" class="form-label ol-form-label">{{ get_phrase('Meta Keywords') }}</label>
                                    <input type="text" name="meta_keywords" class="tagify ol-form-control w-100" id="meta_keywords" placeholder="SEO keywords" />
                                    <small class="text-muted">{{ get_phrase('Press Enter after each keyword') }}</small>
                                </div>
                            </div>
                        </div>

                        <div class="fpb-7 mb-3">
                            <label for="meta_description" class="form-label ol-form-label">{{ get_phrase('Meta Description') }}</label>
                            <textarea class="form-control ol-form-control" id="meta_description" name="meta_description" rows="3" placeholder="{{ get_phrase('Brief description for search engines') }}"></textarea>
                            <small class="text-muted">{{ get_phrase('Recommended: 150-160 characters') }}</small>
                        </div>

                        <div class="fpb-7 mb-3">
                            <button type="submit" class="ol-btn-primary">{{ get_phrase('Add blog') }}</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('css')
    <style>
        .tox-tinymce {
            border-radius: 8px !important;
        }
    </style>
@endpush

@push('js')
    <!-- TinyMCE CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.2/tinymce.min.js"></script>

    <script>
        "use strict";
        $(function() {
            // Image preview functionality
            $('#thumbnail').change(function(e) {
                e.preventDefault();
                var x = URL.createObjectURL(event.target.files[0]);
                $('#preview_thumbnail').attr('src', x);
            });

            // Auto-generate slug from title
            $('#title').on('input', function() {
                var title = $(this).val();
                var slug = title.toLowerCase()
                    .replace(/[^\w\s-]/g, '') // Remove special characters
                    .replace(/\s+/g, '-') // Replace spaces with hyphens
                    .replace(/-+/g, '-') // Replace multiple hyphens with single
                    .trim('-'); // Remove leading/trailing hyphens

                $('#slug').val(slug);
            });

            // Character counter for meta fields
            $('#meta_title').on('input', function() {
                var length = $(this).val().length;
                var color = length > 60 ? 'text-danger' : (length > 50 ? 'text-warning' : 'text-success');
                $(this).next('small').html('<span class="' + color + '">' + length + '/60 characters</span>');
            });

            $('#meta_description').on('input', function() {
                var length = $(this).val().length;
                var color = length > 160 ? 'text-danger' : (length > 150 ? 'text-warning' : 'text-success');
                $(this).next('small').html('<span class="' + color + '">' + length + '/160 characters</span>');
            });

            // Initialize TinyMCE with enhanced Word support
            tinymce.init({
                selector: '#tinymce-description',
                height: 500,

                // Plugins hỗ trợ copy từ Word
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount', 'paste',
                    'importcss', 'autosave', 'save', 'textcolor', 'colorpicker'
                ],

                // Toolbar với đầy đủ chức năng
                toolbar: 'undo redo | blocks fontfamily fontsize | bold italic underline strikethrough | ' +
                        'forecolor backcolor | align lineheight | ' +
                        'bullist numlist outdent indent | removeformat | ' +
                        'table tabledelete | tableprops tablerowprops tablecellprops | ' +
                        'tableinsertrowbefore tableinsertrowafter tabledeleterow | ' +
                        'tableinsertcolbefore tableinsertcolafter tabledeletecol | ' +
                        'link image media | code preview fullscreen',

                // Cấu hình Paste để chấp nhận mọi nội dung từ Word
                paste_as_text: false,
                paste_auto_cleanup_on_paste: false,
                paste_data_images: true,
                paste_enable_default_filters: false,
                paste_remove_spans: false,
                paste_remove_styles: false,
                paste_remove_styles_if_webkit: false,
                paste_strip_class_attributes: 'none',
                paste_merge_formats: true,
                paste_convert_word_fake_lists: true,
                paste_retain_style_properties: 'all',

                // Cho phép tất cả các element và attribute
                valid_elements: '*[*]',
                valid_children: '+body[style],+div[p|br|span]',
                extended_valid_elements: '*[*]',

                // Xử lý paste content từ Word
                paste_preprocess: function(plugin, args) {
                    console.log('Đang paste nội dung từ Word...');

                    // Chỉ xóa một số thuộc tính không cần thiết của Office
                    args.content = args.content.replace(/<!--[\s\S]*?-->/g, ''); // Xóa comment
                },

                paste_postprocess: function(plugin, args) {
                    console.log('Hoàn thành paste từ Word');
                },

                // Font và style
                font_family_formats: 'Arial=arial,helvetica,sans-serif; Times New Roman=times new roman,times,serif; Courier New=courier new,courier,monospace; Verdana=verdana,geneva,sans-serif; Georgia=georgia,serif; Palatino=palatino linotype,book antiqua,palatino,serif; Tahoma=tahoma,arial,helvetica,sans-serif; Trebuchet MS=trebuchet ms,geneva,sans-serif; Lucida Sans=lucida sans unicode,lucida grande,sans-serif; Impact=impact,chicago,sans-serif; Comic Sans MS=comic sans ms,cursive',

                font_size_formats: '8pt 9pt 10pt 11pt 12pt 14pt 16pt 18pt 20pt 22pt 24pt 26pt 28pt 30pt 32pt 34pt 36pt 38pt 40pt 42pt 44pt 46pt 48pt 50pt 52pt 54pt 56pt 58pt 60pt 62pt 64pt 66pt 68pt 70pt 72pt 74pt 76pt 78pt 80pt',

                // Cấu hình bảng
                table_default_attributes: {
                    border: '1'
                },
                table_default_styles: {
                    'border-collapse': 'collapse',
                    'width': '100%'
                },

                // Cấu hình khác
                menubar: 'file edit view insert format tools table help',
                contextmenu: 'link image table',
                branding: false,
                promotion: false,

                // Xử lý khi editor sẵn sàng
                setup: function(editor) {
                    editor.on('init', function() {
                        console.log('TinyMCE đã sẵn sàng cho blog editor!');
                    });

                    editor.on('paste', function(e) {
                        console.log('Đã paste nội dung vào blog editor');
                    });
                }
            });
        });
    </script>
@endpush

@extends('layouts.admin')
@push('title', 'CRM Dashboard Thông Minh')
@push('meta')@endpush
@push('css')
<style>
    .smart-crm-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .smart-crm-card:hover {
        transform: translateY(-5px);
    }

    .pipeline-stage {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-left: 4px solid #007bff;
    }

    .pipeline-stage.new { border-left-color: #17a2b8; }
    .pipeline-stage.contacted { border-left-color: #007bff; }
    .pipeline-stage.nurturing { border-left-color: #ffc107; }
    .pipeline-stage.qualified { border-left-color: #28a745; }
    .pipeline-stage.proposal { border-left-color: #6f42c1; }
    .pipeline-stage.won { border-left-color: #28a745; }
    .pipeline-stage.lost { border-left-color: #dc3545; }

    .lead-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .lead-card:hover {
        background: #e3f2fd;
        border-color: #2196f3;
        transform: translateX(5px);
    }

    .ai-suggestion {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        border-left: 4px solid #e91e63;
    }

    .quick-action-btn {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;
        border-radius: 25px;
        color: white;
        padding: 8px 20px;
        margin: 5px;
        transition: all 0.3s ease;
    }

    .quick-action-btn:hover {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .revenue-chart {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }

    .smart-filter {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 10px;
        padding: 20px;
        color: white;
        margin-bottom: 20px;
    }

    .notification-bell {
        position: relative;
        display: inline-block;
    }

    .notification-count {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #dc3545;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Header thông minh -->
    <div class="smart-crm-card">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-2">🚀 CRM Dashboard Thông Minh</h2>
                <p class="mb-0">Quản lý khách hàng hiệu quả với AI và tự động hóa</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="notification-bell">
                    <i class="fi-rr-bell fs-3"></i>
                    <span class="notification-count">3</span>
                </div>
                <span class="ms-3">{{ now()->format('d/m/Y H:i') }}</span>
            </div>
        </div>
    </div>

    <!-- Thống kê nhanh -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary fs-1 mb-2">
                        <i class="fi-rr-users"></i>
                    </div>
                    <h3 class="text-primary">{{ $stats['total_leads'] ?? 0 }}</h3>
                    <p class="text-muted mb-0">Tổng khách hàng</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success fs-1 mb-2">
                        <i class="fi-rr-dollar"></i>
                    </div>
                    <h3 class="text-success">{{ number_format($stats['total_revenue'] ?? 0, 0, ',', '.') }}</h3>
                    <p class="text-muted mb-0">Doanh thu (VNĐ)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning fs-1 mb-2">
                        <i class="fi-rr-chart-line-up"></i>
                    </div>
                    <h3 class="text-warning">{{ $stats['conversion_rate'] ?? 0 }}%</h3>
                    <p class="text-muted mb-0">Tỷ lệ chuyển đổi</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-info fs-1 mb-2">
                        <i class="fi-rr-target"></i>
                    </div>
                    <h3 class="text-info">{{ $stats['opportunities'] ?? 0 }}</h3>
                    <p class="text-muted mb-0">Cơ hội đang mở</p>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Suggestions -->
    <div class="ai-suggestion">
        <h5><i class="fi-rr-brain me-2"></i>Gợi ý thông minh từ AI</h5>
        <div class="row">
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <i class="fi-rr-phone-call text-primary me-2"></i>
                    <span>Có 5 khách hàng cần follow-up hôm nay</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <i class="fi-rr-time-fast text-warning me-2"></i>
                    <span>3 cơ hội sắp hết hạn, cần chốt gấp</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <i class="fi-rr-star text-success me-2"></i>
                    <span>Khách hàng tiềm năng cao: Nguyễn Văn A</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Pipeline bán hàng -->
    <div class="row">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5><i class="fi-rr-workflow me-2"></i>Pipeline Bán Hàng Thông Minh</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach(['new' => 'Lead mới', 'contacted' => 'Đã tiếp cận', 'nurturing' => 'Đang vun đắp', 'qualified' => 'Tiềm năng cao'] as $stage => $label)
                        <div class="col-md-3">
                            <div class="pipeline-stage {{ $stage }}">
                                <h6 class="mb-3">{{ $label }}</h6>
                                <div class="text-center mb-2">
                                    <span class="badge bg-primary">{{ $pipeline_data[$stage] ?? 0 }} khách</span>
                                </div>
                                <!-- Sample lead cards -->
                                <div class="lead-card" onclick="openLeadDetail(1)">
                                    <small class="text-muted">Nguyễn Văn A</small><br>
                                    <small>Quan tâm khóa PHP</small>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-3">
                <div class="card-header bg-transparent">
                    <h6><i class="fi-rr-magic-wand me-2"></i>Hành động nhanh</h6>
                </div>
                <div class="card-body">
                    <button class="quick-action-btn btn btn-sm w-100 mb-2" onclick="quickCall()">
                        <i class="fi-rr-phone-call me-2"></i>Gọi điện nhanh
                    </button>
                    <button class="quick-action-btn btn btn-sm w-100 mb-2" onclick="quickEmail()">
                        <i class="fi-rr-envelope me-2"></i>Gửi email
                    </button>
                    <button class="quick-action-btn btn btn-sm w-100 mb-2" onclick="createOpportunity()">
                        <i class="fi-rr-add me-2"></i>Tạo cơ hội
                    </button>
                    <button class="quick-action-btn btn btn-sm w-100" onclick="scheduleFollowUp()">
                        <i class="fi-rr-calendar me-2"></i>Lên lịch follow-up
                    </button>
                </div>
            </div>

            <!-- Today's Tasks -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h6><i class="fi-rr-list me-2"></i>Nhiệm vụ hôm nay</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Gọi cho Nguyễn Văn A</span>
                        <span class="badge bg-warning">10:00</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Email báo giá cho Trần Thị B</span>
                        <span class="badge bg-info">14:00</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Họp với khách hàng C</span>
                        <span class="badge bg-success">16:00</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script>
function openLeadDetail(leadId) {
    // Mở modal chi tiết lead
    console.log('Opening lead detail for ID:', leadId);
}

function quickCall() {
    // Mở modal gọi điện nhanh
    console.log('Quick call initiated');
}

function quickEmail() {
    // Mở modal email nhanh
    console.log('Quick email initiated');
}

function createOpportunity() {
    // Mở modal tạo cơ hội
    console.log('Create opportunity initiated');
}

function scheduleFollowUp() {
    // Mở modal lên lịch
    console.log('Schedule follow-up initiated');
}
</script>
@endpush

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T<PERSON>h năng chưa <PERSON><PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .error-container {
            max-width: 500px;
            text-align: center;
            background-color: white;
            border-radius: 12px;
            padding: 40px 30px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.06);
        }
        .error-icon {
            font-size: 60px;
            color: #ffc107;
            margin-bottom: 20px;
        }
        h1 {
            font-size: 24px;
            color: #343a40;
            margin-bottom: 15px;
        }
        p {
            color: #6c757d;
            margin-bottom: 25px;
            line-height: 1.6;
        }
        .btn-addon {
            background-color: #4361ee;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        .btn-addon:hover {
            background-color: #3a56d4;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(59, 97, 238, 0.2);
        }
        .addon-name {
            background-color: #f0f7ff;
            color: #4361ee;
            padding: 4px 10px;
            border-radius: 20px;
            font-family: monospace;
            font-weight: 600;
            margin: 0 5px;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="bi bi-lock-fill"></i>
        </div>
        <h1>Tính năng chưa được kích hoạt</h1>
        <p>
            Tính năng <strong>{{ $feature_name ?? 'này' }}</strong> yêu cầu cài đặt và kích hoạt addon 
            <span class="addon-name">{{ $addon_name ?? 'unknown' }}</span> trước khi sử dụng.
        </p>
        <p>Vui lòng liên hệ quản trị viên để kích hoạt tính năng này.</p>
    </div>
</body>
</html> 
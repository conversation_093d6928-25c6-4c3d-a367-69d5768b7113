@php
    $course_progress_out_of_100 = progress_bar($course_details->id);
    if (isset($_GET['tab'])) {
        $tab = $_GET['tab'];
    } elseif (Session::has('forum_tab')) {
        $tab = Session::get('forum_tab');
    } else {
        $tab = 'summary';
    }
@endphp
@php
    $pro = false;
    $enrollment_status = enroll_status(isset($course_details)?$course_details->id:0, Auth()->user()->id);
    if ($enrollment_status=='valid' || Auth()->user()->role == 'admin'){
        $pro = true;
    }

    // Ensure $enroll_status is available for all tabs - use the same logic as main page
    if (auth()->user()->role == 'admin' || $enrollment_status == 'valid') {
        $enroll_status = true;
    } else {
        $enroll_status = false;
    }
@endphp
<ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
    <li data-tab="course-menu" class="nav-item course-menu-tab" role="presentation">
        <button
                class="nav-link"
                id="pills-course-menu-tab"
                data-bs-toggle="pill"
                data-bs-target="#pills-course-menu"
                type="button"
                role="tab"
                aria-controls="pills-course-menu"
                aria-selected="true"
        >
            <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 18 18"
                    fill="none"
            >
                <path
                        d="M4.64791 9.25135L1.76932 7.64525C1.41023 7.4449 1.41023 6.8051 1.76932 6.60475L7.52651 3.39255C8.4646 2.86915 9.5354 2.86915 10.4735 3.39255L16.2307 6.60475C16.5898 6.8051 16.5898 7.4449 16.2307 7.64525L13.3521 9.25135M4.64791 9.25135L7.52651 10.8574C8.46459 11.3809 9.5354 11.3809 10.4735 10.8574L13.3521 9.25135M4.64791 9.25135L3.375 12.75C3.375 12.75 5.2178 15 9 15C12.7822 15 14.625 12.75 14.625 12.75L13.3521 9.25135"
                        stroke="currentColor"
                        stroke-width="1.5"
                        style="fill: transparent"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                />
            </svg>
            <span>Danh sách bài học</span>
        </button>
    </li>
    <li data-tab="summary" class="nav-item" role="presentation">
        <button class="nav-link @if ($tab == 'summary') active @endif" id="pills-summary-tab" data-bs-toggle="pill"
                data-bs-target="#pills-summary" type="button" role="tab" aria-controls="pills-summary"
                aria-selected="true">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path
                    d="M6.75 4.5C6.33579 4.5 6 4.83579 6 5.25C6 5.66421 6.33579 6 6.75 6V4.5ZM11.25 6C11.6642 6 12 5.66421 12 5.25C12 4.83579 11.6642 4.5 11.25 4.5V6ZM5.25 15.75C5.25 16.1642 5.58579 16.5 6 16.5C6.41421 16.5 6.75 16.1642 6.75 15.75H5.25ZM5.25 13.5C4.83579 13.5 4.5 13.1642 4.5 12.75H3C3 13.9926 4.00736 15 5.25 15V13.5ZM5.25 10.5C4.00736 10.5 3 11.5074 3 12.75H4.5C4.5 12.3358 4.83579 12 5.25 12V10.5ZM5.25 3H13.875V1.5H5.25V3ZM13.875 3C13.6679 3 13.5 2.83211 13.5 2.625H15C15 2.00368 14.4963 1.5 13.875 1.5V3ZM5.25 1.5C4.00736 1.5 3 2.50736 3 3.75H4.5C4.5 3.33579 4.83579 3 5.25 3V1.5ZM3 3.75V12.75H4.5V3.75H3ZM13.5 2.625V11.25H15V2.625H13.5ZM6.75 6H11.25V4.5H6.75V6ZM5.25 12H14.25V10.5H5.25V12ZM13.875 13.5H5.25V15H13.875V13.5ZM13.5 13.875C13.5 13.6679 13.6679 13.5 13.875 13.5V15C14.4963 15 15 14.4963 15 13.875H13.5ZM13.5 11.25V13.875H15V11.25H13.5ZM5.25 12.75V15.75H6.75V12.75H5.25Z"
                    fill="currentColor"></path>
            </svg>

            <span>{{ get_phrase('Summary') }}</span>
        </button>
    </li>
    @if(addon_check('my.live_class'))
    <li data-tab="live-class" class="nav-item" role="presentation">
        <button class="nav-link @if ($tab == 'live-class') active @endif" id="pills-live-class-tab"
                data-bs-toggle="pill" data-bs-target="#pills-live-class" type="button" role="tab"
                aria-controls="pills-live-class" aria-selected="true">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path
                    d="M14.25 4.5L3.75 4.49997M5.25 2.25H12.75M3.75 15.75H14.25C15.4926 15.75 16.5 14.7426 16.5 13.5V8.99997C16.5 7.75733 15.4926 6.74997 14.25 6.74997H3.75C2.50736 6.74997 1.5 7.75733 1.5 8.99997V13.5C1.5 14.7426 2.50736 15.75 3.75 15.75ZM8.25 10.125V12.375L10.5 11.25L8.25 10.125Z"
                    stroke="currentColor" stroke-width="1.5" style="fill: transparent" stroke-linecap="round"
                    stroke-linejoin="round"></path>
            </svg>
            <span>{{ get_phrase('Live class') }}</span>
        </button>
    </li>
    @endif
    @if(addon_check('my.certificate'))
    <li data-tab="certificate" class="nav-item" role="presentation">
        <button class="nav-link @if ($tab == 'certificate') active @endif" id="pills-certificate-tab"
                data-bs-toggle="pill" data-bs-target="#pills-certificate" type="button" role="tab"
                aria-controls="pills-certificate" aria-selected="true">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path
                    d="M9 7.87502C8.26131 7.87502 7.52263 7.81381 6.75 7.6914C5.61019 7.51081 4.39651 7.19702 3 6.75002C3 6.75002 1.5 7.69016 1.5 9.75002C1.5 11.8099 3 12.75 3 12.75C7.68629 11.25 10.3137 11.25 15 12.75C15 12.75 16.5 11.8099 16.5 9.75002C16.5 7.69016 15 6.75002 15 6.75002C13.6035 7.19702 12.3898 7.51081 11.25 7.6914C10.4774 7.81381 9.73869 7.87502 9 7.87502ZM9 7.87502V6.75002M9 7.87502L11.3993 7.63509C12.1661 7.55841 12.75 6.91316 12.75 6.14254V6.00002C12.75 5.17159 12.0784 4.50002 11.25 4.50002C10.0074 4.50002 9 5.50738 9 6.75002M9 7.87502L6.60074 7.63507C5.83394 7.55839 5.25 6.91315 5.25 6.14252V6C5.25 5.17157 5.92157 4.5 6.75 4.5C7.99264 4.5 9 5.50738 9 6.75002"
                    stroke="currentColor" stroke-width="1.5" stroke-linejoin="round"
                    style="fill: transparent"></path>
                <path
                    d="M7.82008 14.9454L8.86742 13.9111C8.94064 13.8388 9.05936 13.8388 9.13258 13.9111L10.1799 14.9454C10.298 15.062 10.5 14.9794 10.5 14.8145V8.24068C10.5 7.83161 10.1642 7.5 9.75 7.5H8.25C7.83579 7.5 7.5 7.83161 7.5 8.24068V14.8145C7.5 14.9794 7.70196 15.062 7.82008 14.9454Z"
                    fill="currentColor"></path>
            </svg>
            <span>{{ get_phrase('Certificate') }}</span>
        </button>
    </li>
    @endif
    <li data-tab="forum" class="nav-item" role="presentation">
        <button class="nav-link @if ($tab == 'forum') active @endif" id="pills-forum-tab" data-bs-toggle="pill"
                data-bs-target="#pills-forum" type="button" role="tab" aria-controls="pills-forum"
                aria-selected="true">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path
                    d="M6.75 6.75H9M6.75 9.75H11.25M9.39474 15.7105C13.3189 15.7105 16.5 12.5294 16.5 8.60526C16.5 4.68113 13.3189 1.5 9.39474 1.5C5.47061 1.5 2.28947 4.68113 2.28947 8.60526C2.28947 9.47752 2.44665 10.3131 2.73422 11.0851C2.9242 11.5952 2.97978 12.1548 2.80432 12.67L2.15533 14.5757C1.88775 15.3614 2.6386 16.1122 3.42432 15.8447L5.33 15.1957C5.84522 15.0202 6.40484 15.0758 6.91488 15.2658C7.68694 15.5534 8.52248 15.7105 9.39474 15.7105Z"
                    stroke="currentColor" style="fill: transparent" stroke-width="1.5"
                    stroke-linecap="round"></path>
            </svg>
            <span>{{ get_phrase('Forum') }}</span>
        </button>
    </li>
</ul>

<div class="tab-content" id="pills-tabContent">
    @include('course_player.summary.index')
    @include('course_player.live_class.index')
    @include('course_player.certificate.index')
    @include('course_player.forum.index')
</div>

@push('js')
    <script>
        $(document).ready(function () {
            $("#pills-tab .nav-item").click(function () {
                const currentPinTab = $(this).data("tab");
                if (currentPinTab === "course-menu") {
                    $(".elearning-course-playlist").css("display", "block");
                } else {
                    $(".elearning-course-playlist").css("display", "none");
                }
            });
        });
    </script>
    <script>
        "use strict";
        $(document).ready(function () {
            $('button.nav-link').on('click', function (e) {
                e.preventDefault();
                let tab = $(this).data('bs-target');
                $.ajax({
                    type: "get",
                    url: "{{ route('forum.tab.active') }}",
                    data: {
                        tab: tab
                    },
                    success: function (response) {
                        console.log(response);
                    }
                });
            });
        });
        $(document).ready(function () {
            function checkMobile() {
                if ($(window).width() < 768) {
                    handleMobileView();
                }
            }

            function handleMobileView() {
                $('#pills-course-menu-tab').trigger('click');
                $('#pills-course-menu-tab').tab('show');
            }

            checkMobile();

            $(window).resize(function () {
                checkMobile();
            });
        });
    </script>
@endpush

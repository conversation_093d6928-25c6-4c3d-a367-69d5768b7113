@php
    $sections = App\Models\Section::where('course_id', $course_details->id)
        ->orderBy('sort')
        ->get();

    $completed_lesson = json_decode(
        App\Models\Watch_history::where('course_id', $course_details->id)
            ->where('student_id', Auth()->user()->id)
            ->value('completed_lesson'),
        true,
    ) ?? [];
    $active_section = App\Models\Lesson::where('id', $history->watching_lesson_id ?? '')->value('section_id');

    $lesson_history = App\Models\Watch_history::where('course_id', $course_details->id)
        ->where('student_id', auth()->user()->id)
        ->firstOrNew();
    $completed_lesson_arr = json_decode($lesson_history->completed_lesson, true);
    $complated_lesson = is_array($completed_lesson_arr) ? count($completed_lesson_arr) : 0;
    $course_progress_out_of_100 = progress_bar($course_details->id);

    $user_id = Auth()->user()->id;
    $is_course_instructor = is_course_instructor($course_details->id, $user_id);

    $is_locked = 0;
    $locked_lesson_ids = array();

@endphp


<div class="course-list-container sticky-top"
     data-tour="step: 1; title: Chọn bài học; content: Chọn bài học để xem video">
    <ul class="course-header nav nav-pills" role="tablist">
        <li class="nav-item has-divider course-menu d-flex align-items-center" role="presentation">
            <a class="nav-link active" href="#" data-bs-toggle="tab" id="course-menu-list-tab"
               data-bs-target="#course-menu-list" role="tab" aria-controls="course-menu-list"
               aria-selected="false" tabindex="-1">
                  <span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                      <path
                          d="M3.83333 2.91667C3.83333 2.68655 4.01988 2.5 4.25 2.5H8.41667C8.64679 2.5 8.83333 2.68655 8.83333 2.91667V7.08333C8.83333 7.31345 8.64679 7.5 8.41667 7.5H4.25C4.01988 7.5 3.83333 7.31345 3.83333 7.08333V2.91667Z"
                          fill="currentColor"></path>
                      <path
                          d="M13 12.0833C13 11.8532 13.1865 11.6667 13.4167 11.6667H17.5833C17.8135 11.6667 18 11.8532 18 12.0833V16.25C18 16.4801 17.8135 16.6667 17.5833 16.6667H13.4167C13.1865 16.6667 13 16.4801 13 16.25V12.0833Z"
                          fill="currentColor"></path>
                      <path
                          d="M6.62537 10.9543C6.46408 10.793 6.20259 10.793 6.0413 10.9543L3.12096 13.8746C2.95968 14.0359 2.95968 14.2974 3.12096 14.4587L6.0413 17.379C6.20259 17.5403 6.46408 17.5403 6.62537 17.379L9.5457 14.4587C9.70699 14.2974 9.70699 14.0359 9.5457 13.8746L6.62537 10.9543Z"
                          fill="currentColor"></path>
                      <path
                          d="M15.9167 3.33333C15.9167 2.8731 15.5436 2.5 15.0833 2.5C14.6231 2.5 14.25 2.8731 14.25 3.33333V4.375C14.25 4.49006 14.1567 4.58333 14.0417 4.58333H13C12.5398 4.58333 12.1667 4.95643 12.1667 5.41667C12.1667 5.8769 12.5398 6.25 13 6.25H14.0417C14.1567 6.25 14.25 6.34327 14.25 6.45833V7.5C14.25 7.96024 14.6231 8.33333 15.0833 8.33333C15.5436 8.33333 15.9167 7.96024 15.9167 7.5V6.45833C15.9167 6.34327 16.0099 6.25 16.125 6.25H17.1667C17.6269 6.25 18 5.8769 18 5.41667C18 4.95643 17.6269 4.58333 17.1667 4.58333H16.125C16.0099 4.58333 15.9167 4.49006 15.9167 4.375V3.33333Z"
                          fill="currentColor"></path>
                    </svg>
                    <span>{{ get_phrase('Course Curriculum') }}</span>
                  </span>
                <img class="course-divider"
                     src="{{ asset('assets/frontend/course-shopee/assets/images/course-divider.svg') }}"
                     alt="">
            </a>
        </li>
        <li class="nav-item course-update" role="presentation">
            <a class="nav-link" href="#" data-bs-toggle="tab" id="course-update-tab"
               data-bs-target="#course-update" role="tab" aria-controls="course-menu-list"
               aria-selected="true">
                  <span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                      <path
                          d="M7.88592 1.79285C7.94818 1.62459 8.18617 1.62459 8.24843 1.79285L9.86331 6.15701C9.88289 6.20991 9.9246 6.25162 9.9775 6.27119L14.3417 7.88608C14.5099 7.94834 14.5099 8.18632 14.3417 8.24858L9.9775 9.86347C9.9246 9.88304 9.88289 9.92475 9.86332 9.97765L8.24843 14.3418C8.18617 14.5101 7.94818 14.5101 7.88592 14.3418L6.27104 9.97765C6.25146 9.92475 6.20975 9.88304 6.15685 9.86347L1.7927 8.24858C1.62444 8.18632 1.62444 7.94834 1.7927 7.88608L6.15685 6.27119C6.20975 6.25162 6.25146 6.20991 6.27104 6.15701L7.88592 1.79285Z"
                          fill="currentColor"></path>
                      <path
                          d="M14.6502 11.4562C14.7125 11.2879 14.9505 11.2879 15.0127 11.4562L15.8446 13.7043C15.8642 13.7572 15.9059 13.7989 15.9588 13.8185L18.207 14.6504C18.3752 14.7126 18.3752 14.9506 18.207 15.0129L15.9588 15.8448C15.9059 15.8644 15.8642 15.9061 15.8446 15.959L15.0127 18.2071C14.9505 18.3754 14.7125 18.3754 14.6502 18.2071L13.8183 15.959C13.7988 15.9061 13.7571 15.8644 13.7042 15.8448L11.456 15.0129C11.2877 14.9506 11.2877 14.7126 11.456 14.6504L13.7042 13.8185C13.7571 13.7989 13.7988 13.7572 13.8183 13.7043L14.6502 11.4562Z"
                          fill="currentColor"></path>
                    </svg>
                    <span>{{ get_phrase('Recently Updated') }}</span>
                  </span>
            </a>
        </li>
    </ul>

    <div class="course-section tab-content">
        <div class="tab-pane active" id="course-menu-list" aria-labelledby="course-menu-list-tab"
             tabindex="0">
            <div class="accordion" id="course-items-accordion">
                @foreach ($sections as $section)
                    @php
                        $lessons = App\Models\Lesson::where('section_id', $section->id)
                            ->orderBy('sort')
                            ->get();

                        $section_completed = 0;
                        $section_total = count($lessons);

                        foreach ($lessons as $lesson) {
                            if (in_array($lesson->id, $completed_lesson_arr)) {
                                $section_completed++;
                            }
                        }

                        $section_progress = $section_total > 0 ? round(($section_completed / $section_total) * 100) : 0;
                    @endphp
                    <div class="accordion-item">
                        <h3 class="accordion-header">
                            <button
                                class="accordion-button course-item-accordion-header @if ($active_section != $section->id) collapsed @endif"
                                type="button" data-bs-toggle="collapse"
                                data-bs-target="#course-item-{{ $section->id }}"
                                aria-expanded="@if ($section->id == $active_section) true @else false @endif"
                                aria-controls="course-item-{{ $section->id }}">
                                <span class="course-section-header d-flex justify-content-between align-items-center">
                                    <span class="course-item-accordion-header-title">
                                        {{ $section->title }}
                                    </span>
                                    <span class="toggle-icon">
                                        <img
                                            src="{{ asset('assets/frontend/course-shopee/assets/images/chevron-down.svg') }}"
                                            alt="">
                                    </span>
                                </span>
                                <span class="course-meta">
                                    <span class="total-time">{{ get_phrase('Total time') }}:
                                        <span
                                            class="total-time-value">{{ get_total_duration_of_lesson_by_section_id($section->id) }}</span>
                                    </span>
                                    <span class="divider"></span>
                                    <span class="lesson-count">{{ get_phrase('Lessons') }}:
                                        <span class="lesson-count-value">{{ $section_total }}</span>
                                    </span>
                                    <span class="divider"></span>
{{--                                    <span class="progress-count">{{ get_phrase('Progress') }}:--}}
                                    {{--                                        <span class="progress-count-value">{{ $section_progress }}%</span>--}}
                                    {{--                                    </span>--}}
                                </span>
                                <span class="course-item-accordion-header-icon">
                                    <img
                                        src="{{ asset('assets/frontend/course-shopee/assets/icons/chevron-down.svg') }}"
                                        alt=""
                                        width="24" height="24">
                                </span>
                            </button>
                        </h3>
                        <div id="course-item-{{ $section->id }}"
                             class="accordion-collapse collapse @if ($section->id == $active_section) show @endif"
                             data-bs-parent="#course-items-accordion">
                            <div class="accordion-body">
                                <div class="course-lessons">
                                    @foreach ($lessons as $lesson)
                                        @php
                                            $type = $lesson->lesson_type;
                                            $is_completed = in_array($lesson->id, $completed_lesson_arr);
                                            $is_active = isset($history->watching_lesson_id) && $lesson->id == $history->watching_lesson_id;
                                        @endphp
                                        <a href="{{ route('course.player', ['slug' => $course_details->slug, 'id' => $lesson->id]) }}">
                                            <div class="lesson-item @if ($is_active) active @endif">
                                                @if ($lesson->thumbnail)
                                                    <div class="lesson-thumbnail">
                                                        <img src="{{ asset('uploads/thumbnails/'.$lesson->thumbnail) }}"
                                                             alt="{{ $lesson->title }}" class="img-fluid rounded"
                                                             style="width: 90px; height: 56px; object-fit: cover;">
                                                    </div>
                                                @endif
                                                <div class="lesson-content">
                                                    <div class="lesson-content-header">

                                                        @if (isset($history->watching_lesson_id) && $lesson->id == $history->watching_lesson_id)
                                                            <div class="lesson-thumbnail">
                                                                <img
                                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}"
                                                                    alt="Lesson thumbnail" width="24" height="24">
                                                            </div>
                                                        @endif
                                                        <a href="{{ route('course.player', ['slug' => $course_details->slug, 'id' => $lesson->id]) }}">

                                                            @if(!$enroll_status)
                                                                @if($lesson->hide_title == 1)
                                                                    Nâng cấp để xem bài học
                                                                @else
                                                                    {{ $lesson->title }}
                                                                @endif
                                                            @else
                                                                {{ $lesson->title }}
                                                            @endif
                                                        </a>
                                                    </div>
                                                    <div class="lesson-meta">
                                                        <div class="d-flex align-items-center">
                                                            @if($lesson->paid_lesson)
                                                                <span class="badge premium-trial-badge">
                                                                <img
                                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}"
                                                                    width="12.941px" height="12.941px" alt="Pro"/>
                                                                <span>PRO</span>
                                                            </span>
                                                            @elseif($lesson->trial_lesson)
                                                                <span class="badge pro-trial-badge">
                                                                <img
                                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}"
                                                                    width="12.941px" height="12.941px" alt="Pro"/>
                                                                <span>{{get_phrase('PRO TRIAL')}}</span>
                                                            </span>
                                                            @else
                                                                <span class="badge free-badge">
                                                                <img
                                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/coin.svg') }}"
                                                                    width="11" height="11" alt="Free"/>
                                                                <span>{{get_phrase('FREE')}}</span>
                                                            </span>
                                                            @endif

                                                            @if(isset($lesson->is_important) && $lesson->is_important)
                                                                <span class="badge important-badge">
                                                                <img
                                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/important.svg') }}"
                                                                    width="11" height="11" alt="Important"/>
                                                                <span>{{get_phrase('IMPORTANT')}}</span>
                                                            </span>
                                                            @endif
                                                        </div>

                                                        {{--                                                    @if($course_details->enable_drip_content)--}}
                                                        {{--                                                        @if($is_locked)--}}
                                                        {{--                                                            <div class="lesson-status locked">--}}
                                                        {{--                                                                <i class="fas fa-lock" title="{{ get_phrase('Complete previous lesson to unlock it') }}"--}}
                                                        {{--                                                                   data-bs-toggle="tooltip"></i>--}}
                                                        {{--                                                            </div>--}}
                                                        {{--                                                        @else--}}
                                                        {{--                                                            @if($is_completed)--}}
                                                        {{--                                                                <div class="lesson-status completed">--}}
                                                        {{--                                                                    <i class="fas fa-check-circle checkbox-icon" title="{{ get_phrase('Lesson completed') }}"></i>--}}
                                                        {{--                                                                </div>--}}
                                                        {{--                                                            @else--}}
                                                        {{--                                                                <div class="lesson-status">--}}
                                                        {{--                                                                    <i class="form-check-input flexCheckChecked mt-0" title="{{ get_phrase('Play Now') }}"></i>--}}
                                                        {{--                                                                </div>--}}
                                                        {{--                                                            @endif--}}
                                                        {{--                                                        @endif--}}
                                                        {{--                                                    @else--}}
                                                        {{--                                                        @if($is_completed)--}}
                                                        {{--                                                            <div class="lesson-status completed">--}}
                                                        {{--                                                                <i class="fas fa-check-circle checkbox-icon" title="{{ get_phrase('Lesson completed') }}"></i>--}}
                                                        {{--                                                            </div>--}}
                                                        {{--                                                        @else--}}
                                                        {{--                                                            <div class="lesson-status">--}}
                                                        {{--                                                                <i class="form-check-input flexCheckChecked mt-0" title="{{ get_phrase('Play Now') }}"></i>--}}
                                                        {{--                                                            </div>--}}
                                                        {{--                                                        @endif--}}
                                                        {{--                                                    @endif--}}

                                                        @if (lesson_durations($lesson->id) != '00:00:00')
                                                            <span class="duration">
                                                            <img
                                                                src="{{ asset('assets/frontend/course-shopee/assets/icons/clock.svg') }}"
                                                                alt="Clock" width="16" height="16">

                                                                <span>{{ lesson_durations($lesson->id) }}</span>

                                                        </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </a>
                                        @php
                                            if ($is_locked) {
                                                $locked_lesson_ids[] = $lesson->id;
                                            }

                                            if (
                                                !in_array($lesson->id, $completed_lesson_arr) &&
                                                !$is_locked &&
                                                $course_details->enable_drip_content == 1 &&
                                                auth()->user() &&
                                                !$is_course_instructor
                                            ) {
                                                $is_locked = 1;
                                            }
                                        @endphp
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
        <div class="tab-pane" id="course-update" aria-labelledby="course-update-tab" tabindex="0">
            <div class="accordion" id="course-items-accordion-update">
                @php
                    $recent_lessons = App\Models\Lesson::where('course_id', $course_details->id)
                        ->orderBy('created_at', 'desc')
                        ->limit(10)
                        ->get();
                @endphp

                @if($recent_lessons->count() > 0)
                    <div class="accordion-item">

                        <div id="recent-updates" class="accordion-collapse collapse show"
                             data-bs-parent="#course-items-accordion-update">
                            <div class="accordion-body">
                                <div class="course-lessons">
                                    @foreach ($recent_lessons as $lesson)
                                        @php
                                            $type = $lesson->lesson_type;
                                            $is_completed = in_array($lesson->id, $completed_lesson_arr);
                                            $is_active = isset($history->watching_lesson_id) && $lesson->id == $history->watching_lesson_id;
                                            $section = App\Models\Section::find($lesson->section_id);
                                        @endphp
                                        <div class="lesson-item @if ($is_active) active @endif">
                                            <div class="lesson-content">
                                                <div class="lesson-content-header">

                                                    <div class="lesson-thumbnail">
                                                        <img
                                                            src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}"
                                                            alt="Lesson thumbnail" width="24" height="24">
                                                    </div>
                                                    <a href="{{ route('course.player', ['slug' => $course_details->slug, 'id' => $lesson->id]) }}">

                                                        @if(!$enroll_status)
                                                            @if($lesson->hide_title == 1)
                                                                Nâng cấp để xem bài học
                                                            @else
                                                                {{ $lesson->title }}
                                                            @endif
                                                        @else
                                                            {{ $lesson->title }}
                                                        @endif
                                                    </a>
                                                </div>
                                                <div class="lesson-meta">

                                                    <div class="d-flex align-items-center">
                                                        @if($lesson->paid_lesson)
                                                            <span class="badge premium-trial-badge">
                                                                <img
                                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}"
                                                                    width="12.941px" height="12.941px" alt="Pro"/>
                                                                <span>PRO</span>
                                                            </span>
                                                        @elseif($lesson->trial_lesson)
                                                            <span class="badge pro-trial-badge">
                                                                <img
                                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}"
                                                                    width="12.941px" height="12.941px" alt="Pro"/>
                                                                <span>{{get_phrase('PRO TRIAL')}}</span>
                                                            </span>
                                                        @else
                                                            <span class="badge free-badge">
                                                                <img
                                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/coin.svg') }}"
                                                                    width="11" height="11" alt="Free"/>
                                                                <span>{{get_phrase('FREE')}}</span>
                                                            </span>
                                                        @endif

                                                        @if(isset($lesson->is_important) && $lesson->is_important)
                                                            <span class="badge important-badge">
                                                                <img
                                                                    src="{{ asset('assets/frontend/course-shopee/assets/icons/important.svg') }}"
                                                                    width="11" height="11" alt="Important"/>
                                                                <span>{{get_phrase('IMPORTANT')}}</span>
                                                            </span>
                                                        @endif
                                                    </div>

                                                    @if (lesson_durations($lesson->id) != '00:00:00')

                                                        <div class="update-time">
                                                            <img
                                                                src="{{ asset('assets/frontend/course-shopee/assets/icons/clock.svg') }}"
                                                                alt="Clock" width="16" height="16">
                                                            <span>{{ lesson_durations($lesson->id) }}</span>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="no-updates">
                        <p>{{ get_phrase('No recent updates available.') }}</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>


<form class="ajaxForm" action="{{ route('set.watch.history') }}" method="post" id="watch_history_form">
    @csrf
    <input type="hidden" class="course_id" name="course_id" value="{{ $course_details->id }}">
    <input type="hidden" class="lesson_id" name="lesson_id">
</form>

<script>
    window.currentLessonData = {!! json_encode($sections, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) !!};
</script>

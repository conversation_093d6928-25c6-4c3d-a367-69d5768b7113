<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light">
    <meta name="supported-color-schemes" content="light">
    <title><PERSON><PERSON><PERSON> ký khóa học thành công</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f7f9fc;
            -webkit-font-smoothing: antialiased;
        }
        .wrapper {
            width: 100%;
            table-layout: fixed;
            background-color: #f7f9fc;
            padding-top: 40px;
            padding-bottom: 40px;
        }
        .container {
            max-width: 600px;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            margin: 0 auto;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        }
        .header {
            background-color: #4a69bd;
            color: white;
            padding: 30px 24px;
            text-align: center;
        }
        .header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 24px;
        }
        .content {
            padding: 36px 24px;
        }
        .course-info {
            background-color: #f8f9fa;
            border-left: 4px solid #4a69bd;
            border-radius: 4px;
            padding: 20px;
            margin: 25px 0;
        }
        .course-info h3 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 18px;
            font-weight: 600;
        }
        .button {
            display: inline-block;
            background-color: #4a69bd;
            color: white !important;
            text-decoration: none;
            padding: 14px 32px;
            border-radius: 4px;
            font-weight: 600;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        .button:hover {
            background-color: #3d56a8;
        }
        .cta {
            text-align: center;
            margin: 30px 0 15px;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 24px;
            font-size: 14px;
            color: #666;
            text-align: center;
            border-top: 1px solid #eaeaea;
        }
        .footer p {
            margin: 5px 0;
        }
        .logo {
            margin-bottom: 20px;
        }
        .text-center {
            text-align: center;
        }
        p {
            margin: 16px 0;
        }
        @media only screen and (max-width: 480px) {
            .container {
                width: 100% !important;
            }
            .header {
                padding: 20px 15px;
            }
            .content {
                padding: 24px 15px;
            }
        }
    </style>
</head>
@php
    $system_name = \App\Models\Setting::where('type', 'system_name')->value('description');
@endphp
<body>
<div class="wrapper">
    <div class="container">
        <div class="header">
            <h2>Chúc mừng bạn đã đăng ký khóa học thành công!</h2>
        </div>

        <div class="content">
            <p>Kính gửi {{ $user->name }},</p>

            <p>Cảm ơn bạn đã đăng ký khóa học với chúng tôi. Dưới đây là thông tin khóa học của bạn:</p>

            <div class="course-info">
                <h3>{{ $course->title ?? $course->name }}</h3>
            </div>

            <p>Sự lựa chọn này sẽ giúp bạn phát triển kỹ năng và kiến thức cá nhân. Chúng tôi rất vui được đồng hành cùng bạn trong hành trình học tập sắp tới.</p>

            <p>Nội dung khóa học đã được kích hoạt và bạn có thể bắt đầu học ngay từ bây giờ.</p>

            <div class="cta">
                <a href="{{ route('course.player', ['slug' => $course->slug]) }}" class="button">Bắt đầu học ngay</a>
            </div>

            <p style="margin-top: 30px;">Nếu bạn cần hỗ trợ, vui lòng liên hệ với chúng tôi qua địa chỉ email hoặc hotline hỗ trợ.</p>

            <p>Trân trọng,<br>
                Đội ngũ {{ $system_name }}</p>
        </div>

        <div class="footer">
            <p>© {{ date('Y') }} {{ $system_name }}. Tất cả quyền được bảo lưu.</p>
            <p>Đây là email tự động từ hệ thống. Vui lòng không trả lời email này.</p>
        </div>
    </div>
</div>
</body>
</html>

@push('css')
    <script src="https://cdn.tailwindcss.com"></script>
    <link
        href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap"
        rel="stylesheet"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
        href="https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&display=swap"
        rel="stylesheet"
    />
    <style>
        body {
            font-family: "Lexend", sans-serif;
            font-size: 16px;
        }
    </style>
    <link
        rel="stylesheet"
        href="{{ asset('assets/frontend/default/libs/aos/aos-*******-beta.6.css') }}"
    />
    <!-- Jquery Ui Js -->
    <script src="{{ asset('assets/frontend/default/js/jquery/jquery-3.7.1.min.js') }}"></script>
    <script src="{{ asset('assets/frontend/default/js/jquery-ui/jquery-ui.min.js') }}"></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    maxWidth: {
                        "6xl": "1200px",
                    },
                    colors: {
                        red: {
                            600: "#B4332B",
                        },
                    },
                },
            },
        };
    </script>
    <style>
        body{
            color: #000;
        }
        html{
            font-size: 16px;
        }
        .banner-section {
            background-image: url("{{ asset('assets/frontend/helen-spa/assets/images/bg-banner.png') }}");
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        .benefits-section {
            background-image: url("{{ asset('assets/frontend/helen-spa/assets/images/bg-section-benefit.png') }}");
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        .course-section {
            background-image: url("{{ asset('assets/frontend/helen-spa/assets/images/bg-course.png') }}");
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .section-images {
            background: linear-gradient(180deg, #fff 0%, rgba(255, 255, 255, 0) 18%),
            linear-gradient(270deg, #fff 0%, #efebd9 100%);
        }

        .gallery-img {
            width: 100%;
            height: 100%;
            object-fit: cover; /* Ensures images cover the area, might crop */
            border-radius: 0.5rem; /* rounded-lg */
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-md */
        }

        .reward-section {
            background: url({{ asset('assets/frontend/helen-spa/assets/images/spa-reward-bg.png') }}) center 0 / cover;
        }

        .reward-section .title {
            filter: drop-shadow(0px 3px 0px #c40009);
        }

        .linear-clip-text {
            background: linear-gradient(180deg, #fff8e1 0%, #ffe898 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .reward-section .pills {
            background: #f6f4e9;
            box-shadow: 0px 0px 10px 0px #dac560 inset;
        }
        .reward-section .pills strong {
            text-shadow: -1px -1px 0 white, 1px -1px 0 white, -1px 1px 0 white, 1px 1px 0 white,
            1px 2px 0px #b4332b;
            font-variant-numeric: lining-nums proportional-nums;
        }
        .btn-regis-now{
            padding: 0 !important;
            width: 230px;
            height: 60px;
            background-size: 100%;
            background-repeat: no-repeat;
        }

        /* Enhanced Button Animations */
        .btn-register-now {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        /* Pulse Shadow Animation */
        @keyframes pulse-shadow {
            0% {
                box-shadow: 0 0 0 0 rgba(180, 51, 43, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(180, 51, 43, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(180, 51, 43, 0);
            }
        }

        .animate-pulse-shadow {
            animation: pulse-shadow 2s infinite;
        }

        /* Glow Effect */
        @keyframes glow {
            0%, 100% {
                filter: drop-shadow(0 0 5px rgba(180, 51, 43, 0.5));
            }
            50% {
                filter: drop-shadow(0 0 20px rgba(180, 51, 43, 0.8));
            }
        }

        .animate-glow {
            /* animation: glow 2s ease-in-out infinite; */
        }

        /* Bounce Effect */
        @keyframes bounce-gentle {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .animate-bounce-gentle {
            /* animation: bounce-gentle 3s infinite; */
        }

        /* Shake Effect for Attention */
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
            20%, 40%, 60%, 80% { transform: translateX(2px); }
        }

        .animate-shake {
            animation: shake 0.5s ease-in-out;
        }

        /* Shimmer Effect */
        @keyframes shimmer {
            0% {
                background-position: -200% center;
            }
            100% {
                background-position: 200% center;
            }
        }

        .btn-register-now::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-size: 200% 100%;
            animation: shimmer 3s infinite;
            border-radius: inherit;
        }

        /* Hover Effects */
        .btn-register-now:hover {
            transform: scale(1.05) translateY(-2px);
            box-shadow: 0 10px 25px rgba(180, 51, 43, 0.3);
        }

        .btn-register-now:active {
            transform: scale(0.98);
        }

        /* Attention Grabbing Animation Combo */
        .btn-attention {
            animation: 
                pulse-shadow 2s infinite,
                bounce-gentle 3s infinite,
                glow 2s ease-in-out infinite;
        }

        /* Mobile optimizations */
        @media (max-width: 768px) {
            .btn-register-now {
                width: 200px;
                height: 50px;
            }
            
            @keyframes pulse-shadow {
                0% {
                    box-shadow: 0 0 0 0 rgba(180, 51, 43, 0.7);
                }
                70% {
                    box-shadow: 0 0 0 5px rgba(180, 51, 43, 0);
                }
                100% {
                    box-shadow: 0 0 0 0 rgba(180, 51, 43, 0);
                }
            }
        }
    </style>
@endpush

<div class="bg-gray-100">
    <div class="banner-section">
        <div class="container px-4 md:px-8 xl:max-w-[1140px] xl:px-0 mx-auto">
            <!-- Content Container -->
            <div class="flex flex-col lg:flex-row items-center">
                <!-- Left Content -->
                <div
                    class="lg:w-1/2 z-10 flex justify-center items-center flex-col justify-center pt-6 md:pt-8"
                    data-x-aos="fade-up-right"
                >
                    <!-- Decorative Stars -->
                    <div class="flex items-center justify-center mb-2 gap-2">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="27"
                            height="33"
                            viewBox="0 0 27 33"
                            fill="none"
                            class="w-4 md:w-7 shrink-0"
                        >
                            <path
                                d="M25.9878 17.47C17.9027 19.1663 15.5927 21.9532 14.2067 31.889C14.0912 32.9795 12.5897 32.9795 12.4742 31.889C11.0881 21.9532 8.77811 19.1663 0.693008 17.3488C-0.231003 17.1065 -0.231003 15.7736 0.693008 15.5313C8.66261 13.8349 11.0881 11.048 12.4742 1.11225C12.5897 0.0217403 14.0912 0.0217403 14.2067 1.11225C15.5927 11.048 17.9027 13.8349 25.9878 15.6524C26.9118 15.8948 26.9118 17.3488 25.9878 17.47Z"
                                fill="#B4332B"
                            />
                        </svg>
                        <div
                            class="bg-[#f6f4e9] rounded-[100px] shadow-[inset_0px_0px_10px_0px_rgba(218,197,96,1.00)] outline outline-2 outline-white py-2 px-8"
                        >
                            <h2
                                class="text-[#6d4c33] font-bold text-sm md:text-base xl:text-2xl"
                            >
                                KHOÁ HỌC ONLINE
                            </h2>
                        </div>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="27"
                            height="33"
                            viewBox="0 0 27 33"
                            fill="none"
                            class="w-4 md:w-7 shrink-0"
                        >
                            <path
                                d="M25.9878 17.47C17.9027 19.1663 15.5927 21.9532 14.2067 31.889C14.0912 32.9795 12.5897 32.9795 12.4742 31.889C11.0881 21.9532 8.77811 19.1663 0.693008 17.3488C-0.231003 17.1065 -0.231003 15.7736 0.693008 15.5313C8.66261 13.8349 11.0881 11.048 12.4742 1.11225C12.5897 0.0217403 14.0912 0.0217403 14.2067 1.11225C15.5927 11.048 17.9027 13.8349 25.9878 15.6524C26.9118 15.8948 26.9118 17.3488 25.9878 17.47Z"
                                fill="#B4332B"
                            />
                        </svg>
                    </div>

                    <!-- Title -->
                    <h1 class="text-lg xl:text-3xl md:text-4xl font-bold text-red-600 mb-2">
                        Xây Dựng Ma Trận
                    </h1>

                    <!-- Spa Service Label -->
                    <div class="relative mb-4 w-24 md:w-36 xl:w-[200px]">
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/images/spa-service.svg') }}"
                            alt="Spa Service"
                            class="w-full h-full object-cover max-w-[200px]"
                        />
                    </div>

                    <!-- Description -->
                    <p
                        class="text-sm md:text-base xl:text-lg leading-6 font-light mb-2 xl:mb-5 text-gray-700 text-center"
                    >
                        Xây dựng mạ trận dịch vụ, hiểu rõ thị trường và khách hàng mục tiêu,
                        đồng thời thiết kế menu và combo dịch vụ hiệu quả để x10 kinh doanh spa.
                    </p>

                    <!-- Students Icons -->
                    <div class="flex items-center gap-2">
                        <div class="flex flex-col">
                            <p class="text-lg md:text-xl font-bold text-red-600 xl:text-[26px]">
                                1000+
                            </p>
                            <p class="text-xs xl:text-sm font-bold text-[#674230]">HỌC VIÊN</p>
                        </div>
                        <div class="flex -space-x-4 mt-2">
                            <img
                                class="size-12 md:size-14 xl:size-[60px] rounded-full"
                                src="{{ asset('assets/frontend/helen-spa/assets/images/student-1.png') }}"
                                alt="Student"
                            />
                            <img
                                class="size-12 md:size-14 xl:size-[60px] rounded-full"
                                src="{{ asset('assets/frontend/helen-spa/assets/images/student-2.png') }}"
                                alt="Student"
                            />
                            <img
                                class="size-12 md:size-14 xl:size-[60px] rounded-full"
                                src="{{ asset('assets/frontend/helen-spa/assets/images/student-3.png') }}"
                                alt="Student"
                            />
                            <img
                                class="size-12 md:size-14 xl:size-[60px] rounded-full"
                                src="{{ asset('assets/frontend/helen-spa/assets/images/student-4.png') }}"
                                alt="Student"
                            />
                            <img
                                class="size-12 md:size-14 xl:size-[60px] rounded-full"
                                src="{{ asset('assets/frontend/helen-spa/assets/images/student-5.png') }}"
                                alt="Student"
                            />
                        </div>
                    </div>

                    <!-- CTA Button -->
                    @auth
                        <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                            class="text-white font-bold rounded-full transition-all btn-regis-now flex justify-center items-center mt-4 transition duration-300 transform hover:scale-105 btn-register-now block animate-pulse-shadow animate-glow animate-bounce-gentle"
                            style="
							background: url({{ asset('assets/frontend/helen-spa/assets/images/register-bg-hero.png') }}) center 8px /
								cover;
						"
                        >
                            Vào học ngay ▶
                        </a>
                    @else
                        <a href="#regis-section"
                            class="text-white font-bold rounded-full transition-all btn-regis-now flex justify-center items-center mt-4 transition duration-300 transform hover:scale-105 btn-register-now block animate-pulse-shadow animate-glow animate-bounce-gentle"
                            style="
							background: url({{ asset('assets/frontend/helen-spa/assets/images/register-bg-hero.png') }}) center 8px /
								cover;
						"
                        >
                            Đăng ký ngay ▶
                        </a>
                    @endauth
                </div>

                <!-- Right Content with Stats -->
                <div class="lg:w-1/2 pt-20 relative" data-x-aos="fade-up-left">
                    <img
                        src="{{ asset('assets/frontend/helen-spa/assets/images/banner-spot.png') }}"
                        alt="Banner"
                        class="w-full absolute top-0 left-0 object-cover"
                    />
                    <img
                        src="{{ asset('assets/frontend/helen-spa/assets/images/banner-helen.png') }}"
                        alt="Banner"
                        class="w-full h-full object-cover z-10 relative"
                    />
                </div>
            </div>
        </div>
    </div>

    <!-- Why Customers Choose Smallest Packages Section -->
    <div class="benefits-section pb-10 xl:pb-20">
        <div class="container xl:max-w-[1140px] xl:px-0 md:px-8 mx-auto px-4">
            <div data-x-aos="fade-up">
                <div
                    class="relative w-full z-10 py-10 px-6 xl:px-12 -translate-y-4 xl:-translate-y-8 bg-gradient-to-b from-white to-[#ffe4e4] rounded-xl xl:rounded-[49px] shadow-[0px_-10px_15px_0px_rgba(0,0,0,0.10)]"
                >
                    <div class="absolute top-full left-1/2 -translate-y-2 -translate-x-1/2">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="57"
                            height="26"
                            viewBox="0 0 57 26"
                            fill="none"
                            class="w-8 xl:w-14"
                        >
                            <path d="M28.0223 26L0 0H57L28.0223 26Z" fill="#FFE5E5" />
                        </svg>
                    </div>
                    <div class="flex justify-between items-start mb-4 xl:mb-6">
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/images/ques-icon.png') }}"
                            class="w-[75px] md:w-16 lg:w-[75px] h-auto -mt-2 absolute -top-6 -left-4 lg:static"
                            alt="Question Mark"
                        />

                        <h2
                            class="text-lg w-full lg:text-2xl xl:text-3xl font-bold text-center"
                        >
                            Tại sao khách hàng chỉ sử dụng <br class="hidden md:block" />
                            <span class="text-red-600">gói dịch vụ nhỏ nhất</span> khi đi Spa?
                        </h2>

                        <div class="hidden lg:inline-block w-[75px] h-[75px]"></div>
                    </div>

                    <!-- Problem Description -->
                    <div class="text-gray-700 text-sm lg:text-base xl:text-lg text-center">
                        <p class="mb-3 text-[#444]">
                            Trong ngành spa, nhiều chủ spa thường gặp sai lầm khi cung cấp quá
                            nhiều dịch vụ mà không có sự phân loại hoặc định hướng rõ ràng.
                        </p>
                        <p class="text-[#444]">
                            Điều này khiến khách hàng bối rối, khó lựa chọn, dẫn đến trải nghiệm
                            không tốt và doanh thu bị ảnh hưởng. Một lỗi phổ biến khác là tập
                            trung vào các dịch vụ không phù hợp với nhu cầu của khách hàng mục
                            tiêu, làm giảm hiệu quả kinh doanh và lãng phí nguồn lực.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Benefits Circle -->
            <div class="relative flex justify-center mb-6 xl:mb-12" data-x-aos="fade-up">
                <img src="{{ asset('assets/frontend/helen-spa/assets/images/why-choose.png') }}" class="w-full" alt="Benefit Circle" />
            </div>

            <!-- Service Boxes -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Box 1 -->
                <div class="flex flex-col">
                    <div
                        class="bg-red-600 text-white rounded-[50px] py-2 mb-3 px-3 flex items-center gap-2"
                        data-x-aos="fade-up"
                    >
                        <div class="bg-white rounded-full p-1">
                            <img src="{{ asset('assets/frontend/helen-spa/assets/images/check-v.png') }}" class="w-[30px]" />
                        </div>
                        <h3 class="font-bold bg-[#ae322a] rounded-[50px] text-sm lg:text-base">
                            Xây dựng Ma Trận Dịch Vụ chuyên nghiệp
                        </h3>
                    </div>
                    <div
                        class="border-2 border-red-600 border-solid rounded-lg xl:rounded-[20px] py-3 px-4 h-auto text-sm lg:text-base md:h-36 xl:h-[116px] bg-white shadow-[0px_4px_15px_0px_rgba(0,0,0,0.15)]"
                        data-x-aos="fade-up"
                        data-aos-delay="100"
                    >
                        Bạn sẽ biết cách phân loại, tối ưu dịch vụ theo từng phân khúc khách
                        hàng, đảm bảo tăng doanh thu và nâng cao trải nghiệm khách hàng.
                    </div>
                </div>

                <!-- Box 2 -->
                <div class="flex flex-col">
                    <div
                        class="bg-red-600 text-white rounded-[50px] py-2 mb-3 px-3 flex items-center gap-2"
                        data-x-aos="fade-up"
                        data-aos-delay="150"
                    >
                        <div class="bg-white rounded-full p-1">
                            <img src="{{ asset('assets/frontend/helen-spa/assets/images/check-v.png') }}" class="w-[30px]" />
                        </div>
                        <h3 class="font-bold bg-[#ae322a] rounded-[50px] text-sm lg:text-base">
                            Hiểu rõ thị trường và khách hàng mục tiêu
                        </h3>
                    </div>
                    <div
                        class="border-2 border-red-600 border-solid rounded-lg xl:rounded-[20px] py-3 px-4 h-auto text-sm lg:text-base md:h-36 xl:h-[116px] bg-white shadow-[0px_4px_15px_0px_rgba(0,0,0,0.15)]"
                        data-x-aos="fade-up"
                        data-aos-delay="300"
                    >
                        Nắm bắt xu hướng, phân tích đối thủ và xây dựng các dịch vụ phù hợp để
                        tạo sự khác biệt và thu hút khách hàng.
                    </div>
                </div>

                <!-- Box 3 -->
                <div class="flex flex-col">
                    <div
                        class="bg-red-600 text-white rounded-[50px] py-2 mb-3 px-3 flex items-center gap-2"
                        data-x-aos="fade-up"
                        data-aos-delay="200"
                    >
                        <div class="bg-white rounded-full p-1">
                            <img src="{{ asset('assets/frontend/helen-spa/assets/images/check-v.png') }}" class="w-[30px]" />
                        </div>
                        <h3 class="font-bold bg-[#ae322a] rounded-[50px] text-sm lg:text-base">
                            Thiết kế menu và combo dịch vụ ấn tượng
                        </h3>
                    </div>
                    <div
                        class="border-2 border-red-600 border-solid rounded-lg xl:rounded-[20px] py-3 px-4 h-auto text-sm lg:text-base md:h-36 xl:h-[116px] bg-white shadow-[0px_4px_15px_0px_rgba(0,0,0,0.15)]"
                        data-x-aos="fade-up"
                        data-aos-delay="350"
                    >
                        Thành thạo trong việc sắp xếp, định giá và xây dựng câu chuyện cho từng
                        dịch vụ, đồng thời triển khai các chiến lược marketing hiệu quả.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Outline Section -->
    <div class="course-section py-8 xl:py-16">
        <div class="container px-4 md:px-8 xl:max-w-[1140px] xl:px-0 mx-auto">
            <!-- Course Title with Stars -->
            <div class="flex items-center justify-center mb-8 gap-2" data-x-aos="fade-up">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="27"
                    height="33"
                    viewBox="0 0 27 33"
                    fill="none"
                    class="w-4 md:w-7 shrink-0"
                >
                    <path
                        d="M25.9878 17.47C17.9027 19.1663 15.5927 21.9532 14.2067 31.889C14.0912 32.9795 12.5897 32.9795 12.4742 31.889C11.0881 21.9532 8.77811 19.1663 0.693008 17.3488C-0.231003 17.1065 -0.231003 15.7736 0.693008 15.5313C8.66261 13.8349 11.0881 11.048 12.4742 1.11225C12.5897 0.0217403 14.0912 0.0217403 14.2067 1.11225C15.5927 11.048 17.9027 13.8349 25.9878 15.6524C26.9118 15.8948 26.9118 17.3488 25.9878 17.47Z"
                        fill="white"
                    />
                </svg>
                <div
                    class="bg-[#f6f4e9] rounded-[100px] shadow-[inset_0px_0px_10px_0px_rgba(218,197,96,1.00)] outline outline-2 outline-white py-2 px-8"
                >
                    <h2
                        class="text-[#6d4c33] font-bold text-center text-sm md:text-base xl:text-2xl"
                    >
                        Trọn Bộ Khóa Học Cầm Tay Chỉ Việc
                    </h2>
                </div>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="27"
                    height="33"
                    viewBox="0 0 27 33"
                    fill="none"
                    class="w-4 md:w-7 shrink-0"
                >
                    <path
                        d="M25.9878 17.47C17.9027 19.1663 15.5927 21.9532 14.2067 31.889C14.0912 32.9795 12.5897 32.9795 12.4742 31.889C11.0881 21.9532 8.77811 19.1663 0.693008 17.3488C-0.231003 17.1065 -0.231003 15.7736 0.693008 15.5313C8.66261 13.8349 11.0881 11.048 12.4742 1.11225C12.5897 0.0217403 14.0912 0.0217403 14.2067 1.11225C15.5927 11.048 17.9027 13.8349 25.9878 15.6524C26.9118 15.8948 26.9118 17.3488 25.9878 17.47Z"
                        fill="white"
                    />
                </svg>
            </div>

            <!-- Main Title -->
            <div class="flex flex-col md:flex-row items-center gap-2 justify-center mb-10">
                <h2
                    class="text-xl md:text-2xl xl:text-4xl text-center md:text-right font-bold text-white"
                    data-x-aos="fade-right"
                >
                    Xây Dựng <br class="hidden md:block" />
                    ma trận
                </h2>
                <img
                    src="{{ asset('assets/frontend/helen-spa/assets/images/spa-dv.png') }}"
                    alt=""
                    data-x-aos="fade-left"
                    class="max-w-[200px] w-32 md:w-40 lg:w-[200px]"
                />
            </div>

            <!-- Course Modules Container -->
            <div class="flex flex-col lg:flex-row gap-4 lg:gap-6">
                <!-- Left Module Content -->
                <div
                    class="lg:w-1/2 bg-white p-6 rounded-3xl relative"
                    data-x-aos="fade-up-right"
                >
                    <div
                        class="absolute border border-[2px] border-white -top-5 left-4 bg-red-600 text-white font-bold text-sm md:text-base lg:text-xl py-2 px-6 rounded-full inline-block mb-4"
                    >
                        Phần 1
                    </div>

                    <h3
                        class="text-base md:text-xl lg:text-2xl lg:mt-2 font-bold text-red-600 md:mt-1 md:mb-2 lg:mb-4"
                    >
                        Xây Dựng Ma Trận Dịch Vụ Hoàn Hảo Cho Spa
                    </h3>

                    <p class="mb-4 text-sm md:text-base font-light">
                        Giúp bạn hiểu rõ vai trò của ma trận dịch vụ và cách xây dựng một danh
                        mục
                        <span class="font-bold"
                        >dịch vụ chuyên nghiệp, tối ưu doanh thu và trải nghiệm khách
								hàng</span
                        >.
                    </p>

                    <ul class="space-y-1 font-light text-sm md:text-base">
                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                Khái niệm và lợi ích của ma trận dịch vụ.
                            </div>
                        </li>

                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                <span class="font-bold">Phân loại</span> dịch vụ theo từng nhóm
                                khách hàng: cơ bản, điều trị chuyên sâu và dịch vụ công nghệ cao
                            </div>
                        </li>

                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                Phân tích các
                                <span class="font-bold">mô hình ma trận spa thành công.</span>
                            </div>
                        </li>

                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                <span class="font-bold">Lập kế hoạch và xây dựng ma trận</span>
                                dịch vụ để triển khai.
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- Right Image -->
                <div
                    class="lg:w-1/2 flex items-center justify-center relative"
                    data-x-aos="fade-up-left"
                >
                    <img
                        src="{{ asset('assets/frontend/helen-spa/assets/images/course-1.png') }}"
                        class="size-full object-cover rounded-3xl"
                        alt="Course Outline"
                    />
                    <div
                        class="flex items-center justify-center absolute gap-3 left-1/2 -translate-x-1/2 -bottom-8"
                    >
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/images/course-11.png') }}"
                            class="w-16 md:w-28 lg:w-[122px] max-w-[122px]"
                            alt="Course Outline"
                        />
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/images/course-12.png') }}"
                            class="w-16 md:w-28 lg:w-[122px] max-w-[122px]"
                            alt="Course Outline"
                        />
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/images/course-13.png') }}"
                            class="w-16 md:w-28 lg:w-[122px] max-w-[122px]"
                            alt="Course Outline"
                        />
                    </div>
                </div>
            </div>

            <div class="flex justify-center my-4 md:my-8" data-x-aos="fade-up">
                <img
                    src="{{ asset('assets/frontend/helen-spa/assets/images/line-course.png') }}"
                    class="w-full max-w-[320px]"
                    alt=""
                />
            </div>

            <!-- Course Modules Container -->
            <div class="flex flex-col-reverse lg:flex-row gap-4 lg:gap-6">
                <!-- Right Image -->
                <div
                    class="lg:w-1/2 flex items-center justify-center relative"
                    data-x-aos="fade-up-right"
                >
                    <img
                        src="{{ asset('assets/frontend/helen-spa/assets/images/course-2.png') }}"
                        class="size-full object-cover rounded-3xl"
                        alt="Course Outline"
                    />
                    <div
                        class="flex items-center justify-center absolute gap-3 left-1/2 -translate-x-1/2 -bottom-8"
                    >
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/images/course-21.png') }}"
                            class="w-16 md:w-28 lg:w-[122px] max-w-[122px]"
                            alt="Course Outline"
                        />
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/images/course-22.png') }}"
                            class="w-16 md:w-28 lg:w-[122px] max-w-[122px]"
                            alt="Course Outline"
                        />
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/images/course-23.png') }}"
                            class="w-16 md:w-28 lg:w-[122px] max-w-[122px]"
                            alt="Course Outline"
                        />
                    </div>
                </div>
                <!-- Left Module Content -->
                <div
                    class="lg:w-1/2 bg-white p-6 rounded-3xl relative"
                    data-x-aos="fade-up-left"
                >
                    <div
                        class="absolute border border-[2px] border-white -top-5 left-4 bg-red-600 text-white font-bold text-sm md:text-base lg:text-xl py-2 px-6 rounded-full inline-block mb-4"
                    >
                        Phần 2
                    </div>

                    <h3
                        class="text-base md:text-xl lg:text-2xl lg:mt-2 font-bold text-red-600 md:mt-1 md:mb-2 lg:mb-4"
                    >
                        Hiểu Khách Hàng - Đánh Trúng Thị Trường
                    </h3>

                    <p class="mb-4 text-sm md:text-base font-light">
                        Giúp bạn phân tích thị trường,
                        <span class="font-bold">hiểu rõ khách hàng</span> mục tiêu và
                        <span class="font-bold">thiết kế dịch vụ phù hợp</span> để tạo lợi thế
                        cạnh tranh.
                    </p>

                    <ul class="space-y-1 font-light text-sm md:text-base">
                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                Phân tích xu hướng thị trường: làm đẹp xanh, cá nhân hóa, công
                                nghệ cao.
                            </div>
                        </li>

                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                Phân khúc khách hàng dựa trên giới tính, độ tuổi, thu nhập và
                                nhu cầu, từ đó xây dựng chân dung khách hàng chi tiết để phục vụ
                                hiệu quả
                            </div>
                        </li>

                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                Cách lựa chọn và tối ưu các dịch vụ đáp ứng nhu cầu thị trường,
                                đồng thời phân bổ ngân sách cho các dịch vụ chủ lực.
                            </div>
                        </li>

                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                Phân tích điểm mạnh, điểm yếu để tạo sự khác biệt cho spa.
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="flex justify-center my-4 md:my-8" data-x-aos="fade-up">
                <img
                    src="{{ asset('assets/frontend/helen-spa/assets/images/line-course.png') }}"
                    class="max-w-full w-[320px] transform -scale-x-100"
                    alt=""
                />
            </div>

            <!-- Course Modules Container -->
            <div class="flex flex-col lg:flex-row gap-6">
                <!-- Left Module Content -->
                <div
                    class="lg:w-1/2 bg-white p-6 rounded-3xl relative"
                    data-x-aos="fade-up-right"
                >
                    <div
                        class="absolute border border-[2px] border-white -top-5 left-4 bg-red-600 text-white font-bold text-sm md:text-base lg:text-xl py-2 px-6 rounded-full inline-block mb-4 flex items-center gap-2"
                    >
                        <span class="lg:mr-2">Phần 3</span>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="21"
                            height="21"
                            viewBox="0 0 21 21"
                            fill="none"
                            class="w-4 lg:w-5"
                        >
                            <path
                                d="M10.5005 16.585L6.39869 18.741C5.51485 19.2058 4.48235 18.4554 4.65079 17.4711L5.434 12.9032L2.11539 9.66773C1.3999 8.97096 1.79479 7.75681 2.78249 7.61244L7.36889 6.9463L9.4198 2.78979C9.86168 1.89396 11.1382 1.89396 11.5801 2.78979L13.631 6.9463L18.2175 7.61244C19.2052 7.75576 19.6001 8.96983 18.8848 9.66773L15.5661 12.9032L16.3493 17.4711C16.5177 18.4555 15.4851 19.2058 14.6014 18.741L10.5005 16.585Z"
                                fill="#FFDE51"
                            />
                        </svg>
                        <span
                            class="bg-yellow-300 text-red-600 px-3 py-1 rounded-full text-sm font-bold"
                        >Quan trọng</span
                        >
                    </div>

                    <h3
                        class="text-base md:text-xl lg:text-2xl lg:mt-2 font-bold text-red-600 md:mt-1 md:mb-2 lg:mb-4"
                    >
                        Xây Dựng Ma Trận Dịch Vụ Hoàn Hảo Cho Spa
                    </h3>

                    <p class="mb-4 text-sm md:text-base font-light">
                        Giúp bạn hiểu rõ vai trò của ma trận dịch vụ và cách xây dựng một danh
                        mục
                        <span class="font-bold"
                        >dịch vụ chuyên nghiệp, tối ưu doanh thu và trải nghiệm khách
								hàng</span
                        >.
                    </p>

                    <ul class="space-y-1 font-light text-sm md:text-base">
                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                Khái niệm và lợi ích của ma trận dịch vụ.
                            </div>
                        </li>

                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                <span class="font-bold">Phân loại</span> dịch vụ theo từng nhóm
                                khách hàng: cơ bản, điều trị chuyên sâu và dịch vụ công nghệ cao
                            </div>
                        </li>

                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                Phân tích các
                                <span class="font-bold"> mô hình ma trận spa thành công.</span>
                            </div>
                        </li>

                        <li class="flex items-start gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="19"
                                height="19"
                                viewBox="0 0 19 19"
                                fill="none"
                                class="w-4 lg:w-5"
                            >
                                <g clip-path="url(#clip0_17_58)">
                                    <path
                                        d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                        fill="#B4332B"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_17_58">
                                        <rect width="19" height="19" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex-1 -translate-y-[2px]">
                                <span class="font-bold">Lập kế hoạch và xây dựng ma trận</span>
                                dịch vụ để triển khai.
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- Right Image -->
                <div class="lg:w-1/2 flex items-center justify-center relative">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-2 w-full">
                        <!-- Large Center Image -->
                        <div class="md:col-span-2">
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/course-31.png') }}"
                                data-x-aos="zoom-in"
                                alt="Ultralift Skin Renewal"
                                class="w-full rounded-2xl shadow-md h-full object-cover"
                            />
                        </div>

                        <!-- Top Right 2 Images -->
                        <div class="md:col-span-2 grid grid-cols-2 md:grid-cols-2 gap-2">
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/course-32.png') }}"
                                data-x-aos="zoom-in"
                                alt="Table of Contents"
                                class="w-full rounded-2xl shadow-md h-full object-cover"
                            />
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/course-33.png') }}"
                                data-x-aos="zoom-in"
                                alt="Spa Intro"
                                class="w-full rounded-2xl shadow-md h-full object-cover"
                            />

                            <!-- Bottom Right 2 Images -->
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/course-34.png') }}"
                                data-x-aos="zoom-in"
                                alt="Nâng cơ trẻ hóa"
                                class="w-full rounded-2xl shadow-md h-full object-cover"
                            />
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/course-35.png') }}"
                                data-x-aos="zoom-in"
                                alt="Căng bóng sáng mịn"
                                class="w-full rounded-2xl shadow-md h-full object-cover"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center mt-8">
                @auth
                    <a
                        href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                        class="text-[#B4332B] font-bold py-5 px-12 rounded-full transition-all text-lg btn-regis-now flex justify-center items-center btn-register-now"
                        style="background: url({{ asset('assets/frontend/helen-spa/assets/images/register-bg.png') }}) center 8px / cover"
                        data-x-aos="fade-up"
                    >
                        Vào học ngay ▶
                    </a>
                @else
                    <a
                        href="#regis-section"
                        class="text-[#B4332B] font-bold py-5 px-12 rounded-full transition-all text-lg btn-regis-now flex justify-center items-center btn-register-now"
                        style="background: url({{ asset('assets/frontend/helen-spa/assets/images/register-bg.png') }}) center 8px / cover"
                        data-x-aos="fade-up"
                    >
                        Đăng ký ngay ▶
                    </a>
                @endauth
            </div>
        </div>
    </div>

    <!-- Section teacher -->
    <div class="section-teacher bg-gradient-to-b from-white to-[#FFEAEA]">
        <div class="container xl:max-w-[1140px] xl:px-0 mx-auto px-4 py-10 md:py-10 lg:py-0">
            <!-- Teacher profile card -->
            <div class="flex flex-col lg:flex-row gap-12 md:gap-10">
                <!-- Left side - Teacher image -->
                <div class="lg:w-1/2 relative">
                    <!-- Background with tags -->
                    <div
                        class="absolute inset-0 flex flex-col justify-center items-center z-10"
                    >
                        <!-- Experience badges -->
                        <div
                            class="absolute bottom-1 md:bottom-12 lg:bottom-[75px] left-0 w-full flex flex-row flex-wrap justify-center gap-2"
                        >
                            <div
                                class="w-2/5 md:w-fit rounded-full bg-red-600 px-3 py-1 flex justify-center items-center"
                                data-x-aos="fade-up"
                                data-aos-delay="100"
                            >
                                <p
                                    class="text-center text-white text-xs md:text-sm font-normal"
                                >
                                    <span class="font-extrabold">8 NĂM</span> kinh nghiệm
                                </p>
                            </div>
                            <div
                                class="w-2/5 md:w-fit rounded-full bg-red-600 px-3 py-1 flex justify-center items-center"
                                data-x-aos="fade-up"
                                data-aos-delay="200"
                            >
                                <p
                                    class="text-center text-white text-xs md:text-sm font-normal"
                                >
                                    <span class="font-extrabold">400+</span> lớp học chuyên môn
                                </p>
                            </div>
                            <div
                                class="w-2/5 md:w-fit rounded-full bg-red-600 px-3 py-1 flex justify-center items-center"
                                data-x-aos="fade-up"
                                data-aos-delay="300"
                            >
                                <p
                                    class="text-center text-white text-xs md:text-sm font-normal"
                                >
                                    <span class="font-extrabold">50+</span> bộ sách đào tạo
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="absolute left-1.5 top-1/2 -translate-y-1/2 leading-none z-10">
                        <!-- Giảng viên label -->
                        <div class="">
                            <div
                                class="text-[#B4332B] font-normal text-base md:text-2xl xl:text-[38px]"
                                data-x-aos="fade-up"
                            >
                                Giảng <br />viên
                            </div>
                        </div>

                        <!-- Helen Hải name -->
                        <div class="">
                            <h2
                                data-x-aos="fade-up"
                                data-aos-delay="100"
                                class="text-[#B4332B] font-extrabold text-3xl md:text-6xl xl:text-[109px]"
                            >
                                Helen <br />Hải
                            </h2>
                        </div>
                    </div>

                    <!-- Teacher image -->
                    <img
                        src="{{ asset('assets/frontend/helen-spa/assets/images/teacher-img.png') }}"
                        alt="Helen Hải"
                        data-x-aos="zoom-in"
                        class="w-full h-full object-cover"
                    />
                </div>

                <!-- Right side - Teacher information -->
                <div class="lg:w-1/2 lg:py-10 xl:py-[60px] flex flex-col">
                    <!-- Title with stars -->
                    <div class="text-center mb-2 lg:mb-8">
                        <h2
                            class="text-lg md:text-2xl lg:text-[32px] font-bold text-[#B4332B] mb-3.5"
                            data-x-aos="fade-up"
                        >
                            Giảng viên
                        </h2>
                        <div
                            class="flex items-center justify-center gap-2 mb-4"
                            data-x-aos="fade-up"
                            data-aos-delay="100"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="27"
                                height="33"
                                viewBox="0 0 27 33"
                                fill="none"
                                class="w-4 md:w-7 shrink-0"
                            >
                                <path
                                    d="M25.9878 17.47C17.9027 19.1663 15.5927 21.9532 14.2067 31.889C14.0912 32.9795 12.5897 32.9795 12.4742 31.889C11.0881 21.9532 8.77811 19.1663 0.693008 17.3488C-0.231003 17.1065 -0.231003 15.7736 0.693008 15.5313C8.66261 13.8349 11.0881 11.048 12.4742 1.11225C12.5897 0.0217403 14.0912 0.0217403 14.2067 1.11225C15.5927 11.048 17.9027 13.8349 25.9878 15.6524C26.9118 15.8948 26.9118 17.3488 25.9878 17.47Z"
                                    fill="#B4332B"
                                />
                            </svg>
                            <div
                                class="bg-[#f6f4e9] rounded-[100px] shadow-[inset_0px_0px_10px_0px_rgba(218,197,96,1.00)] outline outline-2 outline-white py-2 px-8"
                            >
                                <h2
                                    class="text-[#6d4c33] font-bold text-center text-sm md:text-base xl:text-2xl"
                                >
                                    Đồng hành cùng bạn
                                </h2>
                            </div>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="27"
                                height="33"
                                viewBox="0 0 27 33"
                                fill="none"
                                class="w-4 md:w-7 shrink-0"
                            >
                                <path
                                    d="M25.9878 17.47C17.9027 19.1663 15.5927 21.9532 14.2067 31.889C14.0912 32.9795 12.5897 32.9795 12.4742 31.889C11.0881 21.9532 8.77811 19.1663 0.693008 17.3488C-0.231003 17.1065 -0.231003 15.7736 0.693008 15.5313C8.66261 13.8349 11.0881 11.048 12.4742 1.11225C12.5897 0.0217403 14.0912 0.0217403 14.2067 1.11225C15.5927 11.048 17.9027 13.8349 25.9878 15.6524C26.9118 15.8948 26.9118 17.3488 25.9878 17.47Z"
                                    fill="#B4332B"
                                />
                            </svg>
                        </div>
                    </div>

                    <!-- Professional title -->
                    <div class="mb-6">
                        <h3
                            class="text-[#B4332B] font-bold text-base md:text-lg lg:text-xl mb-2 flex items-center"
                            data-x-aos="fade-up"
                        >
                            <span class="inline-block w-1 h-6 bg-[#B4332B] mr-2"></span>
                            Chức danh chuyên môn
                        </h3>
                        <p
                            data-x-aos="fade-up"
                            data-aos-delay="100"
                            class="text-[#212121] text-sm md:text-base xl:text-lg ml-3"
                        >
                            Giảng viên quốc tế ISO bộ môn da liễu thẩm mỹ.
                        </p>
                    </div>

                    <!-- Industry experience -->
                    <div class="mb-6">
                        <h3
                            class="text-[#B4332B] font-bold text-base md:text-lg lg:text-xl mb-2 flex items-center"
                            data-x-aos="fade-up"
                            data-aos-delay="200"
                        >
                            <span class="inline-block w-1 h-6 bg-[#B4332B] mr-2"></span>
                            Kinh nghiệm trong ngành
                        </h3>
                        <ul
                            class="text-[#212121] text-sm md:text-base xl:text-lg ml-3 space-y-1"
                        >
                            <li
                                class="flex items-start"
                                data-x-aos="fade-up"
                                data-aos-delay="300"
                            >
                                <span class="mr-2">•</span>
                                <p>
                                    Hơn 8 năm kinh nghiệm trong lĩnh vực làm đẹp và da liễu thẩm
                                    mỹ.
                                </p>
                            </li>
                            <li
                                class="flex items-start"
                                data-x-aos="fade-up"
                                data-aos-delay="400"
                            >
                                <span class="mr-2">•</span>
                                <p>Đào tạo hơn 400 lớp học lớn nhỏ về chuyên môn.</p>
                            </li>
                            <li
                                class="flex items-start"
                                data-x-aos="fade-up"
                                data-aos-delay="500"
                            >
                                <span class="mr-2">•</span>
                                <p>
                                    Tác giả của hơn 50 bộ sách đào tạo chuyên sâu trong ngành
                                    làm đẹp
                                </p>
                            </li>
                        </ul>
                    </div>

                    <!-- Management and advisory roles -->
                    <div>
                        <h3
                            class="text-[#B4332B] font-bold text-base md:text-lg lg:text-xl mb-2 flex items-center"
                            data-x-aos="fade-up"
                            data-aos-delay="600"
                        >
                            <span class="inline-block w-1 h-6 bg-[#B4332B] mr-2"></span>
                            Vai trò quản lý và cố vấn
                        </h3>
                        <ul
                            class="text-[#212121] text-sm md:text-base xl:text-lg ml-3 space-y-1"
                        >
                            <li
                                class="flex items-start"
                                data-x-aos="fade-up"
                                data-aos-delay="700"
                            >
                                <span class="mr-2">•</span>
                                <p>Giám đốc chuyên môn Nhãn hàng EZB.</p>
                            </li>
                            <li
                                class="flex items-start"
                                data-x-aos="fade-up"
                                data-aos-delay="800"
                            >
                                <span class="mr-2">•</span>
                                <p>Founder Trung tâm Phục Sắc EZB và Perfect Skin Medical.</p>
                            </li>
                            <li
                                class="flex items-start"
                                data-x-aos="fade-up"
                                data-aos-delay="900"
                            >
                                <span class="mr-2">•</span>
                                <p>
                                    CEO & Giảng viên tại BeautyEDU – Hệ thống giáo dục chuyên
                                    sâu trong ngành spa.
                                </p>
                            </li>
                            <li
                                class="flex items-start"
                                data-x-aos="fade-up"
                                data-aos-delay="1000"
                            >
                                <span class="text-red-600 mr-2">•</span>
                                <p>
                                    Cố vấn chuyên môn, kinh doanh cho nhiều spa, giúp nâng cao
                                    hiệu quả trị liệu và xây dựng dịch vụ đẳng cấp
                                </p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- section images -->
    <div class="py-8 md:py-10 lg:py-16 section-images">
        <div class="container xl:max-w-[1140px] xl:px-0 mx-auto px-4">
            <!-- Main Title -->
            <div
                class="text-center text-base mb-12 md:text-lg lg:text-2xl xl:text-[32px] font-bold"
                data-x-aos="fade-up"
            >
                <h2 class="text-[#B4332B]">
                    Chuyên gia hàng đầu
                    <span class="text-[#212121]">trong lĩnh vực thẩm mỹ</span>
                </h2>
                <p class="text-[#212121]">
                    Giảng viên tại nhiều lớp học
                    <span class="text-[#B4332B] font-bold">hàng trăm học viên tham gia</span>
                </p>
            </div>

            <div>
                <div class="mb-6">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex md:flex-col gap-4 md:w-1/3">
                            <div class="h-40 md:h-48" data-x-aos="zoom-in">
                                <img
                                    src="{{ asset('assets/frontend/helen-spa/assets/images/cg1.png') }}"
                                    alt="Gallery Image Left 1"
                                    class="gallery-img size-full object-cover hover:scale-105 transition-all duration-300"
                                />
                            </div>
                            <div class="h-40 md:h-48" data-x-aos="zoom-in">
                                <img
                                    src="{{ asset('assets/frontend/helen-spa/assets/images/cg2.png') }}"
                                    alt="Gallery Image Left 2"
                                    class="gallery-img size-full object-cover hover:scale-105 transition-all duration-300"
                                />
                            </div>
                        </div>

                        <div
                            data-x-aos="zoom-in"
                            class="md:w-1/3 h-80 md:h-[calc(2*theme(spacing.48)+theme(spacing.4))]"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/cg3.png') }}"
                                alt="Gallery Image Center"
                                class="gallery-img size-full object-cover hover:scale-105 transition-all duration-300"
                            />
                        </div>

                        <div class="flex md:flex-col gap-4 md:w-1/3">
                            <div data-x-aos="zoom-in" class="h-40 md:h-48">
                                <img
                                    src="{{ asset('assets/frontend/helen-spa/assets/images/cg4.png') }}"
                                    alt="Gallery Image Right 1"
                                    class="gallery-img size-full object-cover hover:scale-105 transition-all duration-300"
                                />
                            </div>
                            <div data-x-aos="zoom-in" class="h-40 md:h-48">
                                <img
                                    src="{{ asset('assets/frontend/helen-spa/assets/images/cg5.png') }}"
                                    alt="Gallery Image Right 2"
                                    class="gallery-img size-full object-cover hover:scale-105 transition-all duration-300"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col md:flex-row gap-4">
                    <div data-x-aos="zoom-in" class="md:w-1/2 h-60">
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/images/cg6.png') }}"
                            alt="Gallery Image Bottom 1"
                            class="gallery-img size-full object-cover hover:scale-105 transition-all duration-300"
                        />
                    </div>
                    <div data-x-aos="zoom-in" class="md:w-1/2 h-60">
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/images/cg7.png') }}"
                            alt="Gallery Image Bottom 2"
                            class="gallery-img size-full object-cover hover:scale-105 transition-all duration-300"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Student Benefits Section -->
    <div class="py-8 md:py-10 lg:py-16 bg-[#faf7ef]">
        <div class="xl:max-w-[1140px] xl:px-0 mx-auto px-4">
            <!-- Title with stars -->
            <div class="text-center mb-8">
                <div data-x-aos="fade-up" class="flex items-center justify-center gap-2 mb-4">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="27"
                        height="33"
                        viewBox="0 0 27 33"
                        fill="none"
                        class="w-4 md:w-7 shrink-0"
                    >
                        <path
                            d="M25.9878 17.47C17.9027 19.1663 15.5927 21.9532 14.2067 31.889C14.0912 32.9795 12.5897 32.9795 12.4742 31.889C11.0881 21.9532 8.77811 19.1663 0.693008 17.3488C-0.231003 17.1065 -0.231003 15.7736 0.693008 15.5313C8.66261 13.8349 11.0881 11.048 12.4742 1.11225C12.5897 0.0217403 14.0912 0.0217403 14.2067 1.11225C15.5927 11.048 17.9027 13.8349 25.9878 15.6524C26.9118 15.8948 26.9118 17.3488 25.9878 17.47Z"
                            fill="#B4332B"
                        />
                    </svg>
                    <div
                        class="bg-[#f6f4e9] rounded-[100px] shadow-[inset_0px_0px_10px_0px_rgba(218,197,96,1.00)] outline outline-2 outline-white py-2 px-8"
                    >
                        <h2
                            class="text-[#6d4c33] font-bold text-center text-sm md:text-base xl:text-2xl"
                        >
                            Quyền lợi của học viên
                        </h2>
                    </div>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="27"
                        height="33"
                        viewBox="0 0 27 33"
                        fill="none"
                        class="w-4 md:w-7 shrink-0"
                    >
                        <path
                            d="M25.9878 17.47C17.9027 19.1663 15.5927 21.9532 14.2067 31.889C14.0912 32.9795 12.5897 32.9795 12.4742 31.889C11.0881 21.9532 8.77811 19.1663 0.693008 17.3488C-0.231003 17.1065 -0.231003 15.7736 0.693008 15.5313C8.66261 13.8349 11.0881 11.048 12.4742 1.11225C12.5897 0.0217403 14.0912 0.0217403 14.2067 1.11225C15.5927 11.048 17.9027 13.8349 25.9878 15.6524C26.9118 15.8948 26.9118 17.3488 25.9878 17.47Z"
                            fill="#B4332B"
                        />
                    </svg>
                </div>
                <h2
                    class="text-lg md:text-2xl lg:text-[32px] font-bold text-red-600 mb-4"
                    data-x-aos="fade-up"
                >
                    Khi tham gia khóa học
                </h2>
            </div>

            <!-- Benefits Content -->
            <div class="flex flex-col gap-10 items-center">
                <!-- Left Side: 1-1 Consultation -->
                <div class="w-full flex flex-col lg:flex-row items-center gap-10">
                    <div class="w-full lg:w-1/2">
                        <!-- Consultation Title Button -->
                        <div class="mb-4 lg:mb-6">
                            <div
                                class="bg-[#B4332B] text-sm md:text-base text-white font-bold py-3 px-6 rounded-full inline-block"
                                data-x-aos="fade-up"
                            >
                                Tư vấn 1-1 cho chủ spa
                            </div>
                        </div>

                        <!-- Consultation Benefits -->
                        <ul class="space-y-4 font-light text-sm md:text-base lg:text-lg">
                            <li
                                class="flex items-start gap-3"
                                data-x-aos="fade-up"
                                data-aos-delay="100"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 19 19"
                                    fill="none"
                                    class="mt-1 shrink-0 w-4 lg:w-5"
                                >
                                    <g clip-path="url(#clip0_17_58)">
                                        <path
                                            d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                            fill="#B4332B"
                                        />
                                    </g>
                                </svg>
                                <p>
                                    Được hỗ trợ trực tiếp từ chuyên gia với các giải pháp phù
                                    hợp nhất cho spa của bạn.
                                </p>
                            </li>
                            <li
                                class="flex items-start gap-3"
                                data-x-aos="fade-up"
                                data-aos-delay="200"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 19 19"
                                    fill="none"
                                    class="mt-1 shrink-0 w-4 lg:w-5"
                                >
                                    <g clip-path="url(#clip0_17_58)">
                                        <path
                                            d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                            fill="#B4332B"
                                        />
                                    </g>
                                </svg>
                                <p>
                                    Tư vấn chiến lược xây dựng dịch vụ, tối ưu doanh thu và xử
                                    lý các vấn đề thực tế mà spa đang gặp phải.
                                </p>
                            </li>
                        </ul>
                    </div>

                    <!-- Consultation Images -->
                    <div
                        class="relative w-full md:w-1/2 md:mx-auto md:overflow-visible min-h-96 lg:min-h-[419px]"
                        style="background: url(assets/images/grid.svg) center / 50% no-repeat"
                    >
                        <div class="relative z-10 flex flex-col gap-4">
                            <!-- Consultation Image 1 -->

                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/benefit-1.png') }}"
                                alt="Tư vấn trực tiếp"
                                width="338.443px"
                                height="346.934px"
                                class="w-full max-h-[inherit] h-auto max-w-[339px] xl:w-[338.443px] xl:h-[346.934px] object-cover absolute top-1 left-0 lg:left-auto lg:right-9"
                                data-x-aos="zoom-in"
                            />

                            <!-- Consultation Image 2 -->

                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/benefit-2.png') }}"
                                alt="Chuyên gia hỗ trợ"
                                width="250px"
                                height="252.384px"
                                class="xl:w-[250px] max-h-[inherit] h-auto xl:h-[252.384px] object-cover absolute -left-9 top-24 xl:top-[120px] xl:left-[64px]"
                                data-x-aos="zoom-in"
                                data-aos-delay="100"
                            />
                            <!-- Curved arrow -->
                            <div
                                data-x-aos="fade-up"
                                class="absolute -left-1 top-14 xl:top-[84px] xl:left-[142px]"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="61"
                                    height="81"
                                    viewBox="0 0 61 81"
                                    fill="none"
                                >
                                    <path
                                        d="M61 6L50.8797 11.5599L51.1248 0.0154586L61 6ZM10.6053 79.8382L9.94487 80.5891C9.55489 80.2461 9.17628 79.8838 8.80927 79.5034L9.52894 78.8091L10.2486 78.1148C10.579 78.4572 10.9181 78.7816 11.2657 79.0873L10.6053 79.8382ZM7.61151 76.5009L6.79956 77.0846C6.20676 76.2601 5.6543 75.3806 5.14361 74.4529L6.01962 73.9707L6.89564 73.4884C7.37013 74.3503 7.88021 75.1615 8.42345 75.9172L7.61151 76.5009ZM4.71649 71.2787L3.79581 71.669C3.40194 70.74 3.04386 69.7748 2.72279 68.7785L3.67459 68.4718L4.6264 68.1651C4.93 69.1072 5.26751 70.0164 5.63717 70.8884L4.71649 71.2787ZM2.87207 65.5857L1.89888 65.8157C1.66705 64.8347 1.46829 63.83 1.30368 62.8053L2.29102 62.6467L3.27836 62.4881C3.43546 63.466 3.62487 64.4231 3.84527 65.3558L2.87207 65.5857ZM1.91738 59.6742L0.921563 59.7655C0.829424 58.7612 0.768873 57.7419 0.740888 56.7108L1.74052 56.6837L2.74015 56.6565C2.76698 57.6452 2.82502 58.6216 2.9132 59.5828L1.91738 59.6742ZM1.7526 53.688L0.753208 53.6531C0.788443 52.6438 0.854602 51.6263 0.952588 50.6034L1.94803 50.6987L2.94347 50.7941C2.84934 51.7767 2.78582 52.7539 2.75199 53.7229L1.7526 53.688ZM2.32295 47.7265L1.335 47.5717C1.49155 46.5723 1.67886 45.5704 1.89779 44.5683L2.87475 44.7818L3.8517 44.9952C3.64133 45.9581 3.46133 46.9209 3.3109 47.8812L2.32295 47.7265ZM3.60168 41.8751L2.63928 41.6035C2.91447 40.6285 3.22055 39.6559 3.5583 38.6878L4.50248 39.0173L5.44666 39.3467C5.12237 40.2761 4.82842 41.2103 4.56408 42.1468L3.60168 41.8751ZM5.57604 36.2191L4.65387 35.8323C5.04574 34.898 5.46861 33.9704 5.92322 33.0516L6.81951 33.4951L7.71579 33.9385C7.27986 34.8196 6.87423 35.7094 6.49821 36.6059L5.57604 36.2191ZM8.23068 30.8559L7.36423 30.3567C7.86986 29.4792 8.40641 28.6123 8.97458 27.758L9.80723 28.3118L10.6399 28.8656C10.0957 29.6838 9.58167 30.5143 9.09713 31.3552L8.23068 30.8559ZM11.545 25.8742L10.75 25.2676C11.3643 24.4626 12.0091 23.6716 12.6851 22.8965L13.4387 23.5538L14.1924 24.2111C13.5453 24.953 12.928 25.7102 12.34 26.4809L11.545 25.8742ZM15.4823 21.3601L14.7733 20.6549C15.4868 19.9376 16.2299 19.237 17.0032 18.5549L17.6648 19.3048L18.3263 20.0548C17.5858 20.708 16.8743 21.3787 16.1913 22.0654L15.4823 21.3601ZM19.9674 17.4019L19.3554 16.6111C20.1515 15.995 20.9756 15.3973 21.828 14.8197L22.389 15.6475L22.9499 16.4754C22.1323 17.0294 21.3423 17.6024 20.5794 18.1927L19.9674 17.4019ZM24.9212 14.0438L24.4123 13.183C25.2814 12.6692 26.1767 12.1751 27.0988 11.7019L27.5553 12.5916L28.0119 13.4813C27.1251 13.9363 26.2647 14.4112 25.4301 14.9046L24.9212 14.0438ZM30.2429 11.3085L29.8378 10.3943C30.7529 9.98877 31.6918 9.6029 32.6548 9.23773L33.0094 10.1728L33.3639 11.1078C32.4347 11.4602 31.5295 11.8322 30.6481 12.2228L30.2429 11.3085ZM35.8531 9.1797L35.548 8.22736C36.495 7.92403 37.4635 7.6398 38.4536 7.37552L38.7115 8.3417L38.9694 9.30788C38.0105 9.5638 37.0736 9.83882 36.1581 10.132L35.8531 9.1797ZM41.6182 7.63875L41.4057 6.6616C42.3879 6.44793 43.39 6.2531 44.4121 6.07783L44.5811 7.06345L44.7501 8.04906C43.7567 8.2194 42.7837 8.40861 41.8308 8.6159L41.6182 7.63875ZM47.5141 6.62285L47.3857 5.63113C48.3794 5.50243 49.3909 5.39151 50.4203 5.299L50.5098 6.29498L50.5994 7.29097C49.5955 7.38118 48.61 7.48927 47.6426 7.61456L47.5141 6.62285ZM53.4802 6.08295L53.427 5.08437C54.4222 5.0313 55.4332 4.99483 56.4604 4.97545L56.4792 5.97528L56.4981 6.97509C55.4935 6.99405 54.5054 7.0297 53.5335 7.08153L53.4802 6.08295Z"
                                        fill="#B4332B"
                                    />
                                </svg>
                            </div>
                            <div
                                class="absolute left-[296px] top-8 lg:top-10 lg:right-3"
                                data-x-aos="zoom-in"
                            >
                                <img
                                    src="{{ asset('assets/frontend/helen-spa/assets/images/conversation.svg') }}"
                                    width="53.922px"
                                    height="55px"
                                    alt=""
                                    class="w-[54px] h-auto"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Side: Community -->
                <div class="w-full flex flex-col lg:flex-row-reverse items-center gap-10">
                    <div class="w-full lg:w-1/2">
                        <!-- Consultation Title Button -->
                        <div class="mb-6">
                            <div
                                class="bg-[#B4332B] text-sm md:text-base text-white font-bold py-3 px-6 rounded-full inline-block"
                                data-x-aos="fade-up"
                            >
                                Tham Gia Cộng Đồng Học Viên
                            </div>
                        </div>

                        <!-- Consultation Benefits -->
                        <ul class="space-y-4 font-light text-sm md:text-base lg:text-lg">
                            <li
                                class="flex items-start gap-3"
                                data-x-aos="fade-up"
                                data-aos-delay="100"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 19 19"
                                    fill="none"
                                    class="mt-1 shrink-0"
                                >
                                    <g clip-path="url(#clip0_17_58)">
                                        <path
                                            d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                            fill="#B4332B"
                                        />
                                    </g>
                                </svg>
                                <p>
                                    Kết nối với những chủ spa khác để chia sẻ kinh nghiệm và học
                                    hỏi lẫn nhau.
                                </p>
                            </li>
                            <li
                                class="flex items-start gap-3"
                                data-x-aos="fade-up"
                                data-aos-delay="200"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 19 19"
                                    fill="none"
                                    class="mt-1 shrink-0"
                                >
                                    <g clip-path="url(#clip0_17_58)">
                                        <path
                                            d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                            fill="#B4332B"
                                        />
                                    </g>
                                </svg>
                                <p>
                                    Cập nhật xu hướng mới nhất trong ngành spa qua các buổi thảo
                                    luận và tài liệu độc quyền.
                                </p>
                            </li>
                            <li
                                class="flex items-start gap-3"
                                data-x-aos="fade-up"
                                data-aos-delay="300"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 19 19"
                                    fill="none"
                                    class="mt-1 shrink-0"
                                >
                                    <g clip-path="url(#clip0_17_58)">
                                        <path
                                            d="M18.7031 9.5C18.7031 14.5828 14.5828 18.7031 9.5 18.7031C4.41724 18.7031 0.296875 14.5828 0.296875 9.5C0.296875 4.41724 4.41724 0.296875 9.5 0.296875C14.5828 0.296875 18.7031 4.41724 18.7031 9.5ZM8.43548 14.373L15.2636 7.54486C15.4955 7.313 15.4955 6.93704 15.2636 6.70518L14.4239 5.86551C14.1921 5.63361 13.8161 5.63361 13.5842 5.86551L8.01562 11.4341L5.41578 8.83422C5.18392 8.60236 4.80796 8.60236 4.57607 8.83422L3.73639 9.67389C3.50453 9.90575 3.50453 10.2817 3.73639 10.5136L7.59577 14.3729C7.82767 14.6048 8.20358 14.6048 8.43548 14.373Z"
                                            fill="#B4332B"
                                        />
                                    </g>
                                </svg>
                                <p>
                                    Nhận hỗ trợ, lời khuyên từ các chuyên gia và cộng đồng trong
                                    quá trình áp dụng kiến thức vào thực tế.
                                </p>
                            </li>
                        </ul>
                    </div>

                    <!-- Consultation Images -->
                    <div
                        class="relative w-full overflow-hidden md:w-1/2 md:mx-auto md:overflow-visible min-h-96 lg:min-h-[419px]"
                        style="background: url({{ asset('assets/frontend/helen-spa/assets/images/grid.svg') }}) center / 50% no-repeat"
                    >
                        <div class="relative z-10 flex flex-col gap-4">
                            <!-- Consultation Image 1 -->

                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/benefit-3.png') }}"
                                alt="Tư vấn trực tiếp"
                                width="338.443px"
                                height="346.934px"
                                class="w-full h-auto max-h-[inherit] max-w-[339px] xl:w-[338.443px] xl:h-[346.934px] object-cover absolute top-1 left-0"
                                data-x-aos="zoom-in"
                            />

                            <!-- Consultation Image 2 -->

                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/benefit-4.png') }}"
                                alt="Chuyên gia hỗ trợ"
                                width="250px"
                                height="252.384px"
                                data-x-aos="zoom-in"
                                data-aos-delay="100"
                                class="w-[250px] h-[252.384px] max-h-[inherit] object-cover absolute top-24 left-[calc(min(100%,339px)-200px)] xl:top-[109px] xl:left-[170px]"
                            />
                            <!-- Curved arrow -->
                            <div
                                class="absolute xl:left-[290px] left-[calc(min(100%,339px)-60px)] top-[50px] xl:top-[61px]"
                                data-x-aos="fade-left"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="61"
                                    height="82"
                                    viewBox="0 0 61 82"
                                    fill="none"
                                >
                                    <path
                                        d="M0.685485 6.68017L10.8058 12.24L10.5606 0.695634L0.685485 6.68017ZM51.0802 80.5184L51.7406 81.2693C52.1306 80.9263 52.5092 80.564 52.8762 80.1836L52.1565 79.4893L51.4369 78.795C51.1065 79.1374 50.7674 79.4618 50.4198 79.7675L51.0802 80.5184ZM54.074 77.1811L54.8859 77.7648C55.4787 76.9402 56.0312 76.0608 56.5419 75.1331L55.6659 74.6508L54.7898 74.1686C54.3154 75.0304 53.8053 75.8417 53.262 76.5973L54.074 77.1811ZM56.969 71.9589L57.8897 72.3492C58.2835 71.4202 58.6416 70.455 58.9627 69.4587L58.0109 69.1519L57.0591 68.8452C56.7555 69.7874 56.418 70.6966 56.0483 71.5686L56.969 71.9589ZM58.8134 66.2659L59.7866 66.4959C60.0184 65.5149 60.2172 64.5101 60.3818 63.4855L59.3945 63.3269L58.4071 63.1682C58.25 64.1462 58.0606 65.1033 57.8402 66.0359L58.8134 66.2659ZM59.7681 60.3544L60.7639 60.4457C60.8561 59.4413 60.9166 58.422 60.9446 57.391L59.945 57.3638L58.9453 57.3367C58.9185 58.3253 58.8605 59.3018 58.7723 60.263L59.7681 60.3544ZM59.9329 54.3682L60.9323 54.3333C60.897 53.324 60.8309 52.3064 60.7329 51.2835L59.7375 51.3789L58.742 51.4742C58.8361 52.4569 58.8997 53.4341 58.9335 54.4031L59.9329 54.3682ZM59.3625 48.4066L60.3505 48.2519C60.1939 47.2525 60.0066 46.2505 59.7877 45.2485L58.8107 45.4619L57.8338 45.6754C58.0442 46.6382 58.2242 47.6011 58.3746 48.5614L59.3625 48.4066ZM58.0838 42.5553L59.0462 42.2837C58.771 41.3087 58.4649 40.336 58.1272 39.368L57.183 39.6974L56.2388 40.0269C56.5631 40.9563 56.8571 41.8904 57.1214 42.827L58.0838 42.5553ZM56.1094 36.8993L57.0316 36.5125C56.6397 35.5782 56.2169 34.6505 55.7623 33.7318L54.866 34.1752L53.9697 34.6187C54.4056 35.4997 54.8113 36.3896 55.1873 37.2861L56.1094 36.8993ZM53.4548 31.5361L54.3213 31.0369C53.8156 30.1594 53.2791 29.2925 52.7109 28.4382L51.8783 28.992L51.0456 29.5458C51.5898 30.364 52.1038 31.1945 52.5884 32.0354L53.4548 31.5361ZM50.1405 26.5544L50.9355 25.9478C50.3212 25.1428 49.6764 24.3518 49.0004 23.5767L48.2468 24.234L47.4931 24.8913C48.1402 25.6332 48.7574 26.3904 49.3455 27.161L50.1405 26.5544ZM46.2032 22.0403L46.9122 21.3351C46.1987 20.6178 45.4555 19.9172 44.6822 19.2351L44.0207 19.985L43.3592 20.735C44.0997 21.3882 44.8112 22.0589 45.4942 22.7456L46.2032 22.0403ZM41.7181 18.0821L42.3301 17.2912C41.534 16.6751 40.7099 16.0775 39.8574 15.4999L39.2965 16.3277L38.7355 17.1556C39.5532 17.7096 40.3432 18.2825 41.1061 18.8729L41.7181 18.0821ZM36.7643 14.724L37.2732 13.8631C36.4041 13.3494 35.5088 12.8552 34.5867 12.3821L34.1302 13.2718L33.6736 14.1615C34.5604 14.6165 35.4208 15.0914 36.2554 15.5848L36.7643 14.724ZM31.4426 11.9887L31.8477 11.0745C30.9326 10.6689 29.9937 10.2831 29.0307 9.91791L28.6761 10.8529L28.3216 11.788C29.2508 12.1403 30.156 12.5124 31.0374 12.903L31.4426 11.9887ZM25.8324 9.85988L26.1374 8.90754C25.1904 8.60421 24.222 8.31997 23.2319 8.0557L22.974 9.02188L22.7161 9.98805C23.675 10.244 24.6119 10.519 25.5274 10.8122L25.8324 9.85988ZM20.0673 8.31892L20.2798 7.34177C19.2976 7.12811 18.2955 6.93328 17.2734 6.75801L17.1044 7.74362L16.9354 8.72923C17.9288 8.89958 18.9018 9.08879 19.8547 9.29608L20.0673 8.31892ZM14.1714 7.30303L14.2998 6.31131C13.3061 6.18261 12.2946 6.07169 11.2652 5.97917L11.1756 6.97516L11.0861 7.97114C12.09 8.06136 13.0755 8.16944 14.0429 8.29474L14.1714 7.30303ZM8.20526 6.76312L8.25851 5.76454C7.26332 5.71148 6.25224 5.67501 5.22514 5.65563L5.20627 6.65545L5.18741 7.65527C6.19195 7.67423 7.18009 7.70988 8.15201 7.76171L8.20526 6.76312Z"
                                        fill="#B4332B"
                                    />
                                </svg>
                            </div>
                            <div
                                class="absolute top-8 left-0 md:-left-2 lg:-left-4"
                                data-x-aos="zoom-in"
                            >
                                <img
                                    src="{{ asset('assets/frontend/helen-spa/assets/images/conversation.svg') }}"
                                    width="53.922px"
                                    height="55px"
                                    alt=""
                                    class="w-[54px] h-auto -rotate-45"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reward section -->
    <div class="reward-section" >
        <div class="container mx-auto px-4 xl:max-w-[1140px] xl:px-0">
            <div class="flex flex-col justify-center items-center mb-[52px]">
                <h3
                    class="title linear-clip-text text-center pt-10 lg:pt-[134px] mb-4 font-extrabold lg:leading-[54px]"
                >
						<span class="text-base lg:text-[32px]" data-x-aos="fade-up">
							Bộ Quà tặng
						</span>
                    <br />

                    <span
                        data-x-aos="fade-up"
                        data-aos-delay="100"
                        class="text-xl lg:text-[52px]"
                    >
							Của Helen Hải
						</span>
                </h3>
                <div
                    class="font-bold flex pills items-end italic py-1 lg:py-3 px-8 gap-1 rounded-full text-[#bf0309] inline-block mx-auto lg:h-[60px] outline outline-2 outline-white"
                    data-x-aos="zoom-in"
                    data-aos-delay="100"
                >
						<span class="text-base lg:text-2xl font-semibold lg:leading-[34px]"
                        >TRỊ GIÁ</span
                        >
                    <strong class="font-black lg:text-[49px] lg:leading-10">
                        1.000.000đ
                    </strong>
                </div>
            </div>

            <div class="flex flex-col gap-3 md:flex-row lg:gap-1.5">
                <figure
                    class="card w-full lg:w-[566px]"
                    data-x-aos="fade-right"
                    data-aos-delay="100"
                >
                    <img
                        class="w-full h-auto aspect-[2/1] lg:h-[282px] object-cover bg-gradient-to-b from-[#fcfcf8]/0 to-[#fcfcf8] rounded-tl-[10px] relative rounded-tr-[10px]"
                        src="{{ asset('assets/frontend/helen-spa/assets/images/spa-reward-1.png') }}"
                    />
                    <figcaption
                        class="bg-[#fdeded] rounded-[10px] md:h-44 lg:h-[179.5px] border-4 border-white -mt-2 relative z-[1]"
                    >
                        <div class="h-6 md:h-8 lg:h-[22px]">
                            <div
                                class="text-white font-bold text-center py-3 px-6 rounded-full block mx-auto w-[93%] lg:h-11 text-sm lg:text-base bg-[#126ef1] shadow-[inset_0px_0px_10px_0px_rgb(218,197,96)] outline outline-2 outline-white -translate-y-1/2"
                            >
                                Tặng: Mẫu thiết kế menu câu chuyện của dịch vụ chính
                            </div>
                        </div>
                        <p
                            class="text-justify text-[#212121] text-base font-normal leading-normal pt-4 px-4 lg:px-[54px]"
                        >
                            Bộ mẫu thiết kế menu chuyên nghiệp <br />
                            Được xây dựng dựa trên chiến lược kể chuyện giúp tăng giá trị cảm
                            xúc và thu hút khách hàng.
                        </p>
                    </figcaption>
                </figure>
                <figure
                    class="card w-full lg:w-[566px]"
                    data-x-aos="fade-left"
                    data-aos-delay="100"
                >
                    <img
                        class="w-full h-auto aspect-[2/1] lg:h-[282px] object-cover bg-gradient-to-b from-[#fcfcf8]/0 to-[#fcfcf8] rounded-tl-[10px] relative rounded-tr-[10px]"
                        src="{{ asset('assets/frontend/helen-spa/assets/images/spa-reward-2.png') }}"
                    />
                    <figcaption
                        class="bg-[#fdeded] rounded-[10px] md:h-44 lg:h-[179.5px] border-4 border-white -mt-2 relative z-[1]"
                    >
                        <div class="h-6 md:h-8 lg:h-[22px]">
                            <div
                                class="text-white font-bold text-center py-3 px-6 rounded-full block mx-auto w-[93%] lg:h-11 text-sm lg:text-base bg-[#126ef1] shadow-[inset_0px_0px_10px_0px_rgb(218,197,96)] outline outline-2 outline-white -translate-y-1/2"
                            >
                                Tặng khóa: Quảng cáo Facebook từ Zero tới Hero
                            </div>
                        </div>
                        <p
                            class="text-justify text-[#212121] text-base font-normal leading-normal pt-4 px-4 lg:px-[54px]"
                        >
                            Công thức phát triển Fanpage từ 0 - 50.000 follower thật (không mua
                            like)
                            <br />
                            - Chạy quảng cáo nhắn tin bán hàng
                            <br />
                            - Chạy quảng cáo website bán hàng
                            <br />
                            - Cấu trúc quảng cáo 1-1-N, 1-N-2...
                        </p>
                    </figcaption>
                </figure>
            </div>
        </div>

        <div
            class="mt-10 lg:mt-[62px] container mx-auto pb-10 lg:pb-[90px] px-4 xl:max-w-[1140px] xl:px-0"
        >
            <!-- Title with stars -->
            <div class="text-center mb-8">
                <div
                    class="flex items-center justify-center gap-2 mb-4"
                    data-x-aos="fade-up"
                    data-aos-delay="100"
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="27"
                        height="33"
                        viewBox="0 0 27 33"
                        fill="none"
                        class="w-4 md:w-7 shrink-0"
                    >
                        <path
                            d="M25.9878 17.47C17.9027 19.1663 15.5927 21.9532 14.2067 31.889C14.0912 32.9795 12.5897 32.9795 12.4742 31.889C11.0881 21.9532 8.77811 19.1663 0.693008 17.3488C-0.231003 17.1065 -0.231003 15.7736 0.693008 15.5313C8.66261 13.8349 11.0881 11.048 12.4742 1.11225C12.5897 0.0217403 14.0912 0.0217403 14.2067 1.11225C15.5927 11.048 17.9027 13.8349 25.9878 15.6524C26.9118 15.8948 26.9118 17.3488 25.9878 17.47Z"
                            fill="white"
                        />
                    </svg>
                    <div
                        class="bg-[#F6F4E9] rounded-[100px] shadow-[inset_0px_0px_10px_0px_rgba(218,197,96,1.00)] outline outline-2 outline-white py-2 px-8"
                    >
                        <h2
                            class="text-[#6d4c33] font-bold text-center text-sm md:text-base xl:text-2xl"
                        >
                            Đăng ký ngay
                        </h2>
                    </div>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="27"
                        height="33"
                        viewBox="0 0 27 33"
                        fill="none"
                        class="w-4 md:w-7 shrink-0"
                    >
                        <path
                            d="M25.9878 17.47C17.9027 19.1663 15.5927 21.9532 14.2067 31.889C14.0912 32.9795 12.5897 32.9795 12.4742 31.889C11.0881 21.9532 8.77811 19.1663 0.693008 17.3488C-0.231003 17.1065 -0.231003 15.7736 0.693008 15.5313C8.66261 13.8349 11.0881 11.048 12.4742 1.11225C12.5897 0.0217403 14.0912 0.0217403 14.2067 1.11225C15.5927 11.048 17.9027 13.8349 25.9878 15.6524C26.9118 15.8948 26.9118 17.3488 25.9878 17.47Z"
                            fill="white"
                        />
                    </svg>
                </div>

                <h2
                    class="text-center text-white text-base lg:text-[32px] font-bold lg:leading-[38px]"
                    data-x-aos="fade-up"
                    data-aos-delay="100"
                >
                    Để Nhận Trọn Bộ Quà Tặng Và Ưu Đãi Từ
                </h2>
                <p
                    class="text-center linear-clip-text text-2xl lg:text-[52px] font-extrabold lg:leading-[54px] drop-shadow-[0px_3px_0px_rgb(196,0,9)]"
                    data-x-aos="fade-up"
                    data-aos-delay="150"
                >
                    Helen Hải
                </p>
            </div>

            <div
                id="regis-section"
                class="main-container pt-2 lg:pt-10 flex flex-wrap md:!flex-nowrap gap-7 justify-center items-center"
            >
                <!-- Left Section -->
                <div class="w-full md:w-1/2" data-x-aos="fade-right" data-aos-delay="50">
                    <!-- Pricing Section -->
                    <div class="relative w-full lg:w-fit">
                        <!-- Original Price -->
                        <div
                            class="bg-white text-[#B4332B] text-lg -top-5 left-5 font-bold py-1 px-6 rounded-full italic inline-block absolute lg:top-[-30px] lg:left-10 font-bold line-through lg:text-[30px]"
                        >
                            3.200.000đ
                        </div>

                        <!-- Discounted Price -->
                        <div
                            class="bg-[#5271ff] text-white rounded-l-full rounded-r-full lg:rounded-r-md w-full px-5 py-4 lg:px-14 lg:pr-20 mt-2 flex items-end italic lg:text-xl lg:pt-7 lg:pb-4 text-center justify-start"
                        >
								<span
                                    class="mr-2 text-base lg:text-2xl font-normal lg:leading-[34px] whitespace-nowrap"
                                >CHỈ CÒN:</span
                                >
                            <div
                                class="border-0 text-center leading-none italic text-white text-2xl lg:text-[51px] [text-shadow:_1px_2px_1px_rgb(0_32_132_/_1.00)] font-black py-0"
                            >
                                999.000đ
                            </div>
                        </div>

                        <!-- Hot Deal Badge -->
                        <div
                            class="absolute right-0 translate-x-[18%] bottom-5 lg:bottom-0 transform lg:translate-x-1/2"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/images/hot-deal.svg') }}"
                                class="w-16 lg:w-[122px] h-auto"
                                width="122"
                                height="126"
                                alt="Hot Deal"
                            />
                        </div>
                    </div>

                    <!-- Countdown Timer -->
                    <div
                        class="bg-white rounded-b-[10px] px-4 py-2 lg:px-9 lg:py-3 table mx-auto w-fit"
                    >
                        <div
                            class="flex justify-center items-start space-x-1.5 countdown-container"
                            data-target-date="2025-06-03"
                        >
                            <div class="countdown-item">
                                <div
                                    class="bg-gradient-to-b from-[#032CAC] to-[#002084] rounded-lg text-white text-base lg:text-4xl font-bold w-8 h-8 lg:w-14 lg:h-14 flex items-center justify-center days-count"
                                >
                                    00
                                </div>
                                <div class="text-center text-xs lg:text-sm mt-1">NGÀY</div>
                            </div>
                            <div
                                class="text-[#105ce4] text-base lg:text-[38.86px] font-black uppercase leading-relaxed"
                            >
                                :
                            </div>
                            <div class="countdown-item">
                                <div
                                    class="bg-gradient-to-b from-[#032CAC] to-[#002084] rounded-lg text-white text-base lg:text-4xl font-bold w-8 h-8 lg:w-14 lg:h-14 flex items-center justify-center hours-count"
                                >
                                    06
                                </div>
                                <div class="text-center text-xs lg:text-sm mt-1">GIỜ</div>
                            </div>
                            <div
                                class="text-[#105ce4] text-base lg:text-[38.86px] font-black uppercase leading-relaxed"
                            >
                                :
                            </div>
                            <div class="countdown-item">
                                <div
                                    class="bg-gradient-to-b from-[#032CAC] to-[#002084] rounded-lg text-white text-base lg:text-4xl font-bold w-8 h-8 lg:w-14 lg:h-14 flex items-center justify-center minutes-count"
                                >
                                    30
                                </div>
                                <div class="text-center text-xs lg:text-sm mt-1">PHÚT</div>
                            </div>
                            <div
                                class="text-[#105ce4] text-base lg:text-[38.86px] font-black uppercase leading-relaxed"
                            >
                                :
                            </div>
                            <div class="countdown-item">
                                <div
                                    class="bg-gradient-to-b from-[#032CAC] to-[#002084] rounded-lg text-white text-base lg:text-4xl font-bold w-8 h-8 lg:w-14 lg:h-14 flex items-center justify-center seconds-count"
                                >
                                    54
                                </div>
                                <div class="text-center text-xs lg:text-sm mt-1">GIÂY</div>
                            </div>
                        </div>
                    </div>

                    <!-- Features List -->
                    <div class="text-white space-y-2.5 my-4 lg:my-7">
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2 mt-1">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd"
                                    ></path>
                                </svg>
                            </div>
                            <div class="text-sm lg:text-xl lg:leading-[1.35]">
                                Xây dựng ma trận cho dịch vụ Spa
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2 mt-1">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd"
                                    ></path>
                                </svg>
                            </div>
                            <div class="text-sm lg:text-xl lg:leading-[1.35]">
                                Tham Gia Cộng Đồng Học Viên
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2 mt-1">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd"
                                    ></path>
                                </svg>
                            </div>
                            <div class="text-sm lg:text-xl lg:leading-[1.35]">
                                HOÀN TIỀN nếu bạn học không hiệu quả
                            </div>
                        </div>
                    </div>

                    <!-- Gift Section -->
                    <div
                        class="bg-[#b3150b] rounded-[20px] border-2 border-[#ffbd4d] border-dashed rounded-lg !p-4 lg:p-6 space-y-4"
                    >
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="26"
                                    height="26"
                                    viewBox="0 0 26 26"
                                    fill="none"
                                >
                                    <path
                                        d="M14.9643 8.48232H11.034C9.40619 8.48232 6.95822 7.10583 6.05425 6.19554C4.78207 4.91387 4.78207 2.82622 6.05583 1.54455C7.29325 0.29923 9.45202 0.29923 10.691 1.54455C11.3753 2.23359 13.1927 5.0324 12.943 6.90038H13.0552C12.8055 5.0324 14.623 2.23359 15.3088 1.54455C16.5447 0.29923 18.7034 0.29607 19.944 1.54455C21.2194 2.82622 21.2194 4.91229 19.944 6.19396C19.04 7.10425 16.5921 8.48232 14.9643 8.48232ZM14.9643 6.90196C16.0563 6.90196 18.1424 5.76727 18.822 5.08139C19.4857 4.41448 19.4857 3.3272 18.822 2.66028C18.1803 2.01392 17.0662 2.01708 16.4277 2.66028C15.3626 3.73177 14.3037 6.46895 14.6719 6.84981C14.6735 6.84981 14.7399 6.90196 14.9643 6.90196ZM8.37422 2.16089C7.92224 2.16089 7.49712 2.33789 7.17631 2.66028C6.51414 3.3272 6.51414 4.41448 7.17631 5.08139C7.85744 5.76727 9.94193 6.90196 11.034 6.90196C11.26 6.90196 11.3263 6.85139 11.3263 6.85139C11.6945 6.46737 10.6357 3.73019 9.57055 2.66028C9.24974 2.33789 8.82462 2.16089 8.37422 2.16089Z"
                                        fill="#F44336"
                                    ></path>
                                    <path
                                        d="M2.96323 12.2695V22.3063C2.96323 23.0978 3.60701 23.7401 4.39705 23.7401H21.6029C22.3944 23.7401 23.0368 23.0978 23.0368 22.3063V12.2695H2.96323Z"
                                        fill="#FFC107"
                                    ></path>
                                    <path
                                        d="M23.0368 7.96875H2.96324C2.17321 7.96875 1.52942 8.61254 1.52942 9.40257V12.9871C1.52942 13.3829 1.8506 13.704 2.24633 13.704H23.7537C24.1494 13.704 24.4706 13.3829 24.4706 12.9871V9.40257C24.4706 8.6111 23.8282 7.96875 23.0368 7.96875Z"
                                        fill="#FFD54F"
                                    ></path>
                                    <path
                                        d="M15.6112 7.96875H10.3888V23.7408H15.6112V7.96875Z"
                                        fill="#F44336"
                                    ></path>
                                </svg>
                            </div>
                            <div class="text-white text-sm lg:text-xl font-medium">
                                <span class="font-extrabold">Tặng: </span> Mẫu thiết kế menu câu
                                chuyện của dịch vụ chính
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="26"
                                    height="26"
                                    viewBox="0 0 26 26"
                                    fill="none"
                                >
                                    <path
                                        d="M14.9643 8.48232H11.034C9.40619 8.48232 6.95822 7.10583 6.05425 6.19554C4.78207 4.91387 4.78207 2.82622 6.05583 1.54455C7.29325 0.29923 9.45202 0.29923 10.691 1.54455C11.3753 2.23359 13.1927 5.0324 12.943 6.90038H13.0552C12.8055 5.0324 14.623 2.23359 15.3088 1.54455C16.5447 0.29923 18.7034 0.29607 19.944 1.54455C21.2194 2.82622 21.2194 4.91229 19.944 6.19396C19.04 7.10425 16.5921 8.48232 14.9643 8.48232ZM14.9643 6.90196C16.0563 6.90196 18.1424 5.76727 18.822 5.08139C19.4857 4.41448 19.4857 3.3272 18.822 2.66028C18.1803 2.01392 17.0662 2.01708 16.4277 2.66028C15.3626 3.73177 14.3037 6.46895 14.6719 6.84981C14.6735 6.84981 14.7399 6.90196 14.9643 6.90196ZM8.37422 2.16089C7.92224 2.16089 7.49712 2.33789 7.17631 2.66028C6.51414 3.3272 6.51414 4.41448 7.17631 5.08139C7.85744 5.76727 9.94193 6.90196 11.034 6.90196C11.26 6.90196 11.3263 6.85139 11.3263 6.85139C11.6945 6.46737 10.6357 3.73019 9.57055 2.66028C9.24974 2.33789 8.82462 2.16089 8.37422 2.16089Z"
                                        fill="#F44336"
                                    ></path>
                                    <path
                                        d="M2.96323 12.2695V22.3063C2.96323 23.0978 3.60701 23.7401 4.39705 23.7401H21.6029C22.3944 23.7401 23.0368 23.0978 23.0368 22.3063V12.2695H2.96323Z"
                                        fill="#FFC107"
                                    ></path>
                                    <path
                                        d="M23.0368 7.96875H2.96324C2.17321 7.96875 1.52942 8.61254 1.52942 9.40257V12.9871C1.52942 13.3829 1.8506 13.704 2.24633 13.704H23.7537C24.1494 13.704 24.4706 13.3829 24.4706 12.9871V9.40257C24.4706 8.6111 23.8282 7.96875 23.0368 7.96875Z"
                                        fill="#FFD54F"
                                    ></path>
                                    <path
                                        d="M15.6112 7.96875H10.3888V23.7408H15.6112V7.96875Z"
                                        fill="#F44336"
                                    ></path>
                                </svg>
                            </div>
                            <div class="text-white text-sm lg:text-xl font-medium">
                                <span class="font-extrabold">Tặng khóa: </span> Quảng cáo
                                Facebook từ Zero tới Hero
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="26"
                                    height="26"
                                    viewBox="0 0 26 26"
                                    fill="none"
                                >
                                    <path
                                        d="M14.9643 8.48232H11.034C9.40619 8.48232 6.95822 7.10583 6.05425 6.19554C4.78207 4.91387 4.78207 2.82622 6.05583 1.54455C7.29325 0.29923 9.45202 0.29923 10.691 1.54455C11.3753 2.23359 13.1927 5.0324 12.943 6.90038H13.0552C12.8055 5.0324 14.623 2.23359 15.3088 1.54455C16.5447 0.29923 18.7034 0.29607 19.944 1.54455C21.2194 2.82622 21.2194 4.91229 19.944 6.19396C19.04 7.10425 16.5921 8.48232 14.9643 8.48232ZM14.9643 6.90196C16.0563 6.90196 18.1424 5.76727 18.822 5.08139C19.4857 4.41448 19.4857 3.3272 18.822 2.66028C18.1803 2.01392 17.0662 2.01708 16.4277 2.66028C15.3626 3.73177 14.3037 6.46895 14.6719 6.84981C14.6735 6.84981 14.7399 6.90196 14.9643 6.90196ZM8.37422 2.16089C7.92224 2.16089 7.49712 2.33789 7.17631 2.66028C6.51414 3.3272 6.51414 4.41448 7.17631 5.08139C7.85744 5.76727 9.94193 6.90196 11.034 6.90196C11.26 6.90196 11.3263 6.85139 11.3263 6.85139C11.6945 6.46737 10.6357 3.73019 9.57055 2.66028C9.24974 2.33789 8.82462 2.16089 8.37422 2.16089Z"
                                        fill="#F44336"
                                    ></path>
                                    <path
                                        d="M2.96323 12.2695V22.3063C2.96323 23.0978 3.60701 23.7401 4.39705 23.7401H21.6029C22.3944 23.7401 23.0368 23.0978 23.0368 22.3063V12.2695H2.96323Z"
                                        fill="#FFC107"
                                    ></path>
                                    <path
                                        d="M23.0368 7.96875H2.96324C2.17321 7.96875 1.52942 8.61254 1.52942 9.40257V12.9871C1.52942 13.3829 1.8506 13.704 2.24633 13.704H23.7537C24.1494 13.704 24.4706 13.3829 24.4706 12.9871V9.40257C24.4706 8.6111 23.8282 7.96875 23.0368 7.96875Z"
                                        fill="#FFD54F"
                                    ></path>
                                    <path
                                        d="M15.6112 7.96875H10.3888V23.7408H15.6112V7.96875Z"
                                        fill="#F44336"
                                    ></path>
                                </svg>
                            </div>
                            <div class="text-white text-sm lg:text-xl font-medium">
                                <span class="font-extrabold">Tặng:</span> tư vấn 1-1 chuyên môn
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Section - Registration Form -->
                <div
                    class="w-full md:w-1/2 bg-white rounded-lg"
                    data-x-aos="fade-left"
                    data-aos-delay="50"
                >
                    <div
                        class="text-center bg-gradient-to-b from-white to-[#FFF4E9] px-4 pt-4 pb-3 rounded-tl-2xl rounded-tr-2xl"
                    >
                        <h2 class="text-lg lg:text-2xl font-bold text-[#051c45]">
                            ĐĂNG KÝ 1 LẦN
                            <span class="text-[#b4332b]"> HỌC TRỌN ĐỜI </span>
                        </h2>
                        <p class="text-gray-700">Đăng ký nhận ưu đãi ngay!</p>
                    </div>

                    <div class="py-6 px-3 lg:px-10 rounded-2xl">
                        <div class="rounded-lg border !border-yellow-400 p-4 lg:p-5">
                            <form class="space-y-4" id="registration-form">
                                @if(Auth()->check())
                                    <div id="success-message">
                                        <div class="flex flex-col items-center justify-center py-8 text-center">
                                            <img class="mx-auto h-auto"
                                                 src="{{ asset('assets/frontend/mst-academy/assets/images/regis-success.png') }}"
                                                 alt="Đăng ký thành công">

                                            <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                                               id="redirect-link"
                                               class="flex items-center justify-center gap-2 w-full bg-gradient-to-r from-yellow-500 to-red-500 text-white font-bold py-3 px-4 rounded-md text-xl hover:from-yellow-600 hover:to-red-600 transition duration-300 mt-4"
                                            >
                                                Vào học ngay
                                                <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35"
                                                     viewBox="0 0 35 35" fill="none">
                                                    <path
                                                        d="M15.1619 9.53757C14.9622 9.20404 14.5909 9.01257 14.2032 9.04203L5.87063 9.6857C5.02702 9.75091 4.27555 10.1931 3.8088 10.8989L0.311899 16.188C-0.0419945 16.7233 -0.0983222 17.3932 0.161305 17.98C0.420863 18.5667 0.954404 18.9756 1.58843 19.0738L8.34502 20.1203C8.39759 20.1285 8.45009 20.1325 8.50218 20.1325C8.84828 20.1324 9.17572 19.957 9.36596 19.6593L15.1462 10.6166C15.3556 10.289 15.3616 9.87116 15.1619 9.53757Z"
                                                        fill="#FFA700"/>
                                                    <path
                                                        d="M25.4621 19.839C25.1285 19.6393 24.7106 19.6454 24.383 19.8548L15.3404 25.635C14.998 25.8539 14.8171 26.2543 14.8793 26.6559L15.9257 33.4125C16.024 34.0466 16.4329 34.5801 17.0197 34.8397C17.2638 34.9476 17.5222 35.0009 17.7793 35.0009C18.1403 35.0009 18.499 34.8957 18.8116 34.6891L24.1008 31.1921C24.8066 30.7255 25.2488 29.974 25.3139 29.1303L25.9576 20.7978C25.9876 20.4101 25.7956 20.0388 25.4621 19.839Z"
                                                        fill="#F7B84C"/>
                                                    <path
                                                        d="M13.1844 28.1829C13.5925 28.591 14.1724 28.7819 14.7416 28.6867C23.089 27.2909 29.6945 20.3796 32.2092 15.5177C35.0016 10.1191 35.105 4.41415 34.9591 1.73859C34.909 0.821761 34.1782 0.0909361 33.2614 0.0408974C30.5858 -0.10498 24.8809 -0.0015534 19.4823 2.79084C14.6205 5.30555 7.70913 11.911 6.31338 20.2584C6.21822 20.8277 6.40908 21.4075 6.81718 21.8157L13.1844 28.1829Z"
                                                        fill="#F1F1FB"/>
                                                    <path
                                                        d="M34.4353 0.564453L10.0007 24.999L13.1843 28.1826C13.5924 28.5907 14.1723 28.7816 14.7416 28.6864C23.089 27.2906 29.6944 20.3793 32.2092 15.5175C35.0015 10.1189 35.105 4.41394 34.959 1.73838C34.934 1.27997 34.7388 0.868035 34.4353 0.564453Z"
                                                        fill="#D7D6FB"/>
                                                    <path
                                                        d="M23.9756 16.1494C22.6625 16.1495 21.35 15.6498 20.3503 14.6502C19.382 13.6819 18.8486 12.3943 18.8486 11.0249C18.8486 9.65547 19.382 8.36793 20.3503 7.39963C22.3492 5.40068 25.6018 5.40061 27.6008 7.39963C28.5692 8.36793 29.1025 9.65547 29.1025 11.0249C29.1025 12.3943 28.5692 13.6819 27.6008 14.6502C26.6015 15.6496 25.2883 16.1493 23.9756 16.1494Z"
                                                        fill="#466288"/>
                                                    <path
                                                        d="M6.31338 20.2593C6.21822 20.8285 6.40908 21.4084 6.81718 21.8165L13.1844 28.1837C13.5925 28.5918 14.1724 28.7826 14.7416 28.6875C16.558 28.3838 18.2917 27.8187 19.9189 27.0707L7.9302 15.082C7.18215 16.7092 6.61709 18.443 6.31338 20.2593Z"
                                                        fill="#466288"/>
                                                    <path
                                                        d="M1.04611 28.8439C1.30847 28.8439 1.57097 28.7437 1.77113 28.5435L5.11889 25.1958C5.51934 24.7954 5.51934 24.1461 5.11889 23.7456C4.71851 23.3452 4.0693 23.3452 3.66878 23.7456L0.321089 27.0934C-0.0793604 27.4938 -0.0793604 28.1431 0.321089 28.5435C0.521313 28.7438 0.783677 28.8439 1.04611 28.8439Z"
                                                        fill="white"/>
                                                    <path
                                                        d="M8.187 26.814C7.78662 26.4136 7.13734 26.4136 6.73689 26.814L0.300581 33.2503C-0.0998682 33.6506 -0.0998682 34.2999 0.300581 34.7004C0.500806 34.9006 0.763237 35.0007 1.02567 35.0007C1.2881 35.0007 1.55053 34.9005 1.75069 34.7003L8.187 28.264C8.58745 27.8636 8.58745 27.2143 8.187 26.814Z"
                                                        fill="white"/>
                                                    <path
                                                        d="M11.255 29.8823C10.8546 29.4819 10.2053 29.4819 9.80489 29.8823L6.4572 33.23C6.05675 33.6304 6.05675 34.2797 6.4572 34.6801C6.65742 34.8803 6.91985 34.9805 7.18222 34.9805C7.44458 34.9805 7.70708 34.8803 7.90724 34.6801L11.255 31.3324C11.6554 30.932 11.6554 30.2827 11.255 29.8823Z"
                                                        fill="white"/>
                                                    <path
                                                        d="M0.300537 34.699C0.500762 34.8992 0.763193 34.9993 1.02562 34.9993C1.28806 34.9993 1.55049 34.8991 1.75064 34.6989L8.18695 28.2626C8.5874 27.8622 8.5874 27.2129 8.18695 26.8125L0.300537 34.699Z"
                                                        fill="white"/>
                                                    <path
                                                        d="M10.0007 24.9979L13.1843 28.1816C13.5924 28.5897 14.1723 28.7805 14.7416 28.6854C16.5579 28.3816 18.2916 27.8166 19.9188 27.0685L13.9245 21.0742L10.0007 24.9979Z"
                                                        fill="#354A67"/>
                                                    <path
                                                        d="M20.3503 14.649C21.35 15.6486 22.6625 16.1484 23.9756 16.1482C25.2883 16.1481 26.6015 15.6483 27.6008 14.649C28.5692 13.6806 29.1025 12.3931 29.1025 11.0237C29.1025 9.65428 28.5692 8.36674 27.6008 7.39844L20.3503 14.649Z"
                                                        fill="#354A67"/>
                                                    <path
                                                        d="M23.9756 7.94922C23.1879 7.94922 22.4001 8.24904 21.8005 8.84876C21.2195 9.42975 20.8994 10.2022 20.8994 11.0239C20.8994 11.8456 21.2195 12.618 21.8005 13.199C22.9998 14.3984 24.9515 14.3984 26.1508 13.199C26.7318 12.618 27.0518 11.8455 27.0518 11.0238C27.0518 10.2021 26.7318 9.42968 26.1508 8.84869C25.5511 8.24904 24.7634 7.94922 23.9756 7.94922Z"
                                                        fill="#4BBEFD"/>
                                                    <path
                                                        d="M26.1506 8.84961L21.8003 13.1999C22.9997 14.3993 24.9513 14.3994 26.1506 13.1999C26.7316 12.6189 27.0516 11.8464 27.0516 11.0247C27.0516 10.2031 26.7317 9.4306 26.1506 8.84961Z"
                                                        fill="#0590FB"/>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                @else
                                    <div id="form-content" class="flex flex-col gap-3">
                                        <!-- Name field - only for registration -->
                                        <div class="relative" id="name-field">
                                            <label
                                                class="block text-xs md:text-sm text-gray-700 bg-white absolute -top-1 left-3 z-10"
                                            >Họ và tên *:</label
                                            >
                                            <input
                                                type="text"
                                                name="user_name"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                required=""
                                            />
                                        </div>

                                        <!-- Email field -->
                                        <div class="relative">
                                            <label
                                                class="block text-xs md:text-sm text-gray-700 bg-white absolute -top-1 left-3 z-10"
                                            >Email *:</label
                                            >
                                            <input
                                                type="email"
                                                name="user_email"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                required=""
                                            />
                                            <div
                                                class="email-error-message text-red-500 text-sm mt-1 hidden"
                                            ></div>
                                        </div>

                                        <!-- Password field -->
                                        <div class="relative">
                                            <label
                                                class="block text-xs md:text-sm text-gray-700 bg-white absolute -top-1 left-3 z-10"
                                                id="password-label"
                                            >Tạo mật khẩu *:</label
                                            >
                                            <div class="relative">
                                                <input
                                                    type="password"
                                                    name="user_password"
                                                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                    required=""
                                                />
                                                <div
                                                    class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer toggle-password"
                                                >
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        class="h-5 w-5 text-gray-400"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                        stroke="currentColor"
                                                    >
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                                        ></path>
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                                        ></path>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div
                                                class="password-error-message text-red-500 text-sm mt-1 hidden"
                                            ></div>
                                        </div>

                                        <!-- Phone field - only for registration -->
                                        <div class="relative" id="phone-field">
                                            <label
                                                class="block text-xs md:text-sm text-gray-700 bg-white absolute -top-1 left-3 z-10"
                                            >Số điện thoại *:</label
                                            >
                                            <div class="flex mt-1">
                                                <div
                                                    class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm lg:text-base"
                                                >
                                                    <img
                                                        src="https://upload.wikimedia.org/wikipedia/commons/2/21/Flag_of_Vietnam.svg"
                                                        alt="Vietnam flag"
                                                        class="h-4 w-6 mr-1"
                                                    />
                                                    +84
                                                </div>
                                                <input
                                                    type="tel"
                                                    name="user_phone"
                                                    class="flex-1 block w-full border border-gray-300 rounded-r-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                    required=""
                                                />
                                            </div>
                                        </div>

                                        <button
                                            type="submit"
                                            id="submit-btn"
                                            class="w-full bg-gradient-to-r from-[#EB2805] to-[#B4332B] text-white font-bold py-2 px-3 lg:py-3 lg:px-4 rounded-md text-sm lg:text-xl hover:from-[#B4332B] hover:to-[#B4332B] transition duration-300 mt-4"
                                        >
                                            ĐĂNG KÝ NGAY
                                        </button>
                                        <p class="text-xs md:text-sm text-center" id="form-toggle-text">
                                            Bạn đã có tài khoản?
                                            <a class="text-[#B4332B] cursor-pointer" id="form-toggle-link">
                                                Ấn vào đây để đăng nhập</a
                                            >
                                        </p>
                                    </div>

                                    <div id="loading" class="hidden">
                                        <div class="flex flex-col items-center justify-center py-8">
                                            <div
                                                class="w-16 h-16 border-4 border-t-orange-500 border-b-orange-500 border-l-gray-200 border-r-gray-200 rounded-full animate-spin"
                                            ></div>
                                            <p class="mt-4 text-gray-700 font-medium">
                                                Đang xử lý...
                                            </p>
                                        </div>
                                    </div>
                                @endif
                                <div id="error-message" class="hidden">
                                    <div
                                        class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
                                        role="alert"
                                    >
                                        <strong class="font-bold">Lỗi đăng ký!</strong>
                                        <span class="block sm:inline error-text"
                                        >Đã có lỗi xảy ra. Vui lòng thử lại sau.</span
                                        >
                                    </div>
                                </div>
                            </form>
                        </div>

                        <ul class="list-none mt-3 font-light text-[#674230]">
                            <li class="text-xs">
                                * Chú ý: Tư vấn viên sẽ liên hệ lại để xác nhận đăng ký khóa học
                                cho bạn.
                            </li>
                            <li class="text-xs">
                                * Đây là khóa học online không phải trực tiếp
                            </li>
                            <li class="text-xs">
                                * Hãy kiểm tra lại thông tin họ tên và số điện thoại của bạn đã
                                điền đúng trước khi bấm "Đăng Ký Ngay"
                            </li>
                            <li class="text-xs">* Học phí sẽ tăng khi ưu đãi kết thúc</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer section -->
    <footer class="bg-white py-8 lg:py-[60px] border-t border-gray-200">
        <div class="container mx-auto max-w-6xl px-4">
            <div class="flex flex-col md:flex-row gap-7 items-center">
                <!-- Left Side - Company Info -->
                <div class="w-full md:w-1/2" data-x-aos="fade-right">
                    <div class="text-base md:text-xl lg:text-2xl font-bold text-[#1E1E1E]">
                        <h2>BeautyEDU - Nền tảng Học tập Toàn Diện dành cho ngành làm đẹp.</h2>
                        <p class="mt-0">CÔNG TY CỔ PHẦN GIÁO DỤC BEAUTY EDU</p>
                    </div>

                    <div class="space-y-3 mt-4">
                        <!-- Hotline -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center mr-3">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 16 16"
                                    fill="none"
                                >
                                    <g clip-path="url(#clip0_36_448)">
                                        <path
                                            d="M15.5434 11.306L12.0434 9.80597C11.8939 9.74225 11.7278 9.72882 11.5699 9.76771C11.4121 9.8066 11.2712 9.8957 11.1684 10.0216L9.61844 11.9153C7.18586 10.7684 5.22819 8.81073 4.08125 6.37815L5.975 4.82815C6.10115 4.72555 6.19044 4.58465 6.22934 4.42676C6.26825 4.26888 6.25466 4.10262 6.19063 3.95315L4.69063 0.453154C4.62035 0.292032 4.49605 0.160481 4.33917 0.081185C4.18229 0.00188911 4.00266 -0.0201814 3.83125 0.0187791L0.58125 0.768779C0.41599 0.806941 0.268545 0.899991 0.16298 1.03274C0.0574141 1.16549 -3.80691e-05 1.33011 1.89256e-08 1.49972C1.89256e-08 9.51534 6.49688 15.9997 14.5 15.9997C14.6697 15.9998 14.8344 15.9424 14.9672 15.8368C15.1 15.7313 15.1931 15.5838 15.2313 15.4185L15.9813 12.1685C16.02 11.9962 15.9974 11.8159 15.9175 11.6585C15.8376 11.501 15.7053 11.3764 15.5434 11.306Z"
                                            fill="#B4332B"
                                        />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_36_448">
                                            <rect width="16" height="16" fill="white" />
                                        </clipPath>
                                    </defs>
                                </svg>
                            </div>
                            <span class="text-[#1E1E1E]">Hotline: 039 3591531</span>
                        </div>

                        <!-- Address -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center mr-3">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="18"
                                    height="18"
                                    viewBox="0 0 18 18"
                                    fill="none"
                                >
                                    <g clip-path="url(#clip0_36_451)">
                                        <path
                                            d="M8.3063 17.6368C3.19816 10.2316 2.25 9.47155 2.25 6.75C2.25 3.02207 5.27207 0 9 0C12.7279 0 15.75 3.02207 15.75 6.75C15.75 9.47155 14.8018 10.2316 9.6937 17.6368C9.35849 18.1211 8.64148 18.121 8.3063 17.6368ZM9 9.5625C10.5533 9.5625 11.8125 8.30331 11.8125 6.75C11.8125 5.19669 10.5533 3.9375 9 3.9375C7.44669 3.9375 6.1875 5.19669 6.1875 6.75C6.1875 8.30331 7.44669 9.5625 9 9.5625Z"
                                            fill="#B4332B"
                                        />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_36_451">
                                            <rect width="18" height="18" fill="white" />
                                        </clipPath>
                                    </defs>
                                </svg>
                            </div>
                            <span class="text-[#1E1E1E]"
                            >Address: R4.royal city, Thanh xuân, Hà nội.</span
                            >
                        </div>

                        <!-- Second Hotline -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center mr-3">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="18"
                                    height="18"
                                    viewBox="0 0 18 18"
                                    fill="#B4332B"
                                >
                                    <path
                                        d="M17.659 6.70781C17.7961 6.59883 18 6.70078 18 6.87305V14.0625C18 14.9941 17.2441 15.75 16.3125 15.75H1.6875C0.755859 15.75 0 14.9941 0 14.0625V6.87656C0 6.70078 0.200391 6.60234 0.341016 6.71133C1.12852 7.32305 2.17266 8.1 5.75859 10.7051C6.50039 11.2465 7.75195 12.3855 9 12.3785C10.2551 12.3891 11.5312 11.2254 12.2449 10.7051C15.8309 8.1 16.8715 7.31953 17.659 6.70781ZM9 11.25C9.81563 11.2641 10.9898 10.2234 11.5805 9.79453C16.2457 6.40898 16.6008 6.11367 17.6766 5.26992C17.8805 5.11172 18 4.86562 18 4.60547V3.9375C18 3.00586 17.2441 2.25 16.3125 2.25H1.6875C0.755859 2.25 0 3.00586 0 3.9375V4.60547C0 4.86562 0.119531 5.1082 0.323437 5.26992C1.39922 6.11016 1.7543 6.40898 6.41953 9.79453C7.01016 10.2234 8.18437 11.2641 9 11.25Z"
                                        fill="#B4332B"
                                    />
                                </svg>
                            </div>
                            <span class="text-[#1E1E1E]">Email: <EMAIL></span>
                        </div>

                        <!-- Website -->
                        <div class="flex items-center">
                            <div class="flex items-center justify-center mr-3">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="18"
                                    height="18"
                                    viewBox="0 0 18 18"
                                    fill="none"
                                >
                                    <g clip-path="url(#clip0_36_460)">
                                        <path
                                            d="M12.1113 5.625C11.6016 2.48555 10.3992 0.28125 9 0.28125C7.60078 0.28125 6.39844 2.48555 5.88867 5.625H12.1113ZM5.625 9C5.625 9.78047 5.66719 10.5293 5.74102 11.25H12.2555C12.3293 10.5293 12.3715 9.78047 12.3715 9C12.3715 8.21953 12.3293 7.4707 12.2555 6.75H5.74102C5.66719 7.4707 5.625 8.21953 5.625 9ZM17.0402 5.625C16.0348 3.23789 13.9992 1.39219 11.4855 0.646875C12.3434 1.83516 12.934 3.62461 13.2434 5.625H17.0402ZM6.51094 0.646875C4.00078 1.39219 1.96172 3.23789 0.959766 5.625H4.75664C5.0625 3.62461 5.65312 1.83516 6.51094 0.646875ZM17.4164 6.75H13.384C13.4578 7.48828 13.5 8.24414 13.5 9C13.5 9.75586 13.4578 10.5117 13.384 11.25H17.4129C17.6062 10.5293 17.7152 9.78047 17.7152 9C17.7152 8.21953 17.6062 7.4707 17.4164 6.75ZM4.5 9C4.5 8.24414 4.54219 7.48828 4.61602 6.75H0.583594C0.39375 7.4707 0.28125 8.21953 0.28125 9C0.28125 9.78047 0.39375 10.5293 0.583594 11.25H4.6125C4.54219 10.5117 4.5 9.75586 4.5 9ZM5.88867 12.375C6.39844 15.5145 7.60078 17.7188 9 17.7188C10.3992 17.7188 11.6016 15.5145 12.1113 12.375H5.88867ZM11.4891 17.3531C13.9992 16.6078 16.0383 14.7621 17.0437 12.375H13.2469C12.9375 14.3754 12.3469 16.1648 11.4891 17.3531ZM0.959766 12.375C1.96523 14.7621 4.00078 16.6078 6.51445 17.3531C5.65664 16.1648 5.06602 14.3754 4.75664 12.375H0.959766Z"
                                            fill="#B4332B"
                                        />
                                    </g>
                                    <defs>
                                        <clipPath id="clip0_36_460">
                                            <rect width="18" height="18" fill="white" />
                                        </clipPath>
                                    </defs>
                                </svg>
                            </div>
                            <span class="text-[#1E1E1E]"
                            >Website:
									<a
                                        href="mailto:<EMAIL>"
                                        class="text-blue-600 hover:underline"
                                    ><EMAIL></a
                                    ></span
                            >
                        </div>
                    </div>

                    <!-- Social Media Icons -->
                    <div class="flex flex-row mt-9 gap-3.5">
                        <a
                            class="text-[#B4332B] hover:text-white w-10 h-10 inline-flex items-center justify-center hover:bg-[#B4332B] rounded-full border border-solid border-[#B4332B]"
                            href="#"
                        >
                            <svg
                                width="8"
                                height="16"
                                viewBox="0 0 8 16"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fill-rule="evenodd"
                                    clip-rule="evenodd"
                                    d="M5.17155 15.5489H1.73521V8.22962H0.0178986V5.40907H1.73521V3.71709C1.73521 1.41775 2.70474 0.0488281 5.45949 0.0488281H7.75268V2.86937H6.31987C5.24719 2.86937 5.17585 3.26308 5.17585 3.9988L5.17155 5.40907H7.76901L7.46474 8.22962H5.17155V15.5489Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </a>
                        <a
                            class="text-[#B4332B] hover:text-white w-10 h-10 inline-flex items-center justify-center hover:bg-[#B4332B] rounded-full border border-solid border-[#B4332B]"
                            href="#"
                        >
                            <svg
                                width="16"
                                height="19"
                                viewBox="0 0 16 19"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M15.1316 5.02811V4.40172C14.3646 4.40289 13.6126 4.18731 12.9618 3.77963C13.5379 4.4126 14.2965 4.84905 15.1316 5.02811ZM11.0897 0.999651C11.0702 0.887651 11.0552 0.774912 11.0448 0.661744V0.283203H8.09502V12.0382C8.0903 13.4084 6.98219 14.5181 5.61548 14.5181C5.21424 14.5181 4.8354 14.4225 4.4999 14.2526C4.953 14.8493 5.66844 15.2345 6.47375 15.2345C7.84021 15.2345 8.94857 14.125 8.95335 12.7546V0.999651H11.0897ZM6.36814 7.31601V6.64917C6.12166 6.61536 5.87316 6.5984 5.62436 6.59852C2.63044 6.59846 0.203491 9.03402 0.203491 12.0382C0.203491 13.9217 1.15732 15.5816 2.60675 16.558C1.65077 15.5774 1.06176 14.2351 1.06176 12.7546C1.06176 9.78864 3.42699 7.37699 6.36814 7.31601Z"
                                    fill="currentColor"
                                />
                                <path
                                    d="M11.9031 6.74923C13.0549 7.57553 14.466 8.0617 15.99 8.0617V5.11864C15.7015 5.1187 15.4139 5.08852 15.1317 5.02852V7.34513C13.6078 7.34513 12.197 6.85896 11.0448 6.03272V12.0387C11.0448 15.0431 8.61789 17.4786 5.62427 17.4786C4.50728 17.4786 3.46909 17.1397 2.60666 16.5585C3.59098 17.5685 4.96369 18.1951 6.48236 18.1951C9.47615 18.1951 11.9032 15.7596 11.9032 12.755V6.74923H11.9031V6.74923ZM12.9619 3.78004C12.3732 3.13465 11.9867 2.30061 11.9031 1.37854V1H11.0898C11.2945 2.17195 11.9928 3.1732 12.9619 3.78004ZM4.50012 14.2529C4.17124 13.8202 3.99351 13.2908 3.99431 12.7464C3.99431 11.3723 5.10438 10.2581 6.47391 10.2581C6.72914 10.2581 6.98284 10.2973 7.22608 10.3747V7.36591C6.94182 7.32681 6.65494 7.31022 6.36818 7.3163V9.65824C6.12476 9.58078 5.87094 9.54144 5.61564 9.54169C4.24611 9.54169 3.1361 10.6557 3.1361 12.03C3.1361 13.0018 3.69096 13.8431 4.50012 14.2529Z"
                                    fill="currentColor"
                                />
                                <path
                                    d="M11.0448 6.03266C12.197 6.85889 13.6078 7.34507 15.1317 7.34507V5.02846C14.2811 4.84663 13.528 4.40053 12.9619 3.78004C11.9927 3.17313 11.2945 2.17189 11.0898 1H8.95339V12.7549C8.94855 14.1253 7.84038 15.2349 6.47379 15.2349C5.66848 15.2349 4.95304 14.8497 4.49994 14.2529C3.69084 13.8431 3.13599 13.0017 3.13599 12.0301C3.13599 10.6559 4.246 9.54175 5.61552 9.54175C5.87792 9.54175 6.13083 9.58275 6.36806 9.6583V7.31636C3.42703 7.37734 1.06174 9.78899 1.06174 12.755C1.06174 14.2356 1.65075 15.5778 2.60673 16.5585C3.46915 17.1397 4.50735 17.4786 5.62434 17.4786C8.61801 17.4786 11.0449 15.0431 11.0449 12.0387V6.03266H11.0448Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </a>
                        <a
                            class="text-[#B4332B] hover:text-white w-10 h-10 inline-flex items-center justify-center hover:bg-[#B4332B] rounded-full border border-solid border-[#B4332B]"
                            href="#"
                        >
                            <svg
                                width="19"
                                height="14"
                                viewBox="0 0 19 14"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M9.3689 0.015625C9.3689 0.015625 13.0378 0.0465205 15.4734 0.231445C15.8042 0.262259 16.5561 0.262759 17.2175 1.0332C17.7285 1.61899 17.9089 2.94434 17.9089 2.94434C17.9116 2.96751 18.0896 4.52769 18.0896 6.08789V7.53711C18.0896 9.07823 17.9089 10.6504 17.9089 10.6504C17.9061 10.6713 17.7246 11.9806 17.2175 12.5615C16.5562 13.3011 15.8043 13.3315 15.4734 13.3623C13.0378 13.5781 9.3689 13.5781 9.3689 13.5781C9.32825 13.5778 4.82347 13.5469 3.44604 13.3936C3.05518 13.3011 2.18275 13.3319 1.52124 12.5615C1.01437 11.9808 0.832834 10.6722 0.829834 10.6504C0.829834 10.6504 0.649186 9.10907 0.64917 7.53711V6.08789C0.649191 4.52653 0.827437 2.96529 0.829834 2.94434C0.829834 2.94434 0.980041 1.61892 1.52124 1.0332C2.18277 0.293434 2.93463 0.262278 3.26538 0.231445C5.6888 0.0474581 9.33258 0.0159343 9.3689 0.015625ZM7.2644 10.3721L12.9773 7.10547L7.2644 3.68359V10.3721Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </a>
                        <a
                            class="text-[#B4332B] hover:text-white w-10 h-10 inline-flex items-center justify-center hover:bg-[#B4332B] rounded-full border border-solid border-[#B4332B]"
                            href="#"
                        >
                            <svg
                                width="18"
                                height="18"
                                viewBox="0 0 18 18"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    d="M12.4352 0.5625C15.1407 0.562594 17.3414 2.76355 17.3414 5.46875V12.125C17.3414 14.8302 15.1407 17.0312 12.4352 17.0312H5.77795C3.07231 17.0312 0.870728 14.8303 0.870728 12.125V5.46875C0.870738 2.76349 3.07232 0.5625 5.77795 0.5625H12.4352ZM5.77698 2.21875C3.98233 2.21881 2.52718 3.67338 2.52698 5.46777V12.125C2.52716 13.9194 3.98232 15.374 5.77698 15.374H12.4342C14.2289 15.374 15.684 13.9194 15.6842 12.125V5.46777C15.684 3.67334 14.2289 2.21875 12.4342 2.21875H5.77698ZM9.10706 4.53711C11.4558 4.53734 13.3668 6.44836 13.3668 8.79688C13.3666 11.1452 11.4557 13.0554 9.10706 13.0557C6.7582 13.0557 4.84651 11.1453 4.84631 8.79688C4.84631 6.44825 6.75808 4.53711 9.10706 4.53711ZM9.10608 6.19336C7.66854 6.1934 6.50357 7.35852 6.50354 8.7959C6.50354 10.2333 7.66849 11.3984 9.10608 11.3984C10.5437 11.3984 11.7096 10.2333 11.7096 8.7959C11.7096 7.3585 10.5437 6.19336 9.10608 6.19336ZM13.3746 3.54883C13.9384 3.54883 14.396 4.00571 14.3961 4.56934C14.3961 5.13302 13.9384 5.58984 13.3746 5.58984C12.811 5.58973 12.3541 5.13295 12.3541 4.56934C12.3542 4.00578 12.811 3.54895 13.3746 3.54883Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Right Side - Google Map -->
                <div
                    class="w-full md:w-1/2 md:max-w-[553px] h-[421px] relative"
                    data-x-aos="fade-left"
                >
                    <!-- Google Map iframe -->

                    <iframe
                        loading="lazy"
                        height="421"
                        data-src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4621.972621778329!2d105.8136582759981!3d21.003151288656184!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ac9b394920f7%3A0xe80f93855fba87b2!2zUjQsIEtodSDEkcO0IHRo4buLIFJveWFsIENpdHksIFRoxrDhu6NuZyDEkMOsbmgsIFRoYW5oIFh1w6JuLCBIw6AgTuG7mWksIFZpZXRuYW0!5e1!3m2!1sen!2s!4v1747063519097!5m2!1sen!2s"
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4621.972621778329!2d105.8136582759981!3d21.003151288656184!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ac9b394920f7%3A0xe80f93855fba87b2!2zUjQsIEtodSDEkcO0IHRo4buLIFJveWFsIENpdHksIFRoxrDhu6NuZyDEkMOsbmgsIFRoYW5oIFh1w6JuLCBIw6AgTuG7mWksIFZpZXRuYW0!5e1!3m2!1sen!2s!4v1747063519097!5m2!1sen!2s"
                        class="w-full h-[421px] md:h-full border-0 rounded-lg shadow-md"
                        allowfullscreen=""
                        referrerpolicy="no-referrer-when-downgrade"
                    ></iframe>
                </div>
            </div>
        </div>
    </footer>
</div>

@if (get_frontend_settings('recaptcha_status'))
    @push('js')
        <script
            src="https://www.google.com/recaptcha/api.js?render={{ get_frontend_settings('recaptcha_sitekey') }}"></script>
    @endpush
@endif

@push('js')
<script src="{{ asset('assets/frontend/default/libs/aos/aos-*******-beta.6.js') }}"></script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Update countdown every second
        function updateCountdown() {
            const currentDate = new Date();

            // Tạo một ngày mới ở thời điểm 00:00 của ngày hôm sau
            const tomorrow = new Date(currentDate);
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);

            // Tính thời gian còn lại
            const difference = tomorrow - currentDate;

            // Calculate remaining time
            const days = Math.floor(difference / (1000 * 60 * 60 * 24));
            const hours = Math.floor(
                (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
            );
            const minutes = Math.floor(
                (difference % (1000 * 60 * 60)) / (1000 * 60)
            );
            const seconds = Math.floor((difference % (1000 * 60)) / 1000);

            // Format numbers to always display with two digits
            document.querySelector(".days-count").textContent = days
                .toString()
                .padStart(2, "0");
            document.querySelector(".hours-count").textContent = hours
                .toString()
                .padStart(2, "0");
            document.querySelector(".minutes-count").textContent = minutes
                .toString()
                .padStart(2, "0");
            document.querySelector(".seconds-count").textContent = seconds
                .toString()
                .padStart(2, "0");
        }

        // Update the countdown immediately and then every second
        updateCountdown();
        setInterval(updateCountdown, 1000);
    });
</script>

<script>
    AOS.init({
        once: true,
        duration: 650,
    });

    $(window).on("load", function () {
        $("[data-src]").each(function (ifram) {
            $(this).attr("src", $(this).data("src"));
        });

        $("[data-x-aos]").each(function name(i, element) {
            $(element).attr("data-aos", $(element).data("x-aos"));
        });

        AOS.refreshHard();
    });
</script>

<script>
    $(document).ready(function () {

        var webConfig = {
            course_slug: "{{ route('course.player', ['slug' => $course_details->slug]) }}",
            recaptcha_sitekey: "{{ get_frontend_settings('recaptcha_sitekey') }}",
            recaptcha_action: "{{ get_frontend_settings('recaptcha_action') }}",
            recaptcha_status: {{ get_frontend_settings('recaptcha_status') }},
            loginApi: "{{ route('login.course') }}",
            registerApi: "{{ route('ajax.register.learning.page') }}",
        }

        // Utility functions
        const showElement = (selector) => $(selector).removeClass('hidden');
        const hideElement = (selector) => $(selector).addClass('hidden');
        const showError = (message) => {
            showElement('#error-message');
            $('.error-text').html(message);
            setTimeout(() => hideElement('#error-message'), 5000);
        };

        // Form mode state
        let isLoginMode = false;

        // Validation rules
        const validators = {
            password: (value) => value && value.length < 8 ? 'Mật khẩu phải có ít nhất 8 kí tự' : null,
            email: (value) => {
                const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
                return value && !emailPattern.test(value) ? 'Email không hợp lệ' : null;
            }
        };

        // Real-time validation
        const setupValidation = (inputName, errorClass, validator) => {
            $(`input[name="${inputName}"]`).on('input', function() {
                const error = validator($(this).val());
                $(`.${errorClass}`).text(error || '').toggleClass('hidden', !error);
            });
        };

        // Toggle password visibility
        $('.toggle-password').click(function () {
            const passwordInput = $(this).parent().find('input');
            const isPassword = passwordInput.attr('type') === 'password';
            passwordInput.attr('type', isPassword ? 'text' : 'password');
        });

        // Setup validations
        setupValidation('user_password', 'password-error-message', validators.password);
        setupValidation('user_email', 'email-error-message', validators.email);

        // Form toggle functionality
        $('#form-toggle-link').click(function(e) {
            e.preventDefault();
            toggleFormMode();
        });

        function toggleFormMode() {
            isLoginMode = !isLoginMode;

            if (isLoginMode) {
                // Switch to login mode
                hideElement('#name-field');
                hideElement('#phone-field');
                $('#password-label').text('Mật khẩu *:');
                $('#submit-btn').text('ĐĂNG NHẬP NGAY');
                $('#form-toggle-text').html('Bạn chưa có tài khoản? <a class="text-[#B4332B] cursor-pointer" id="form-toggle-link">Ấn vào đây để đăng ký</a>');

                // Remove required attributes for hidden fields
                $('#name-field input, #phone-field input').removeAttr('required');
            } else {
                // Switch to registration mode
                showElement('#name-field');
                showElement('#phone-field');
                $('#password-label').text('Tạo mật khẩu *:');
                $('#submit-btn').text('ĐĂNG KÝ NGAY');
                $('#form-toggle-text').html('Bạn đã có tài khoản? <a class="text-[#B4332B] cursor-pointer" id="form-toggle-link">Ấn vào đây để đăng nhập</a>');

                // Add required attributes back
                $('#name-field input, #phone-field input').attr('required', '');
            }

            // Re-bind the toggle click event after updating HTML
            $('#form-toggle-link').off('click').on('click', function(e) {
                e.preventDefault();
                toggleFormMode();
            });

            // Clear form and hide error messages
            $('#registration-form')[0].reset();
            $('.email-error-message, .password-error-message').addClass('hidden');
            hideElement('#error-message');
        }

        // Form submission
        $('#registration-form').submit(async function (e) {
            e.preventDefault();

            try {
                // Validate inputs
                const password = $('input[name="user_password"]').val();
                const email = $('input[name="user_email"]').val();

                const passwordError = validators.password(password);
                const emailError = validators.email(email);

                if (passwordError) {
                    $('.password-error-message').text(passwordError).removeClass('hidden');
                    return;
                }
                if (emailError) {
                    $('.email-error-message').text(emailError).removeClass('hidden');
                    return;
                }

                // Show loading
                hideElement('#form-content');
                showElement('#loading');

                // Prepare form data based on mode
                let formData;
                let apiRoute;

                if (isLoginMode) {
                    // Login mode - only email and password
                    formData = {
                        email: email,
                        password: password,
                        _token: $('meta[name="csrf-token"]').attr('content'),
                    };
                    apiRoute = webConfig.loginApi;
                } else {
                    // Registration mode - all fields
                    formData = {
                        name: $('input[name="user_name"]').val(),
                        phone: $('input[name="user_phone"]').val(),
                        email: email,
                        course_slug: "{{$course_details->slug}}",
                        password: password,
                        password_confirmation: password,
                        _token: $('meta[name="csrf-token"]').attr('content'),
                    };
                    apiRoute = webConfig.registerApi;
                }

                if (webConfig.recaptcha_status){
                    // Handle reCAPTCHA
                    const token = await new Promise((resolve, reject) => {
                        grecaptcha.ready(() => {
                            grecaptcha.execute(webConfig.recaptcha_sitekey, {action: 'submit'})
                                .then(resolve)
                                .catch(reject);
                        });
                    });
                    formData['g-recaptcha-response'] = token;
                }

                await submitForm(formData, apiRoute);

            } catch (error) {
                console.error('Form submission error:', error);
                hideElement('#loading');
                showElement('#form-content');
                showError(error.message || 'Đã có lỗi xảy ra. Vui lòng thử lại sau.');
            }
        });

        // Submit form with fetch API
        async function submitForm(formData, apiRoute) {
            try {
                const response = await fetch(apiRoute, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();
                hideElement('#loading');

                if (response.ok && data.success) {
                    // Track purchase if Facebook Pixel is available (only for registration)
                    if (!isLoginMode && typeof fbq !== 'undefined') {
                        fbq('track', 'Purchase', {
                            value: {{$sale_price ?? 0}},
                            currency: "VND"
                        });
                    }

                    showElement('#success-message');

                    if(isLoginMode){
                        window.location.href = webConfig.course_slug;
                    }else{
                       // Redirect if URL provided
                        if (data.url_redirect) {
                            window.location.href = data.url_redirect;
                        }
                    }

                } else {
                    showElement('#form-content');
                    showError(formatErrorMessage(data));
                }
            } catch (error) {
                hideElement('#loading');
                showElement('#form-content');
                showError('Đã có lỗi xảy ra. Vui lòng thử lại sau.');
                console.error('Network error:', error);
            }
        }

        // Format error messages
        function formatErrorMessage(data) {
            if (data.errors) {
                return Object.values(data.errors)
                    .flat()
                    .map(error => Array.isArray(error) ? error[0] : error)
                    .join('<br>');
            }

            if (typeof data.message === 'object') {
                return Object.values(data.message)
                    .flat()
                    .map(error => Array.isArray(error) ? error[0] : error)
                    .join('<br>');
            }

            return data.message || 'Đã có lỗi xảy ra. Vui lòng thử lại sau.';
        }
    });
</script>
@endpush

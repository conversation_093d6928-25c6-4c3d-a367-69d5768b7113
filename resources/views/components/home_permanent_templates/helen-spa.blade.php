@extends('layouts.helen-spa')
@section('content')
    <header class="header bg-white py-4 px-8 xl:px-0 xl:py-6 top-0 z-50">
        <div class="container mx-auto xl:max-w-6xl xl:px-0 flex justify-between items-center">
            <!-- Logo Section -->
            <a href="#" class="flex items-center">
                <img
                    width="160"
                    height="40"
                    src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/logo.png') }}"
                    alt="Beauty Edu Logo"
                    class="h-8 w-auto xl:h-10"
                />
            </a>

            <!-- Navigation Menu -->
            <ul class="lg:flex items-center gap-x-7 hidden">
                <li>
                    <a
                        href="#home"
                        class="text-gray-700 hover:text-red-500 font-semibold text-base leading-none"
                    >Trang chủ</a>
                </li>
                <li>
                    <a
                        href="#about"
                        class="text-gray-700 hover:text-red-500 font-semibold text-base leading-none"
                    >Giới thiệu</a>
                </li>
                <li>
                    <a
                        href="#courses"
                        class="text-gray-700 hover:text-red-500 font-semibold text-base leading-none"
                    >Danh sách khóa học</a>
                </li>
                <li>
                    <a href="#faq" class="text-gray-700 hover:text-red-500 font-semibold text-base leading-none">Hỏi
                        đáp</a>
                </li>
                <li>
                    <a
                        href="#contact"
                        class="text-gray-700 hover:text-red-500 font-semibold text-base leading-none"
                    >Liên hệ</a>
                </li>
            </ul>

            @if(!Auth()->check())
                <!-- Login Button -->
                <button
                    class="lg:flex hidden login-button bg-red-500 text-white py-1.5 pl-1.5 pr-4 rounded-full items-center"
                    data-bs-toggle="modal"
                    data-bs-target="#modal-auth"
                >
                    <img
                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/user.svg') }}"
                        alt="User"
                        width="30"
                        height="30"
                        class="w-[30px] h-[30px] mr-1.5 object-cover object-center"

                    />
                    <span>Đăng nhập</span>
                </button>
            @else
                <!-- User Profile Dropdown -->
                <div class="relative">
                    <button
                        class="lg:flex hidden bg-white border border-gray-200 text-gray-700 py-1.5 pl-1.5 pr-4 rounded-full items-center hover:bg-gray-50 transition-colors duration-200"
                        id="user-menu-button"
                        aria-expanded="false"
                        data-bs-toggle="dropdown"
                    >
                        <img
                            src="{{ Auth()->user()->photo ? get_image(Auth()->user()->photo) : asset('assets/frontend/helen-spa/assets/home/<USER>/icons/user.svg') }}"
                            alt="User Avatar"
                            width="30"
                            height="30"
                            class="w-[30px] h-[30px] mr-2 object-cover object-center rounded-full"
                        />
                        <span class="text-sm font-medium">{{ ucfirst(Auth()->user()->name) }}</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <!-- Dropdown Menu -->
                    <div
                        class="dropdown-menu block absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 hidden"
                        id="user-dropdown">
                        <!-- User Info Header -->
                        <div class="px-4 py-3 border-b border-gray-100">
                            <div class="flex items-center">
                                <img
                                    src="{{ Auth()->user()->photo ? get_image(Auth()->user()->photo) : asset('assets/frontend/helen-spa/assets/home/<USER>/icons/user.svg') }}"
                                    alt="User Avatar"
                                    class="w-10 h-10 rounded-full object-cover mr-3"
                                />
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ ucfirst(Auth()->user()->name) }}</p>
                                    <p class="text-xs text-gray-500">{{ Auth()->user()->email }}</p>
                                    @php
                                        $enrollment_status = enroll_status(isset($course_details)?$course_details->id:0, Auth()->user()->id);
                                    @endphp
                                    @if($enrollment_status == 'valid')
                                        <span
                                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
                                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                        PRO
                                    </span>
                                    @else
                                        <span
                                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mt-1">
                                        FREE
                                    </span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Menu Items -->
                        @if (in_array(auth()->user()->role, ['admin', 'instructor']))
                            <a href="{{ route(auth()->user()->role . '.dashboard') }}"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                </svg>
                                {{ get_phrase('Dashboard') }}
                            </a>
                        @endif

                        @if (Auth()->user()->role != 'admin')
                            <a href="{{ route('my.courses') }}"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                {{ get_phrase('My Courses') }}
                            </a>

                            <a href="{{ route('my.profile') }}"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                {{ get_phrase('My Profile') }}
                            </a>

                            <a href="{{ route('my.affiliate') }}"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                {{ get_phrase('Affiliate') }}
                            </a>

                            <a href="{{ route('wishlist') }}"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                {{ get_phrase('Wishlist') }}
                            </a>

                            <a href="{{ route('message') }}"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                {{ get_phrase('Message') }}
                            </a>

                            <a href="{{ route('purchase.history') }}"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ get_phrase('Purchase History') }}
                            </a>
                        @endif

                        <div class="border-t border-gray-100 mt-2 pt-2">
                            <a href="{{ route('logout.course') }}"
                               class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                {{ get_phrase('Log Out') }}
                            </a>
                        </div>
                    </div>
                </div>
            @endif
            <button class="block lg:hidden menu-toggle">
                <img src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/bar.svg') }}" alt=""
                     class="w-6 h-6"/>
            </button>
        </div>

        <!-- Mobile Navigation Menu (hidden by default) -->
        <div
            id="mobile-menu"
            class="hidden pt-0 pb-4 absolute top-14 left-0 px-8 w-full bg-white z-50"
        >
            <div class="flex flex-col space-y-3">
                <a href="#home" class="text-gray-700 hover:text-red-500 font-medium py-1"
                >Trang chủ</a
                >
                <a href="#about" class="text-gray-700 hover:text-red-500 font-medium py-1"
                >Giới thiệu</a
                >
                <a href="#courses" class="text-gray-700 hover:text-red-500 font-medium py-1"
                >Danh sách khóa học</a
                >
                <a href="#faq" class="text-gray-700 hover:text-red-500 font-medium py-1"
                >Hỏi đáp</a
                >
                <a href="#contact" class="text-gray-700 hover:text-red-500 font-medium py-1"
                >Liên hệ</a
                >

                @if(!Auth()->check())
                    <button
                        class="login-button bg-red-500 text-white py-1.5 pl-1.5 pr-4 rounded-full flex justify-center items-center"
                        data-bs-toggle="modal"
                        data-bs-target="#modal-auth"
                    >
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/user.svg') }}"
                            alt="User"
                            width="30"
                            height="30"
                            class="w-7 h-7 mr-2 object-cover object-center"
                        />
                        <span>Đăng nhập</span>
                    </button>
                @else
                    <!-- Mobile User Profile -->
                    <div class="bg-gray-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center mb-3">
                            <img
                                src="{{ Auth()->user()->photo ? get_image(Auth()->user()->photo) : asset('assets/frontend/helen-spa/assets/home/<USER>/icons/user.svg') }}"
                                alt="User Avatar"
                                class="w-10 h-10 rounded-full object-cover mr-3"
                            />
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ ucfirst(Auth()->user()->name) }}</p>
                                <p class="text-xs text-gray-500">{{ Auth()->user()->email }}</p>
                                @php
                                    $enrollment_status = enroll_status(isset($course_details)?$course_details->id:0, Auth()->user()->id);
                                @endphp
                                @if($enrollment_status == 'valid')
                                    <span
                                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
                                    PRO
                                </span>
                                @else
                                    <span
                                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mt-1">
                                    FREE
                                </span>
                                @endif
                            </div>
                        </div>

                        <!-- Mobile Menu Items -->
                        <div class="space-y-1">
                            @if (in_array(auth()->user()->role, ['admin', 'instructor']))
                                <a href="{{ route(auth()->user()->role . '.dashboard') }}"
                                   class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-white rounded">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                    </svg>
                                    {{ get_phrase('Dashboard') }}
                                </a>
                            @endif

                            @if (Auth()->user()->role != 'admin')
                                <a href="{{ route('my.courses') }}"
                                   class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-white rounded">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                    {{ get_phrase('My Courses') }}
                                </a>

                                <a href="{{ route('my.profile') }}"
                                   class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-white rounded">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    {{ get_phrase('My Profile') }}
                                </a>

                                <a href="{{ route('wishlist') }}"
                                   class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-white rounded">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                    {{ get_phrase('Wishlist') }}
                                </a>

                                <a href="{{ route('purchase.history') }}"
                                   class="flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-white rounded">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {{ get_phrase('Purchase History') }}
                                </a>
                            @endif

                            <a href="{{ route('logout.course') }}"
                               class="flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                {{ get_phrase('Log Out') }}
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>

    </header>

    <div
        id="home"
        class="hero bg-cover bg-no-repeat bg-center pb-45 xl:pb-[160px]"
        style="background-image: url({{asset('assets/frontend/helen-spa/assets/home/<USER>/hero-background.png')}})"
    >
        <div
            class="container max-w-full mx-auto xl:max-w-6xl gap-y-10 lg:gap-y-0 gap-x-10 xl:gap-x-20 px-4 xl:px-0 flex flex-col lg:flex-row items-center"
        >
            <div class="w-full md:w-auto">
                <div
                    style="background-image: url({{asset('assets/frontend/helen-spa/assets/home/<USER>/icons/hero-buble-background.svg')}})"
                    class="w-64 xl:w-[280px] xl:h-[70px] text-xl xl:text-[26.32px] pt-2 pb-4 xl:pt-2.5 xl:pb-2.5 mx-auto flex justify-center items-start text-white font-extrabold bg-no-repeat bg-contain bg-center"
                    data-x-aos="fade-up-right"
                >
                    Học Online
                </div>
                <div
                    class="text-2xl md:text-4xl leading-tight xl:text-[45px] font-extrabold text-gray-700 capitalize xl:leading-[52px] text-center tracking-[-0.45px]"
                    data-x-aos="fade-up-right"
                    data-aos-delay="150"
                >
                    Chinh phục mô hình
                    <br/>
                    <span class="gradient-text"> kinh doanh SPA </span>
                </div>
                <div
                    class="flex items-center max-w-full overflow-x-hidden scale-110 -my-4 md:my-0 md:transform-none md:-mx-8"
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="316"
                        height="219"
                        viewBox="0 0 316 219"
                        fill="none"
                        data-x-aos="fade-left"
                    >
                        <g filter="url(#filter0_di_252_225)">
                            <path
                                d="M283 70.076C283 74.1002 280.595 77.5115 277.621 79.6459C270.797 84.5424 266.202 93.7335 266.202 104.266C266.202 114.798 270.797 123.99 277.621 128.886C280.595 131.02 283 134.432 283 138.456V172.933C283 177.941 279.461 182 275.095 182H40.9051C36.5393 182 33 177.941 33 172.933V38.0667C33 33.0593 36.5393 29 40.9051 29H275.095C279.461 29 283 33.0593 283 38.0667V70.076Z"
                                fill="url(#pattern0_252_225)"
                            />
                        </g>
                        <defs>
                            <filter
                                id="filter0_di_252_225"
                                x="0"
                                y="0"
                                width="316"
                                height="219"
                                filterUnits="userSpaceOnUse"
                                color-interpolation-filters="sRGB"
                            >
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix
                                    in="SourceAlpha"
                                    type="matrix"
                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                    result="hardAlpha"
                                />
                                <feOffset dy="4"/>
                                <feGaussianBlur stdDeviation="16.5"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix
                                    type="matrix"
                                    values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.55 0"
                                />
                                <feBlend
                                    mode="normal"
                                    in2="BackgroundImageFix"
                                    result="effect1_dropShadow_252_225"
                                />
                                <feBlend
                                    mode="normal"
                                    in="SourceGraphic"
                                    in2="effect1_dropShadow_252_225"
                                    result="shape"
                                />
                                <feColorMatrix
                                    in="SourceAlpha"
                                    type="matrix"
                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                    result="hardAlpha"
                                />
                                <feOffset dy="4"/>
                                <feGaussianBlur stdDeviation="10"/>
                                <feComposite
                                    in2="hardAlpha"
                                    operator="arithmetic"
                                    k2="-1"
                                    k3="1"
                                />
                                <feColorMatrix
                                    type="matrix"
                                    values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"
                                />
                                <feBlend
                                    mode="normal"
                                    in2="shape"
                                    result="effect2_innerShadow_252_225"
                                />
                            </filter>
                            <pattern
                                id="pattern0_252_225"
                                patternContentUnits="objectBoundingBox"
                                width="1"
                                height="1"
                            >
                                <use
                                    xlink:href="#image0_252_225"
                                    transform="matrix(0.000651042 0 0 0.00106379 0 -0.589325)"
                                />
                            </pattern>
                            <image
                                id="image0_252_225"
                                width="1536"
                                height="2048"
                                preserveAspectRatio="none"
                                xlink:href="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/hero-left-1.png') }}"
                            />
                        </defs>
                    </svg>

                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                        width="335"
                        height="225"
                        viewBox="0 0 335 225"
                        fill="none"
                        class="ml-[-75px]"
                        data-x-aos="fade-left"
                        data-aos-delay="200"
                    >
                        <g filter="url(#filter0_di_252_221)">
                            <path
                                d="M299 175.933C299 180.941 295.472 185 291.12 185H58.6554C54.3033 185 50.7753 180.941 50.7753 175.933V137.711C50.7753 133.573 48.2201 130.111 45.2238 127.823C39.6929 123.6 36 115.974 36 107.266C36.0001 98.5583 39.6929 90.9314 45.2237 86.7079C48.22 84.4198 50.7753 80.9577 50.7753 76.82V41.0667C50.7753 36.0593 54.3033 32 58.6554 32H291.12C295.472 32 299 36.0593 299 41.0667V175.933Z"
                                fill="url(#pattern0_252_221)"
                            />
                            <path
                                d="M291.12 30.5C296.491 30.5002 300.5 35.4353 300.5 41.0664V175.934C300.5 181.565 296.491 186.5 291.12 186.5H58.6553C53.2843 186.5 49.2755 181.565 49.2754 175.934V137.711C49.2754 134.435 47.3674 131.532 44.8291 129.426L44.3135 129.016C38.3601 124.47 34.5 116.373 34.5 107.266L34.5117 106.415C34.7438 97.6561 38.5462 89.9197 44.3135 85.5156L44.8291 85.1055C47.3674 82.9992 49.2753 80.0961 49.2754 76.8203V41.0664C49.2755 35.4352 53.2843 30.5001 58.6553 30.5H291.12Z"
                                stroke="white"
                                stroke-width="3"
                            />
                        </g>
                        <defs>
                            <filter
                                id="filter0_di_252_221"
                                x="0"
                                y="0"
                                width="335"
                                height="225"
                                filterUnits="userSpaceOnUse"
                                color-interpolation-filters="sRGB"
                            >
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix
                                    in="SourceAlpha"
                                    type="matrix"
                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                    result="hardAlpha"
                                />
                                <feOffset dy="4"/>
                                <feGaussianBlur stdDeviation="16.5"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix
                                    type="matrix"
                                    values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.55 0"
                                />
                                <feBlend
                                    mode="normal"
                                    in2="BackgroundImageFix"
                                    result="effect1_dropShadow_252_221"
                                />
                                <feBlend
                                    mode="normal"
                                    in="SourceGraphic"
                                    in2="effect1_dropShadow_252_221"
                                    result="shape"
                                />
                                <feColorMatrix
                                    in="SourceAlpha"
                                    type="matrix"
                                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                                    result="hardAlpha"
                                />
                                <feOffset dy="4"/>
                                <feGaussianBlur stdDeviation="10"/>
                                <feComposite
                                    in2="hardAlpha"
                                    operator="arithmetic"
                                    k2="-1"
                                    k3="1"
                                />
                                <feColorMatrix
                                    type="matrix"
                                    values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"
                                />
                                <feBlend
                                    mode="normal"
                                    in2="shape"
                                    result="effect2_innerShadow_252_221"
                                />
                            </filter>
                            <pattern
                                id="pattern0_252_221"
                                patternContentUnits="objectBoundingBox"
                                width="1"
                                height="1"
                            >
                                <use
                                    xlink:href="#image0_252_221"
                                    transform="matrix(0.000651042 0 0 0.00126833 0 -1.12663)"
                                />
                            </pattern>
                            <image
                                id="image0_252_221"
                                width="1536"
                                height="2048"
                                preserveAspectRatio="none"
                                xlink:href="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/hero-left-2.png') }}"
                            />
                        </defs>
                    </svg>
                </div>

                <div class="flex items-center gap-2.5">
                    <div
                        class="rounded-xl bg-red-500 border-2 border-solid border-[#FF6363] flex-auto flex flex-row justify-center items-center min-h-[68px]"
                        data-x-aos="fade-up"
                    >
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/community.svg') }}"
                            class="w-8 h-8 md:w-11 md:h-11 mr-1.5"
                            width="44"
                            height="44"
                            alt=""
                        />
                        <div class="text-white">
                            <p
                                class="text-lg md:text-xl xl:text-[26.52px] leading-none font-extrabold tracking-[-0.265px]"
                            >
                                1000+
                            </p>
                            <p class="text-sm xl:text-[15.01px] font-normal">Học viên</p>
                        </div>
                    </div>
                    <div
                        class="rounded-xl bg-red-500 border-2 border-solid border-[#FF6363] flex-auto flex flex-row justify-center items-center min-h-[68px]"
                        data-x-aos="fade-up"
                        data-aos-delay="150"
                    >
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/presentation.svg') }}"
                            class="w-8 h-8 md:w-11 md:h-11 mr-1.5"
                            width="44"
                            height="44"
                            alt=""
                        />
                        <div class="text-white">
                            <p
                                class="text-lg md:text-xl xl:text-[26.52px] leading-none font-extrabold tracking-[-0.265px]"
                            >
                                400+
                            </p>
                            <p class="text-sm xl:text-[15.01px] font-normal">Lớp học</p>
                        </div>
                    </div>
                    <div
                        class="rounded-xl bg-red-500 border-2 border-solid border-[#FF6363] flex-auto flex flex-row justify-center items-center min-h-[68px]"
                        data-x-aos="fade-up"
                        data-aos-delay="200"
                    >
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/chat.svg') }}"
                            class="w-8 h-8 md:w-11 md:h-11 mr-1.5"
                            width="44"
                            height="44"
                            alt=""
                        />
                        <div class="text-white">
                            <p class="text-sm xl:text-[15.01px] font-normal">Tư vấn</p>
                            <p
                                class="text-lg md:text-xl xl:text-[26.52px] leading-none font-extrabold tracking-[-0.265px]"
                            >
                                1-1
                            </p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-center w-full">
                    <a href="#courses"
                        class="pr-3 h-[51px] hover:!text-[#CD1F00] text-[#CD1F00] text-xl inline-flex items-center font-bold mt-4 animate-[zoom_2s_ease-in-out_infinite]"
                        data-aos="zoom-in"
                        data-aos-duration="1000"
                        style="
                            border-radius: 66.048px;
                            border: 1.264px solid #FFF;
                            background: linear-gradient(90deg, #FFF6DE 0%, #FFDF86 100%);
                            box-shadow: 0px 5.057px 5.057px 0px rgba(0, 0, 0, 0.25);">
                        <img src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/hat-icon.png') }}"
                            alt=""
                            class="translate-y-1">
                        XEM KHÓA HỌC
                    </a>

                    <style>
                        @keyframes zoom {
                            0%, 100% {
                                transform: scale(1);
                            }
                            50% {
                                transform: scale(1.1);
                            }
                        }
                    </style>
                </div>


            </div>
            <img
                data-x-aos="fade-left"
                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/hero-right.png') }}"
                alt="Helen Hải"
                class="w-[400px] -mb-10 lg:mt-auto xl:mt-0 xl:mb-0 h-auto xl:w-[554px] xl:h-[642px] hero-left-image"
                width="554px"
                height="642px"
            />
        </div>
    </div>

    <div id="about" class="story relative py-10 lg:py-16 -mt-44 xl:-mt-[200px]">
        <div class="story-bg absolute h-full top-0 left-0 w-full">
            <img
                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/stoty-bg.svg') }}"
                alt=""
                class="w-full object-cover object-top"
            />
        </div>
        <div class="container mx-auto xl:max-w-6xl px-4 xl:px-0 relative z-10">
            <!-- Story Title -->
            <div class="text-center mb-4 md:mb-12">
                <h2
                    class="text-xl md:text-4xl xl:text-[52px] font-normal leading-none text-red-500 mb-1 md:mb-0"
                    data-x-aos="fade-up"
                >
                    <span class="playball-font"> Hành trình </span>
                    <span
                        class="text-3xl md:text-7xl xl:text-[77.66px] font-extrabold md:tracking-[-4.659px]"
                    >8 năm</span
                    >
                </h2>
                <p
                    class="text-gray-700 text-xl md:text-3xl xl:text-4xl font-bold leading-none"
                    data-x-aos="fade-up"
                    data-aos-delay="200"
                >
                    Trong lĩnh vực Spa của Helen Hải
                </p>
            </div>

            <!-- Story Content -->
            <div class="relative flex flex-col gap-y-5 items-start overflow-x-hidden">
                <div class="flex flex-col md:flex-row gap-4 md:gap-10 lg:gap-[68px]">
                    <!-- Profile Image -->
                    <div
                        class="overflow-hidden h-fit md:shrink-0 md:grow-0 md:basis-[346px]"
                        data-x-aos="fade-right"
                        data-aos-delay="250"
                    >
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/story/helen.png') }}"
                            alt="Helen Hải"
                            height="520px"
                            width="346px"
                            class="w-full h-auto"
                        />
                    </div>

                    <!-- Story Text -->
                    <div
                        class="text-gray-700 text-sm md:text-base"
                        data-x-aos="fade-left"
                        data-aos-delay="250"
                    >
                        <p class="mb-4">
                            Xin chào, tôi là <strong>Helen Hải</strong> – giảng viên quốc tế
                            được chứng nhận ISO trong bộ môn da liễu thẩm mỹ. Với hơn 8 năm kinh
                            nghiệm trong ngành làm đẹp, tôi đã có cơ hội đào tạo hơn 400 lớp học
                            từ cơ bản đến chuyên sâu và là tác giả của hơn 50 bộ sách chuyên
                            ngành.
                        </p>
                        <p class="mb-4">
                            Hiện tại, tôi đang đảm nhiệm vai trò Giám đốc chuyên môn Nhãn hàng
                            EZB, đồng thời là Founder của Trung tâm Phục Sắc EZB và Perfect Skin
                            Medical. Tôi cũng là CEO và giảng viên tại BeautyEDU – hệ thống giáo
                            dục chuyên sâu trong lĩnh vực spa. Với kinh nghiệm giảng dạy, kinh
                            doanh và tư vấn chiến lược, tôi luôn mong muốn đồng hành và truyền
                            cảm hứng giúp học viên phát triển toàn diện trong ngành làm đẹp.
                        </p>
                    </div>
                </div>

                <!-- Gallery -->
                <div class="static lg:absolute bottom-10 right-0">
                    <div
                        class="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-0 w-full lg:w-[800px] xl:w-[920px] lg:h-[159px]"
                    >
                        <div
                            class="overflow-hidden"
                            data-x-aos="fade-left"
                            data-aos-delay="300"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/story/gallery1.png') }}"
                                alt="Gallery 1"
                                class="w-full h-auto"
                                width="230px"
                                height="159px"
                            />
                        </div>
                        <div
                            class="overflow-hidden"
                            data-x-aos="fade-left"
                            data-aos-delay="350"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/story/gallery2.png') }}"
                                alt="Gallery 2"
                                class="w-full h-auto"
                                width="230px"
                                height="159px"
                            />
                        </div>
                        <div
                            class="overflow-hidden"
                            data-x-aos="fade-left"
                            data-aos-delay="400"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/story/gallery3.png') }}"
                                alt="Gallery 3"
                                class="w-full h-auto"
                                width="230px"
                                height="159px"
                            />
                        </div>
                        <div
                            class="overflow-hidden"
                            data-x-aos="fade-left"
                            data-aos-delay="450"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/story/gallery4.png') }}"
                                alt="Gallery 4"
                                class="w-full h-auto"
                                width="230px"
                                height="159px"
                            />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Courses Section -->
            <div id="courses" class="courses pt-8 lg:pt-16 relative">
                <div class="container mx-auto px-0 md:px-0">
                    <div
                        class="text-center text-xl md:text-3xl xl:text-4xl font-bold mb-2 md:mb-7 lg:mb-10 xl:mb-12"
                    >
                        <h2 class="mb-1 md:mb-2 xl:mb-4 text-red-500" data-x-aos="fade-up">
                            Các Khóa Học
                        </h2>
                        <p class="text-gray-700" data-x-aos="fade-up" data-aos-delay="200">
                            Từ cơ bản đến nâng cao
                        </p>
                    </div>

                    <div class="flex flex-row flex-wrap justify-center lg:!flex-nowrap gap-6 mt-4">
                        @if(isset($courses) && $courses->count()>0)
                            @foreach($courses as $course)
                                <a
                                    href="{{ route('course.details', $course->slug) }}"
                                    class="course-card w-80 lg:w-1/3 !p-3 bg-white rounded-xl overflow-hidden shadow-lg transition-all duration-300 hover:shadow-xl"
                                    data-x-aos="fade-up"
                                >
								<span class="relative">
									<img
                                        src="{{ get_image($course->thumbnail) }}"
                                        alt="{{$course->title}}"
                                        class="w-full h-auto object-cover"
                                    />
									<span
                                        class="course-tag absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2 px-8 py-1 rounded-full text-sm xl:text-base text-[#674230] font-bold"
                                    >
										Khóa học
									</span>
								</span>
                                    <span class="text-center block !p-5">
									<span
                                        class="text-base md:text-lg xl:text-xl font-bold uppercase text-red-500 text-center leading-tight line-clamp-2"
                                    >
										{{$course->title}}
									</span>
								</span>
                                </a>
                            @endforeach
                        @endif
                    </div>

                                    <div class="text-center mt-12">
                                        <a href="{{ route('courses') }}"
                                            class="course-button relative pl-14 pr-4 py-2.5 bg-gradient-to-r from-[#fff6dd] to-[#ffde86] text-red-500 text-base md:text-xl xl:text-2xl font-bold rounded-full shadow-md hover:shadow-lg transition-all inline-flex justify-center items-center mx-auto border border-solid border-white group"
                                            data-x-aos="zoom-in"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/learning.svg') }}"
                                                alt=""
                                                width="38"
                                                height="38"
                                                class="absolute top-1.5 left-1.5 w-8 h-8 md:w-[38px] md:h-[38px] course-icon animate-icon rounded-full"
                                            />
                                            <span class="course-text transition-all">XEM THÊM KHÓA HỌC</span>
                                        </a>
                                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Why Join Section -->
    <div class="reason pt-16 pb-28 md:pb-40 relative relative z-10">
        <div class="absolute inset-0">
            <img
                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/reason-bg.png') }}"
                alt=""
                class="w-full h-full object-cover lg:object-fill"
            />
        </div>

        <div class="container xl:max-w-6xl px-4 xl:px-0 mx-auto overflow-hidden relative z-10">
            <!-- Main Image -->
            <div class="flex justify-center relative w-fit mx-auto">
                <div
                    class="rounded-full absolute top-1/2 left-1/2 z-0 -translate-x-1/2 -translate-y-1/2 max-w-full overflow-hidden"
                    style="
							width: 1000px;
							height: 412px;
							background: radial-gradient(
								ellipse 46.18% 33.43% at 49.36% 51.31%,
								white 25%,
								rgba(255, 57.87, 57.87, 0) 100%
							);
						"
                ></div>
                <img
                    data-x-aos="zoom-out"
                    src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/reason.png') }}"
                    alt="Helen Hai"
                    width="872px"
                    height="488px"
                    class="relative z-10 w-full lg:w-[800px] xl:w-[872px] h-auto relative z-10"
                />
            </div>

            <!-- Section Title -->
            <div class="text-center mb-4 md:mb-10 -mt-4 md:-mt-10">
                <div
                    class="bg-[#f6f4e9] rounded-full shadow-[inset_0px_0px_10px_0px_rgb(218,197,96)] outline outline-2 outline-white rounded-full px-[50px] py-1 md:py-1.5 mb-2 w-fit mx-auto"
                    data-x-aos="fade-up"
                >
                    <h3 class="text-[#674230] font-bold text-base md:text-xl xl:text-[26px]">
                        Tại sao nên đồng hành
                    </h3>
                </div>
                <h2
                    class="text-xl md:text-5xl xl:text-[52px] font-bold text-white mb-4 md:mb-9"
                    data-x-aos="fade-up"
                    data-aos-delay="200"
                >
                    Cùng giảng viên Helen Hải?
                </h2>
            </div>

            <!-- Feature Boxes -->
            <div class="flex flex-wrap justify-center lg:!flex-nowrap gap-6 mx-auto">
                <!-- Feature 1 -->
                <div
                    class="bg-white rounded-lg md:rounded-3xl !px-5 !py-3 shadow-lg transform w-full md:w-[220px] lg:w-1/4 transition-transform hover:scale-105"
                    data-x-aos="zoom-in"
                >
                    <div class="flex justify-start items-center mb-2.5">
                        <div
                            class="bg-red-500 w-12 xl:w-[52px] h-12 xl:h-[52px] shrink-0 grow-0 basis-12 xl:basis-[52px] flex justify-center items-center rounded-full mr-3"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/chat.svg') }}"
                                alt="Chat Icon"
                                class="w-7 h-7"
                            />
                        </div>
                        <h3 class="text-red-500 font-bold text-base xl:text-lg font-extrabold">
                            Tư vấn trực tiếp 1 - 1
                        </h3>
                    </div>
                    <p class="text-gray-700 text-sm xl:text-base font-normal text-justify">
                        Tư vấn và giải đáp trực tiếp những thắc mắc của học viên
                    </p>
                </div>

                <!-- Feature 2 -->

                <div
                    class="bg-white rounded-lg md:rounded-3xl !px-5 !py-3 shadow-lg transform w-full md:w-[220px] lg:w-1/4 transition-transform hover:scale-105"
                    data-x-aos="zoom-in"
                    data-aos-delay="200"
                >
                    <div class="flex justify-start items-center mb-2.5">
                        <div
                            class="bg-red-500 w-12 xl:w-[52px] h-12 xl:h-[52px] shrink-0 grow-0 basis-12 xl:basis-[52px] flex justify-center items-center rounded-full mr-3"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/community.svg') }}"
                                alt="Chat Icon"
                                class="w-7 h-7 xl:w-9 xl:h-9"
                            />
                        </div>
                        <h3 class="text-red-500 font-bold text-base xl:text-lg font-extrabold">
                            Tham gia cộng đồng học viên
                        </h3>
                    </div>
                    <p class="text-gray-700 text-sm xl:text-base font-normal text-justify">
                        Kết nối, trao đổi, chia sẻ, học hỏi lẫn nhau giữa các chủ Spa
                    </p>
                </div>

                <!-- Feature 3 -->

                <div
                    class="bg-white rounded-lg md:rounded-3xl !px-5 !py-3 shadow-lg transform w-full md:w-[220px] lg:w-1/4 transition-transform hover:scale-105"
                    data-x-aos="zoom-in"
                    data-aos-delay="400"
                >
                    <div class="flex justify-start items-center mb-2.5">
                        <div
                            class="bg-red-500 w-12 xl:w-[52px] h-12 xl:h-[52px] shrink-0 grow-0 basis-12 xl:basis-[52px] flex justify-center items-center rounded-full mr-3"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/treatment.svg') }}"
                                alt="Chat Icon"
                                class="w-7 h-7 xl:w-[30px] xl:h-[30px]"
                            />
                        </div>
                        <h3 class="text-red-500 font-bold text-base xl:text-lg font-extrabold">
                            Giáo trình cá nhân hóa
                        </h3>
                    </div>
                    <p class="text-gray-700 text-sm xl:text-base font-normal text-justify">
                        Giáo trình được cá nhân hóa theo nhu cầu của từng học viên, được update
                        liên tục các công nghệ, kiến thức HOT nhất của ngành Spa
                    </p>
                </div>

                <!-- Feature 4 -->

                <div
                    class="bg-white rounded-lg md:rounded-3xl !px-5 !py-3 shadow-lg transform w-full md:w-[220px] lg:w-1/4 transition-transform hover:scale-105"
                    data-x-aos="zoom-in"
                    data-aos-delay="600"
                >
                    <div class="flex justify-start items-center mb-2.5">
                        <div
                            class="bg-red-500 w-12 xl:w-[52px] h-12 xl:h-[52px] shrink-0 grow-0 basis-12 xl:basis-[52px] flex justify-center items-center rounded-full mr-3"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/schedule.svg') }}"
                                alt="Chat Icon"
                                class="w-7 h-7 xl:w-[30px] xl:h-[30px]"
                            />
                        </div>
                        <h3 class="text-red-500 font-bold text-base xl:text-lg font-extrabold">
                            Lịch học linh hoạt
                        </h3>
                    </div>
                    <p class="text-gray-700 text-sm xl:text-base font-normal text-justify">
                        Giảng viên sẽ sắp xếp lớp phù hợp với lịch của từng học viên
                    </p>
                </div>
            </div>

            @if(!Auth()->check())
                <button
                    class="relative mt-6 md:mt-12 pl-14 pr-4 py-2.5 bg-gradient-to-r from-[#fff6dd] to-[#ffde86] text-red-500 text-xl xl:text-2xl font-bold rounded-full shadow-md hover:shadow-lg transition-all flex justify-center items-center mx-auto border border-solid border-white group register-button"
                    data-x-aos="fade-up"
                    data-bs-toggle="modal"
                    data-bs-target="#modal-auth"
                >
                    <img
                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/user-sign-up.svg') }}"
                        class="absolute top-1/2 left-2 -translate-y-1/2 w-8 h-8 xl:w-[38px] xl:h-[38px] register-icon animate-icon rounded-full"
                        alt=""
                    />
                    <span class="transition-all register-text">ĐĂNG KÝ</span>
                </button>
            @endif
        </div>
    </div>

    <div
        class="testimonia pt-16 pb-10 md:pt-20 xl:pt-32 -mt-20 md:pb-20 z-[1] relative overflow-hidden"
    >
        <!-- Background decorative elements -->
        <div class="absolute inset-0">
            <img
                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/testimonia-bg-layer-1.png') }}"
                alt=""
                class="w-full h-full object-cover"
            />
        </div>
        <div
            class="absolute inset-0 w-full h-full bg-gradient-to-l from-[#ffe5e5]/50 via-[#fff4f4] to-[#ffe5e5]/50"
        ></div>

        <div class="container mx-auto xl:max-w-6xl px-4 xl:px-0 relative">
            <!-- Header Section -->
            <div class="text-center mb-8 md:mb-12">
                <h2
                    class="text-2xl md:text-4xl xl:text-[45px] xl:leading-[1.16] leading-[1.16] tracking-[-0.45px] font-extrabold text-gray-700 mb-4 capitalize"
                    data-x-aos="fade-up"
                >
                    đã đồng hành cùng <br/>
                    <span class="gradient-text">Helen Hải</span>
                </h2>

                <!-- Statistics -->
                <div
                    class="flex flex-wrap sm:flex-nowrap justify-center items-center gap-4 md:gap-8 lg:gap-16 mb-4 md:mt-8"
                >
                    <div
                        class="w-full sm:w-1/2 md:w-[268px] flex items-center bg-white rounded-full p-1.5 shadow-lg"
                        data-x-aos="fade-right"
                    >
                        <div
                            class="w-14 h-14 xl:w-[66px] xl:h-[66px] bg-red-500 grow-0 shrink-0 rounded-full flex items-center justify-center"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/community.svg') }}"
                                width="46.759px"
                                height="46.759px"
                                alt=""
                                srcset=""
                                class="w-10 h-10 xl:w-[47px] xl:h-[47px]"
                            />
                        </div>
                        <div class="text-center flex-auto">
                            <div class="text-xl md:text-3xl xl:text-4xl font-bold text-red-500">
                                1000+
                            </div>
                            <div class="text-base md:text-lg text-[#674230] font-bold">
                                Học viên
                            </div>
                        </div>
                    </div>

                    <div
                        class="w-full sm:w-1/2 md:w-[268px] flex items-center bg-white rounded-full p-1.5 shadow-lg"
                        data-x-aos="fade-right"
                        data-aos-delay="200"
                    >
                        <div
                            class="w-14 h-14 xl:w-[66px] xl:h-[66px] bg-red-500 grow-0 shrink-0 rounded-full flex items-center justify-center"
                        >
                            <img
                                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/presentation.svg') }}"
                                width="39px"
                                height="39px"
                                alt=""
                                srcset=""
                                class="w-8 h-8 xl:w-[39px] xl:h-[39px]"
                            />
                        </div>
                        <div class="text-center flex-auto">
                            <div class="text-xl md:text-3xl xl:text-4xl font-bold text-red-500">
                                400+
                            </div>
                            <div class="text-base md:text-lg text-[#674230] font-bold">
                                Lớp học tổ chức
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div
                class="bg-white p-4 md:!p-8 md:!pt-7 rounded md:rounded-[9.5px] shadow-[0px_3.8px_28.5px_0px_rgba(186,107,50,0.11)] border border-solid border-white"
            >
                <!-- Main Content Grid -->
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 items-start">
                    <img
                        data-x-aos="zoom-in"
                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/testimonia-1.png') }}"
                        alt=""
                        width="257"
                        height="307"
                    />
                    <img
                        data-x-aos="zoom-in"
                        data-aos-delay="100"
                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/testimonia-2.png') }}"
                        alt=""
                        width="257"
                        height="307"
                    />
                    <img
                        data-x-aos="zoom-in"
                        data-aos-delay="200"
                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/testimonia-3.png') }}"
                        alt=""
                        width="257"
                        height="307"
                    />
                    <img
                        data-x-aos="zoom-in"
                        data-aos-delay="300"
                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/testimonia-4.png') }}"
                        alt=""
                        width="257"
                        height="307"
                    />
                    <img
                        data-x-aos="zoom-in"
                        data-aos-delay="400"
                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/testimonia-5.png') }}"
                        alt=""
                        width="257"
                        height="307"
                    />
                    <img
                        data-x-aos="zoom-in"
                        data-aos-delay="500"
                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/testimonia-6.png') }}"
                        alt=""
                        width="257"
                        height="307"
                    />
                    <img
                        data-x-aos="zoom-in"
                        data-aos-delay="600"
                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/testimonia-7.png') }}"
                        alt=""
                        width="257"
                        height="307"
                    />
                    <img
                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/testimonia-8.png') }}"
                        data-x-aos="zoom-in"
                        data-aos-delay="700"
                        alt=""
                        width="257"
                        height="307"
                    />
                </div>

                <!-- Bottom CTA -->
                <div class="text-center mt-8">
                    <button
                        class="testimonia-button w-full h-8 min-h-fit md:w-[401px] md:h-[35px] text-base md:text-xl group flex flex-row items-center justify-center mx-auto bg-[#f6f4e9] rounded-full shadow-[inset_0px_0px_10px_0px_rgba(218,197,96,1.00)] outline outline-2 outline-white rounded-full font-bold text-[#674230]"
                        data-x-aos="zoom-in"
                    >
							<span class="testimonia-text mr-1">
								Xem thêm phản hồi từ học viên
							</span>
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/chevrons-down.svg') }}"
                            alt=""
                            width="20"
                            height="20"
                            class="testimonia-icon w-5 h-5"
                        />
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="faq" class="faq bg-white py-10 md:py-20">
        <div class="container xl:max-w-6xl px-4 xl:px-0 mx-auto">
            <div class="flex gap-8 lg:gap-12 flex-col lg:flex-row items-start">
                <!-- Left Column - FAQ Khóa Học -->
                <div class="lg:w-[308px] w-full shrink-0">
                    <div>
                        <!-- FAQ Header -->
                        <div class="text-center">
                            <div
                                style=" background-image: url({{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/hero-buble-background.svg') }})"
                                class="mx-auto lg:mx-0 w-64 xl:w-[280px] xl:h-[70px] text-base md:text-xl xl:text-[26.32px] pt-2 pb-4 xl:pt-2.5 xl:pb-2.5 flex justify-center items-start text-white tracking-[-0.263px] text-white font-extrabold bg-no-repeat bg-contain bg-center">
                                FAQ Khóa Học
                            </div>

                            <h2
                                class="text-xl md:text-4xl xl:text-[45px] font-extrabold leading-[1.15] text-gray-700 tracking-[-0.45px] mb-4"
                            >
                                Hỏi Đáp
                            </h2>
                            <p
                                class="text-[#444] text-sm md:text-base font-normal text-justify"
                            >
                                Bạn đang muốn theo học nghề spa nhưng còn nhiều băn khoăn? Dưới
                                đây là những thắc mắc thường gặp mà chúng tôi đã tổng hợp để
                                giúp bạn hiểu rõ hơn.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Right Column - FAQ Items -->
                <div class="space-y-4 w-full text-sm md:text-base">
                    <!-- FAQ Item 1 -->
                    <div
                        data-x-aos="fade-up"
                        class="faq-item bg-white rounded-xl shadow-lg overflow-hidden"
                    >
                        <button
                            class="faq-question w-full px-6 py-4 font-bold text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                            onclick="toggleFAQ(1)"
                        >
                            <span>1. Tôi chưa biết gì, có học được không?</span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="25"
                                height="25"
                                viewBox="0 0 25 25"
                                fill="none"
                                class="faq-icon"
                            >
                                <path
                                    d="M6.21581 9.375H18.7793C19.6484 9.375 20.083 10.4248 19.4678 11.04L13.1885 17.3242C12.8076 17.7051 12.1875 17.7051 11.8066 17.3242L5.52733 11.04C4.9121 10.4248 5.34667 9.375 6.21581 9.375Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </button>
                        <div class="faq-answer px-6 pb-4 hidden">
                            <p class="text-gray-600 leading-relaxed">
                                Hoàn toàn được! Khóa học spa được thiết kế từ cơ bản đến nâng
                                cao. Với lộ trình rõ ràng và giảng viên hướng dẫn tận tình, bạn
                                sẽ nắm vững kiến thức và thực hành thuần thục kể cả khi chưa có
                                kinh nghiệm.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div
                        data-x-aos="fade-up"
                        data-aos-delay="200"
                        class="faq-item bg-white rounded-xl shadow-lg overflow-hidden"
                    >
                        <button
                            class="faq-question w-full px-6 py-4 font-bold text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                            onclick="toggleFAQ(2)"
                        >
                            <span>2. Một khóa học kéo dài bao lâu?</span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="25"
                                height="25"
                                viewBox="0 0 25 25"
                                fill="none"
                                class="faq-icon"
                            >
                                <path
                                    d="M6.21581 9.375H18.7793C19.6484 9.375 20.083 10.4248 19.4678 11.04L13.1885 17.3242C12.8076 17.7051 12.1875 17.7051 11.8066 17.3242L5.52733 11.04C4.9121 10.4248 5.34667 9.375 6.21581 9.375Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </button>
                        <div class="faq-answer px-6 pb-4 hidden">
                            <p class="text-gray-600 leading-relaxed">
                                Thời gian học tùy thuộc vào khóa học bạn chọn. Khóa cơ bản
                                thường kéo dài 2-3 tháng, khóa nâng cao có thể từ 4-6 tháng.
                                Chúng tôi có lịch học linh hoạt phù hợp với thời gian của bạn.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div
                        data-x-aos="fade-up"
                        data-aos-delay="400"
                        class="faq-item bg-white rounded-xl shadow-lg overflow-hidden"
                    >
                        <button
                            class="faq-question w-full px-6 py-4 font-bold text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                            onclick="toggleFAQ(3)"
                        >
                            <span>3. Khóa học diễn ra trên nền tảng nào?</span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="25"
                                height="25"
                                viewBox="0 0 25 25"
                                fill="none"
                                class="faq-icon"
                            >
                                <path
                                    d="M6.21581 9.375H18.7793C19.6484 9.375 20.083 10.4248 19.4678 11.04L13.1885 17.3242C12.8076 17.7051 12.1875 17.7051 11.8066 17.3242L5.52733 11.04C4.9121 10.4248 5.34667 9.375 6.21581 9.375Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </button>
                        <div class="faq-answer px-6 pb-4 hidden">
                            <p class="text-gray-600 leading-relaxed">
                                Khóa học được tổ chức trực tiếp tại trung tâm với đầy đủ trang
                                thiết bị chuyên nghiệp. Chúng tôi cũng có hỗ trợ học online cho
                                những buổi lý thuyết để bạn có thể học mọi lúc mọi nơi.
                            </p>
                        </div>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div
                        data-x-aos="fade-up"
                        data-aos-delay="600"
                        class="faq-item bg-white rounded-xl shadow-lg overflow-hidden"
                    >
                        <button
                            class="faq-question w-full px-6 py-4 font-bold text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                            onclick="toggleFAQ(4)"
                        >
                            <span>4. Học xong có được cấp chứng chỉ không?</span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="25"
                                height="25"
                                viewBox="0 0 25 25"
                                fill="none"
                                class="faq-icon"
                            >
                                <path
                                    d="M6.21581 9.375H18.7793C19.6484 9.375 20.083 10.4248 19.4678 11.04L13.1885 17.3242C12.8076 17.7051 12.1875 17.7051 11.8066 17.3242L5.52733 11.04C4.9121 10.4248 5.34667 9.375 6.21581 9.375Z"
                                    fill="currentColor"
                                />
                            </svg>
                        </button>
                        <div class="faq-answer px-6 pb-4 hidden">
                            <p class="text-gray-600 leading-relaxed">
                                Có! Sau khi hoàn thành khóa học và vượt qua bài kiểm tra cuối
                                khóa, bạn sẽ nhận được chứng chỉ hoàn thành khóa học do trung
                                tâm cấp. Chứng chỉ này sẽ giúp bạn tự tin hơn khi xin việc hoặc
                                mở spa riêng.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="contact" class="contact-support py-10 md:py-12 xl:py-16 relative">
        <!-- Background Pattern -->
        <div class="absolute inset-0">
            <img
                class="h-full w-full object-cover"
                src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/contact-support-bg.png') }}"
                alt=""
                srcset=""
            />
        </div>

        <div class="container xl:max-w-6xl mx-auto px-4 xl:px-0 relative z-10">
            <div class="text-center text-white">
                <!-- Main Heading -->
                <h2
                    class="text-sm md:text-xl xl:text-[26px] font-bold mb-1.5"
                    data-x-aos="fade-up"
                >
                    Bạn chưa biết liệu khóa học có
                </h2>
                <h2
                    data-x-aos="fade-up"
                    data-aos-delay="200"
                    class="text-xl md:text-4xl xl:text-[45px] font-extrabold tracking-[-0.45px] mb-4 md:mb-5"
                >
                    Phù Hợp Với Mình Không?
                </h2>

                <!-- Information Box -->
                <p
                    class="text-sm md:text-base font-normal mb-5 md:mb-10"
                    data-x-aos="fade-up"
                    data-aos-delay="300"
                >
                    Đừng lo lắng nếu bạn còn đang phân vân!
                    <br/>
                    Chúng tôi sẽ giúp bạn lựa chọn lộ trình học phù hợp nhất với mong muốn và
                    mục tiêu của bạn.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">


                    <!-- Hotline Button -->
                    <a
                        href="tel:0393591531"
                        data-x-aos="fade-right"
                        data-aos-delay="200"
                        class="bg-white relative text-red-500 rounded-full shadow-[0px_5px_5px_0px_rgba(0,0,0,0.25)] text-base md:text-xl xl:text-2xl font-bold pl-14 pr-5 py-2.5 flex items-center"
                    >
							<span
                                class="absolute md:top-1.5 left-1.5 w-8 h-8 md:w-[38px] md:h-[38px] inline-flex items-center justify-center bg-red-500 rounded-full shadow-[0px_6px_6px_0px_rgba(0,0,0,0.25)]"
                            >
								<img
                                    src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/telephone.svg') }}"
                                    alt=""
                                    width="24"
                                    height="24"
                                    class="w-4 h-4 md:w-6 md:h-6"
                                />
							</span>
                        LIÊN HỆ HOTLINE
                    </a>
                </div>
            </div>
        </div>
        <!-- Arrow pointing down -->
        <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
            <div class="w-4 h-4 bg-red-500 rotate-45"></div>
        </div>
    </div>

    <footer class="footer bg-gradient-to-b from-[#FFF0F0] to-[#FFE8E8] py-10 md:py-13">
        <div class="container xl:max-w-6xl mx-auto px-4 xl:px-0">
            <div class="flex flex-col lg:flex-row gap-8 items-center">
                <!-- Left Column - Company Info -->
                <div class="w-full lg:w-1/2">
                    <!-- Logo -->
                    <div class="mb-6">
                        <img
                            src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/logo.png') }}"
                            alt="Logo"
                            width="157px"
                            height="39px"
                            class="w-36 h-auto xl:w-40 xl:h-10"
                        />
                    </div>

                    <!-- Company Description -->
                    <div class="mb-4 md:mb-6 text-sm md:text-base text-gray-700">
                        <p class="font-normal text-justify">
                            Nền tảng học tập toàn diện cho ngành làm đẹp
                        </p>
                        <p class="font-bold">CÔNG TY CỔ PHẦN GIÁO DỤC BEAUTY EDU</p>
                    </div>

                    <!-- Contact Information -->
                    <div class="flex flex-col md:flex-row flex-wrap gap-y-3.5 gap-x-7">
                        <!-- Phone -->
                        <a
                            href="#"
                            class="flex text-sm md:text-base items-center gap-3 group w-fit min-w-45"
                        >
								<span
                                    class="w-7 h-7 bg-red-500 rounded-full flex items-center justify-center shrink-0 grow-0 shadow-[0px_6px_6px_0px_rgba(0,0,0,0.25)]"
                                >
									<img
                                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/telephone.svg') }}"
                                        alt="Phone"
                                        class="w-4 h-4"
                                    />
								</span>
                            <span class="text-gray-700 font-bold group-hover:text-red-500"
                            >0393 591 531</span
                            >
                        </a>

                        <!-- Address -->
                        <a
                            href="#"
                            class="flex text-sm md:text-base items-center gap-3 group w-fit min-w-45"
                        >
								<span
                                    class="w-7 h-7 bg-red-500 rounded-full flex items-center justify-center shrink-0 grow-0 shadow-[0px_6px_6px_0px_rgba(0,0,0,0.25)]"
                                >
									<img
                                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/map-marker.svg') }}"
                                        alt="Location"
                                        class="w-4 h-4"
                                    />
								</span>
                            <span class="text-gray-700 font-bold group-hover:text-red-500"
                            >R4 Royal City, Thanh Xuân, Hà Nội</span
                            >
                        </a>

                        <!-- Email -->
                        <a
                            href="#"
                            class="flex text-sm md:text-base items-center gap-3 group w-fit min-w-45"
                        >
								<span
                                    class="w-7 h-7 bg-red-500 rounded-full flex items-center justify-center shrink-0 grow-0 shadow-[0px_6px_6px_0px_rgba(0,0,0,0.25)]"
                                >
									<img
                                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/envelope.svg') }}"
                                        alt="Email"
                                        class="w-4 h-4"
                                    />
								</span>
                            <span class="text-gray-700 font-bold group-hover:text-red-500"
                            ><EMAIL></span
                            >
                        </a>

                        <!-- Website -->
                        <a
                            href="#"
                            class="flex text-sm md:text-base items-center gap-3 group w-fit min-w-45"
                        >
								<span
                                    class="w-7 h-7 bg-red-500 rounded-full flex items-center justify-center shrink-0 grow-0 shadow-[0px_6px_6px_0px_rgba(0,0,0,0.25)]"
                                >
									<img
                                        src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/icons/globe.svg') }}"
                                        alt="Website"
                                        class="w-4 h-4"
                                    />
								</span>
                            <span class="text-gray-700 font-bold group-hover:text-red-500"
                            >beautyedu.vn</span
                            >
                        </a>
                    </div>
                </div>

                <!-- Right Column - Map -->
                <div class="w-full lg:w-1/2 md:h-[289px]">
                    <!-- Google Map iframe -->
                    <iframe
                        loading="lazy"
                        height="421"
                        data-src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4621.972621778329!2d105.8136582759981!3d21.003151288656184!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ac9b394920f7%3A0xe80f93855fba87b2!2zUjQsIEtodSDEkcO0IHRo4buLIFJveWFsIENpdHksIFRoxrDhu6NuZyDEkMOsbmgsIFRoYW5oIFh1w6JuLCBIw6AgTuG7mWksIFZpZXRuYW0!5e1!3m2!1sen!2s!4v1747063519097!5m2!1sen!2s"
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4621.972621778329!2d105.8136582759981!3d21.003151288656184!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ac9b394920f7%3A0xe80f93855fba87b2!2zUjQsIEtodSDEkcO0IHRo4buLIFJveWFsIENpdHksIFRoxrDhu6NuZyDEkMOsbmgsIFRoYW5oIFh1w6JuLCBIw6AgTuG7mWksIFZpZXRuYW0!5e1!3m2!1sen!2s!4v1747063519097!5m2!1sen!2s"
                        class="w-full h-[421px] md:h-full border-0 rounded-lg shadow-md"
                        allowfullscreen=""
                        referrerpolicy="no-referrer-when-downgrade"
                    ></iframe>
                </div>
            </div>
        </div>
    </footer>

    @include('partials.modals.register_login')
    @endsection
    @push('js')
        <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
        <script src="{{ asset('assets/frontend/default/libs/aos/aos-*******-beta.6.js') }}"></script>


        <script>
            AOS.init({
                once: true,
                duration: 650,
            });

            $(window).on("load", function () {
                $("[data-src]").each(function (ifram) {
                    $(this).attr("src", $(this).data("src"));
                });

                $("[data-x-aos]").each(function name(i, element) {
                    $(element).attr("data-aos", $(element).data("x-aos"));
                });

                AOS.refreshHard();
            });

            // FAQ Toggle Function
            function toggleFAQ(index) {
                const faqItems = document.querySelectorAll(".faq-item");
                const currentItem = faqItems[index - 1];
                const answer = currentItem.querySelector(".faq-answer");
                const icon = currentItem.querySelector(".faq-icon");

                // Close all other FAQ items
                faqItems.forEach((item, i) => {
                    if (i !== index - 1) {
                        item.classList.remove("active"); // Add background colo
                        const otherAnswer = item.querySelector(".faq-answer");
                        const otherIcon = item.querySelector(".faq-icon");
                        otherAnswer.classList.add("hidden");
                        otherIcon.classList.remove("rotate-180");
                    }
                });

                // Toggle current FAQ item
                if (answer.classList.contains("hidden")) {
                    currentItem.classList.add("active"); // Add background colo
                    answer.classList.remove("hidden");
                    icon.classList.add("rotate-180");
                } else {
                    answer.classList.add("hidden");
                    icon.classList.remove("rotate-180");
                    currentItem.classList.remove("active"); // Add background colo
                }
            }
        </script>

        <!-- Custom JavaScript -->
        <script src="{{ asset('assets/frontend/helen-spa/assets/home/<USER>/main.js') }}"></script>
    @endpush

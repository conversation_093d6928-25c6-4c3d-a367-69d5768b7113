@push('css')
    <link rel="preconnect" href="https://www.facebook.com" />
    <link rel="preconnect" href="https://www.tiktok.com" />
    <link rel="preconnect" href="https://www.google.com" />
    <!-- Font Awesome -->
    <link
        rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Google Fonts -->
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet"
    />
    <link rel="preload" href="{{ asset('assets/frontend/mst-academy/assets/libs/aos/aos-*******-beta.6.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'"/>
    <noscript><link rel="stylesheet" href="{{ asset('assets/frontend/mst-academy/assets/libs/aos/aos-*******-beta.6.css') }}"/></noscript>
    <link rel="preload" href="{{ asset('assets/frontend/mst-academy/assets/css/style.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet'"/>
    <noscript><link rel="stylesheet" href="{{ asset('assets/frontend/mst-academy/assets/css/style.css') }}"/></noscript>
    <style>
        html {
            font-size: 16px !important;
        }
        .italic{
            padding: 0 !important;
        }

        @keyframes pulse-shadow {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        .animate-pulse-shadow {
            animation: pulse-shadow 1s infinite;
        }
    </style>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        yellow: {
                            50: "#fffbeb",
                            100: "#fef3c7",
                            200: "#fde68a",
                            300: "#fcd34d",
                            400: "#FFBC01",
                            500: "#f59e0b",
                            600: "#d97706",
                            700: "#b45309",
                            800: "#92400e",
                            900: "#78350f",
                            950: "#451a03",
                        },
                        blue: {
                            900: "#051F4D",
                        },
                    },
                },
            },
        };
    </script>
@endpush
<!-- Tailwind CSS -->

<div class="mst-wrapper w-screen overflow-x-hidden">
    <div class="fixed bottom-10 left-1/2 -translate-x-1/2 z-[999]">
        <a
            href="#regis-section"
            class="relative text-white font-bold px-16 rounded-full text-xl md:text-2xl transition duration-300 transform hover:scale-105 btn-register-now block animate-pulse-shadow"
        >
            <img
                src="{{ asset('assets/frontend/mst-academy/assets/images/btn-regis-now.png') }}"
                alt="ĐĂNG KÝ NGAY"
                class="w-full h-[70px] absolute top-0 left-0 !max-h-[inherit]"
            />
            <span class="relative z-10 !translate-y-[10px] whitespace-nowrap">ĐĂNG KÝ NGAY</span>
        </a>
    </div>
    <div
        class="bg-gradient-to-r from-[#002084] via-[#0e34ac] to-[#002084] text-white min-h-screen"
    >
        <!-- Header with Logo -->
        <header class="py-6 px-4 md:px-8 lg:px-16">
            <div class="w-full flex justify-center">
                <img
                    src="{{ asset('assets/frontend/mst-academy/assets/images/logo.png') }}"
                    alt="KIMEDIA"
                    class="w-[165px] h-auto object-cover"
                />
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-grow py-8 !px-3 md:px-8 lg:px-16">
            <div class="max-w-7xl mx-auto">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <!-- Left Column - Text Content -->
                    <div class="order-2 lg:order-1 order-lg-1">
                        <h2 class="!text-center lg:!text-left text-xl md:text-2xl font-bold mb-2">
                            Bộ Khóa Học Online 2025
                            <span class="text-yellow-400 ml-2">★★★★★</span>
                        </h2>

                        <h1 class="!text-center lg:!text-left text-2xl md:text-4xl lg:text-4xl font-bold mb-4">
                            Tạo ra khoá học online<br/>
                            <div class="">
                                <span class="text-yellow-400">x100</span>
                                <span class="ml-2">học viên trong</span>
                                <span class="text-yellow-400 ml-2">30 ngày</span>
                            </div>
                        </h1>

                        <div
                            class="my-8 !p-3 lg:!p-4 bg-[#0e34ac] border-l-[6px] border-[#ffba55] text-sm lg:text-lg"
                        >
                            <p class="mb-1 lg:mb-2 text-base lg:text-lg">
                                Các công thức
                                <span class="font-bold text-white">HIỆU QUẢ NHẤT!</span> mà đã
                                giúp mình tạo ra
                            </p>
                            <p class="mb-1 lg:mb-2">
                    <span class="text-yellow-400 font-bold"
                    >136.268 khách hàng</span
                    >
                                cho sản phẩm
                                <span class="text-yellow-400 whitespace-nowrap">giáo dục</span> ngay
                            </p>
                            <p>
                                cả khi thương hiệu của mình
                                <span class="text-yellow-400 font-bold whitespace-nowrap"
                                >chưa có ai biết tới!</span
                                >
                            </p>
                        </div>

                        <div class="mt-8 relative">
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/circle-grid.png') }}"
                                class="absolute -top-3 -left-3"
                            />
                            <a href="#regis-section"
                               class="relative text-white font-bold px-16 rounded-full text-xl md:text-2xl transition duration-300 transform hover:scale-105 btn-register-now"
                            >
                                <img
                                    src="{{ asset('assets/frontend/mst-academy/assets/images/btn-regis-now.png') }}"
                                    alt="ĐĂNG KÝ NGAY"
                                    class="w-full h-[70px] absolute top-0 left-0 !max-h-[inherit]"
                                />
                                <span class="relative z-10 !translate-y-[10px]">ĐĂNG KÝ NGAY</span>
                            </a>
                        </div>
                    </div>

                    <!-- Right Column - Laptop Image -->
                    <div class="order-1 lg:order-2 order-lg-2">
                        <div class="laptop-container">
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/user-banner.png') }}"
                                alt="Laptop"
                                class="w-full h-auto object-cover"
                            />
                        </div>
                    </div>
                </div>

                <!-- Bottom Section -->
                <div class="mt-16 text-center">
                    <div class="flex justify-center items-center mb-1">
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/m-perday.png') }}"
                             class="max-w-[280px] lg:max-w-[390px]"/>
                    </div>

                    <div class="flex justify-center items-center lg:gap-5 gap-2 mt-4 lg:mt-0">
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/ani-arrow-right.png') }}"
                            class="w-10 h-auto dots-animation-right"
                        />
                        <h2 class="text-2xl md:text-3xl lg:text-4xl font-bold">
                            Tăng trưởng học viên của bạn sau
                            <span class="text-yellow-400">14 ngày</span>
                        </h2>
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/ani-arrow-left.png') }}"
                            class="w-10 h-auto dots-animation-left"
                        />
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Second Section -->
    <div class="bg-white">
        <div class="container mx-auto !px-3 py-10 md:px-8">
            <!-- Heading -->
            <h2
                class="text-xl md:text-4xl font-bold text-center text-gray-800 mb-8"
                data-aos-lazy="fade-up"
            >
                Tại sao các chuyên gia/nhà đào tạo/<br class="md:hidden"/> kinh doanh<br class="hidden md:block"/>
                nên làm khóa học online/Elearning?
            </h2>

            <!-- Description Paragraph -->
            <div
                class="max-w-4xl mx-auto text-center text-gray-700 mb-12 text-base lg:text-xl"
                data-aos-lazy="fade-up"
            >
                <div class="w-full max-w-[930px] text-center justify-start">
                <span class="text-black font-normal"
                >Trong thời đại số khi mà các </span
                ><span
                        class="text-black font-normal underline"
                    >
                chi phí cố định như phòng đào tạo, mặt bằng hay việc tìm kiếm
                khách hàng trở nên đắt đỏ hơn</span
                    ><span class="text-black font-normal">
                thì các khóa học Online chính là phương pháp tốt nhất để giúp cho
                chúng ta phát triển. Tại sao như vậy?</span
                    >
                </div>
            </div>

            <!-- Benefits Box -->
            <div class="max-w-4xl mx-auto" data-aos-lazy="fade-up">
                <div class="space-y-4">
                    <!-- Benefit 1 -->
                    <div class="flex items-start gap-2">
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/blue-arr.svg') }}"/>
                        <div>
                    <span class="font-bold text-lg text-gray-800">
                    Mở rộng quy mô đào tạo:
                    </span>
                            <span class="text-gray-700">
                    Tiếp cận hàng nghìn học viên ở bất kỳ đâu, bất kỳ lúc nào.
                    </span>
                        </div>
                    </div>

                    <!-- Benefit 2 -->
                    <div class="flex items-start gap-2">
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/blue-arr.svg') }}"/>
                        <div>
                    <span class="font-bold text-lg text-gray-800">
                    Tiết kiệm thời gian và chi phí:
                    </span>
                            <span class="text-gray-700">
                    Giảm thiểu chi phí tổ chức, di chuyển và thời gian lặp lại bài
                    giảng.
                    </span>
                        </div>
                    </div>

                    <!-- Benefit 3 -->
                    <div class="flex items-start gap-2">
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/blue-arr.svg') }}"/>
                        <div>
                    <span class="font-bold text-lg text-gray-800">
                    Đối với người kinh doanh:
                    </span>
                            <span class="text-gray-700">
                    thì khóa học online chính là sản phẩm phễu tuyệt vời vì khách
                    hàng sẽ được đào tạo qua lớp kiến thức của bạn và họ trở nên
                    dễ dàng mua các sản phẩm dịch vụ thứ 2,3,4...
                    </span>
                        </div>
                    </div>

                    <!-- Benefit 4 -->
                    <div class="flex items-start gap-2">
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/blue-arr.svg') }}"/>
                        <div>
                    <span class="font-bold text-lg text-gray-800">
                    Không cần phải người nổi tiếng:
                    </span>
                            <span class="text-gray-700">
                    cũng có thể tạo ra khóa học và bán vì học viên họ chỉ quan tâm
                    đến kiến thức của bạn.
                    </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mx-auto py-8 max-w-6xl">
                <!-- Main Heading -->
                <h1
                    class="text-2xl md:text-3xl lg:text-4xl font-bold text-center mb-12"
                    data-aos-lazy="fade-up"
                >
                    Và bộ khoá học này sẽ giúp bạn Tạo và Bán khóa học online:
                </h1>

                <!-- Top Benefits Section -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                    <!-- Benefit 1 -->
                    <div
                        class="bg-[#0e34ac] rounded-[3px] border-r-[3px] border-b-[3px] border-[#2a53d1] text-white p-6 rounded-lg flex items-center"
                        data-aos-lazy="zoom-in"
                    >
                        <div class="bg-white rounded-full p-2 mr-4 flex-shrink-0">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="35"
                                height="35"
                                viewBox="0 0 35 35"
                                fill="none"
                            >
                                <g clip-path="url(#clip0_20_15)">
                                    <path
                                        d="M3.18182 0.5C1.43286 0.5 0 1.89192 0 3.59091V23.6818C0 25.3808 1.43286 26.7727 3.18182 26.7727H5.01975C4.91251 26.1364 4.79698 25.439 4.7805 24.8303C4.7805 22.0134 6.44402 19.4872 8.97372 18.2667C8.3432 17.5311 7.96231 16.5869 7.96231 15.5606C7.96231 13.2263 9.93015 11.3182 12.3342 11.3182C14.7383 11.3182 16.7045 13.2263 16.7045 15.5606C16.7045 16.5872 16.3237 17.531 15.6931 18.2667C18.2237 19.4868 19.8864 22.0129 19.8864 24.8303C19.8477 25.5604 19.7849 26.0756 19.6098 26.7727H31.8182C33.5671 26.7727 35 25.3808 35 23.6818V3.59091C35 1.89192 33.5671 0.5 31.8182 0.5H3.18182ZM3.18182 2.04545H31.8182C32.7133 2.04545 33.4091 2.72137 33.4091 3.59091V6.68182H1.59091V3.59091C1.59091 2.72137 2.2867 2.04545 3.18182 2.04545ZM23.455 11.4148L30.6141 15.2784C31.1612 15.5725 31.1612 16.3365 30.6141 16.6307L23.455 20.4943C22.9245 20.7811 22.2721 20.408 22.2727 19.8182V12.0909C22.253 11.4473 22.9329 11.1347 23.455 11.4148ZM12.3342 12.8636C10.7891 12.8636 9.55322 14.0624 9.55322 15.5606C9.55322 17.0589 10.7891 18.2576 12.3342 18.2576C13.8793 18.2576 15.1136 17.0589 15.1136 15.5606C15.1136 14.0624 13.8793 12.8636 12.3342 12.8636ZM10.4031 19.3579C8.00023 20.1543 6.3714 22.343 6.3714 24.8303C6.38921 25.4536 6.52923 26.1257 6.62931 26.7727H17.9956C18.1803 26.0391 18.2591 25.472 18.2955 24.8303C18.2955 22.3436 16.6677 20.1562 14.2654 19.3594C13.6816 19.6411 13.0273 19.8031 12.3342 19.8031C11.6408 19.8031 10.9871 19.6398 10.4031 19.3579Z"
                                        fill="#002084"
                                    />
                                    <path
                                        d="M7.95455 28.3182C6.48144 28.3182 5.23499 29.3088 4.87837 30.6364H0V32.1819H4.87837C5.23499 33.5094 6.48144 34.5001 7.95455 34.5001C9.4281 34.5001 10.6776 33.5101 11.0338 32.1819H35V30.6364H11.0338C10.6776 29.3082 9.4281 28.3182 7.95455 28.3182Z"
                                        fill="#002084"
                                    />
                                    <path
                                        d="M30.2273 3.59094V5.1364H31.8182V3.59094H30.2273Z"
                                        fill="#002084"
                                    />
                                    <path
                                        d="M27.0455 3.59094V5.1364H28.6364V3.59094H27.0455Z"
                                        fill="#002084"
                                    />
                                    <path
                                        d="M23.8636 3.59094V5.1364H25.4546V3.59094H23.8636Z"
                                        fill="#002084"
                                    />
                                    <path
                                        d="M20.6818 3.59094V5.1364H22.2727V3.59094H20.6818Z"
                                        fill="#002084"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_20_15">
                                        <rect
                                            width="35"
                                            height="34"
                                            fill="white"
                                            transform="translate(0 0.5)"
                                        />
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium">
                                Xây dựng bài giảng online nhanh chóng/chất lượng
                            </p>
                        </div>
                    </div>

                    <!-- Benefit 2 -->
                    <div
                        class="bg-[#0e34ac] rounded-[3px] border-r-[3px] border-b-[3px] border-[#2a53d1] text-white p-6 rounded-lg flex items-center"
                        data-aos-lazy="zoom-in"
                    >
                        <div class="bg-white rounded-full p-2 mr-4 flex-shrink-0">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="37"
                                height="37"
                                viewBox="0 0 37 37"
                                fill="none"
                            >
                                <path
                                    d="M9.96688 33.9632H34.5987C34.9081 33.9632 35.1612 33.7156 35.1612 33.4007C35.1612 33.0913 34.9081 32.8382 34.5987 32.8382H33.3556V15.9463H34.8125C35.0319 15.9463 35.2344 15.8169 35.3244 15.62C35.4144 15.4175 35.3806 15.1813 35.2344 15.0182L30.3519 9.42127C30.1381 9.1794 29.7163 9.1794 29.5025 9.42127L24.62 15.0182C24.4737 15.1813 24.44 15.4175 24.53 15.62C24.62 15.8169 24.8225 15.9463 25.0419 15.9463H26.4987V32.8382H25.6438V19.3044C25.6438 18.995 25.3906 18.7419 25.0813 18.7419H19.3438C19.0344 18.7419 18.7812 18.995 18.7812 19.3044V32.8382H18.005V23.0619C18.005 22.7525 17.7519 22.4994 17.4425 22.4994H11.705C11.39 22.4994 11.1425 22.7525 11.1425 23.0619V32.8382H10.5294V26.9825C10.5294 26.6675 10.2763 26.42 9.96688 26.42H4.22938C3.91438 26.42 3.66688 26.6675 3.66688 26.9825V32.8382H2.1875C1.87813 32.8382 1.625 33.0913 1.625 33.4007C1.625 33.7156 1.87813 33.9632 2.1875 33.9632H9.96688Z"
                                    fill="#002084"
                                />
                                <path
                                    d="M10.04 19.8669C14.6806 19.8669 18.455 16.0925 18.455 11.4518C18.455 6.81122 14.6806 3.03687 10.04 3.03687C5.39939 3.03687 1.625 6.81122 1.625 11.4518C1.625 16.0925 5.39939 19.8669 10.04 19.8669ZM10.4787 12.0143H9.60125C8.40875 12.0143 7.44127 11.0469 7.44127 9.85436C7.44127 8.70121 8.34125 7.75621 9.47751 7.69435V6.79999C9.47751 6.49059 9.73061 6.23749 10.04 6.23749C10.355 6.23749 10.6025 6.49059 10.6025 6.79999V7.69435C11.7387 7.75621 12.6444 8.70121 12.6444 9.85436C12.6444 10.1637 12.3913 10.4169 12.0819 10.4169C11.7669 10.4169 11.5194 10.1637 11.5194 9.85436C11.5194 9.2806 11.0525 8.81372 10.4787 8.81372H9.60125C9.03312 8.81372 8.56627 9.2806 8.56627 9.85436C8.56627 10.4225 9.03312 10.8893 9.60125 10.8893H10.4787C11.6712 10.8893 12.6444 11.8625 12.6444 13.0494C12.6444 14.2025 11.7387 15.1475 10.6025 15.2093V16.1037C10.6025 16.4187 10.355 16.6662 10.04 16.6662C9.73061 16.6662 9.47751 16.4187 9.47751 16.1037V15.2093C8.34125 15.1475 7.44127 14.2025 7.44127 13.0494C7.44127 12.74 7.69436 12.4869 8.00377 12.4869C8.31313 12.4869 8.56627 12.74 8.56627 13.0494C8.56627 13.6231 9.03312 14.09 9.60125 14.09H10.4787C11.0525 14.09 11.5194 13.6231 11.5194 13.0494C11.5194 12.4812 11.0525 12.0143 10.4787 12.0143Z"
                                    fill="#002084"
                                />
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium">
                                Gia tăng doanh thu qua quảng cáo online
                            </p>
                        </div>
                    </div>

                    <!-- Benefit 3 -->
                    <div
                        class="bg-[#0e34ac] rounded-[3px] border-r-[3px] border-b-[3px] border-[#2a53d1] text-white p-6 rounded-lg flex items-center"
                        data-aos-lazy="zoom-in"
                    >
                        <div class="bg-white rounded-full p-2 mr-4 flex-shrink-0">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="41"
                                height="42"
                                viewBox="0 0 41 42"
                                fill="none"
                            >
                                <g clip-path="url(#clip0_20_40)">
                                    <path
                                        d="M32.8452 25.3332L26.3418 31.9952L13.3737 31.9933V29.5323H24.2056C24.2057 26.8169 22.0551 24.6126 19.4031 24.6126H9.55964L1.35974 31.7819L8.17392 42.0001L12.028 39.3764H27.1326L39.6403 25.3332C37.7639 23.4109 34.7216 23.4109 32.8452 25.3332Z"
                                        fill="#002084"
                                    />
                                    <path
                                        d="M21.8031 0C15.8419 0 10.9922 4.968 10.9922 11.0746C10.9922 17.1813 15.8419 22.1493 21.8031 22.1493C27.7644 22.1493 32.6141 17.1813 32.6141 11.0746C32.6141 4.968 27.7644 0 21.8031 0ZM27.7884 12.3051H20.5811V4.92206H22.9835V9.84411H27.7884V12.3051Z"
                                        fill="#002084"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_20_40">
                                        <rect width="41" height="42" fill="white"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium">
                                Tự do về thời gian, không cần dạy quá nhiều
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Subheading -->
                <p class="text-center text-lg mb-16" data-aos-lazy="fade-up">
                    Dù bạn chưa biết gì về tạo khóa học online cũng đều có thể làm và
                    tuyển học viên<br class="hidden md:block"/>
                    ngay sau 1 tuần học
                </p>

                <!-- Features Grid -->
                <div
                    class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-10 max-w-[940px] mx-auto"
                >
                    <!-- Feature 1 -->
                    <div class="relative pt-6" data-aos-lazy="flip-up">
                        <div
                            class="feature-header text-white py-3 px-6 rounded-lg text-center font-medium !w-[300px] md:!w-[370px]"
                        >
                            Quy trình tạo khóa học online
                        </div>
                        <div
                            class="bg-[#fff1c5] rounded-[25px] border-r-[3px] border-b-[3px] border-[#2a53d1] flex gap-2 py-3 lg:py-5 px-4 min-h-[145px]"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/qt-1.png') }}"
                                alt="Course creation"
                                class="w-[80px] lg:w-[110px] h-auto object-contain mr-2 lg:mr-4 flex-shrink-0"
                            />
                            <p class="text-gray-800">
                                Từ tay xây dựng cấu trúc khóa học chuyên nghiệp, từ nội dung,
                                quay video đến chỉnh sửa và tối ưu chi phí.
                            </p>
                        </div>
                    </div>

                    <!-- Feature 2 -->
                    <div class="relative pt-6" data-aos-lazy="flip-up">
                        <div
                            class="feature-header text-white py-3 px-6 rounded-lg text-center font-medium"
                        >
                            Tăng doanh thu vượt bậc
                        </div>
                        <div
                                class="bg-[#fff1c5] rounded-[25px] border-r-[3px] border-b-[3px] border-[#2a53d1] flex gap-2 py-3 lg:py-5 px-4 min-h-[145px]"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/qt-2.png') }}"
                                alt="Revenue increase"
                                class="w-[80px] lg:w-[110px] h-auto object-contain mr-2 lg:mr-4 flex-shrink-0"
                            />
                            <p class="text-gray-800">
                                Hiểu rõ tâm lý khách hàng và áp dụng các chiến lược bán hàng
                                hiệu quả, giúp tăng doanh số lên đến 150%.
                            </p>
                        </div>
                    </div>

                    <!-- Feature 3 -->
                    <div class="relative pt-6" data-aos-lazy="flip-up">
                        <div
                            class="feature-header text-white py-3 px-6 rounded-lg text-center font-medium"
                        >
                            Quảng cáo tuyển học viên
                        </div>
                        <div
                                class="bg-[#fff1c5] rounded-[25px] border-r-[3px] border-b-[3px] border-[#2a53d1] flex gap-2 py-3 lg:py-5 px-4 min-h-[145px]"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/qt-3.png') }}"
                                alt="Advertising"
                                class="w-[80px] lg:w-[110px] h-auto object-contain mr-2 lg:mr-4 flex-shrink-0"
                            />
                            <p class="text-gray-800">
                                Quảng cáo Facebook Ads và Tiktok Ads, tiết kiệm 40% chi phí
                                quảng cáo nhưng vẫn tăng lượng học viên
                            </p>
                        </div>
                    </div>

                    <!-- Feature 4 -->
                    <div class="relative pt-6" data-aos-lazy="flip-up">
                        <div
                            class="feature-header text-white py-3 px-6 rounded-lg text-center font-medium"
                        >
                            Bán khóa học được nhiều lần
                        </div>
                        <div
                                class="bg-[#fff1c5] rounded-[25px] border-r-[3px] border-b-[3px] border-[#2a53d1] flex gap-2 py-3 lg:py-5 px-4 min-h-[145px]"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/qt-4.png') }}"
                                alt="Repeat sales"
                                class="w-[80px] lg:w-[110px] h-auto object-contain mr-2 lg:mr-4 flex-shrink-0"
                            />
                            <p class="text-gray-800">
                                Nắm vững tư duy bán hàng dài hạn (lần 2-3-4-5), giúp tạo nguồn
                                thu ổn định và lâu dài.
                            </p>
                        </div>
                    </div>

                    <!-- Feature 5 -->
                    <div class="relative pt-6" data-aos-lazy="flip-up">
                        <div
                            class="feature-header text-white py-3 px-6 rounded-lg text-center font-medium"
                        >
                            Lên kế hoạch bán khóa học bài bản
                        </div>
                        <div
                                class="bg-[#fff1c5] rounded-[25px] border-r-[3px] border-b-[3px] border-[#2a53d1] flex gap-2 py-3 lg:py-5 px-4 min-h-[145px]"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/qt-5.png') }}"
                                alt="Course planning"
                                class="w-[80px] lg:w-[110px] h-auto object-contain mr-2 lg:mr-4 flex-shrink-0"
                            />
                            <p class="text-gray-800">
                                Tự tay lên bản kế hoạch bán khóa học cho riêng mình, bất cứ
                                sản ngành giáo dục nào
                            </p>
                        </div>
                    </div>

                    <!-- Feature 6 -->
                    <div class="relative pt-6" data-aos-lazy="flip-up">
                        <div
                            class="feature-header text-white py-3 px-6 rounded-lg text-center font-medium"
                        >
                            Tiết kiệm thời gian và chi phí
                        </div>
                        <div
                                class="bg-[#fff1c5] rounded-[25px] border-r-[3px] border-b-[3px] border-[#2a53d1] flex gap-2 py-3 lg:py-5 px-4 min-h-[145px]"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/qt-6.png') }}"
                                alt="Time saving"
                                class="w-[80px] lg:w-[110px] h-auto object-contain mr-2 lg:mr-4 flex-shrink-0"
                            />
                            <p class="text-gray-800">
                                Tự tay xây dựng cấu trúc khóa học chuyên nghiệp, từ nội dung,
                                quay video đến chỉnh sửa và tối ưu chi phí.
                            </p>
                        </div>
                    </div>

                    <!-- Feature 7 -->
                    <div class="relative pt-6" data-aos-lazy="flip-up">
                        <div
                            class="feature-header text-white py-3 px-6 rounded-lg text-center font-medium"
                        >
                            Xây dựng website E-learning
                        </div>
                        <div
                                class="bg-[#fff1c5] rounded-[25px] border-r-[3px] border-b-[3px] border-[#2a53d1] flex gap-2 py-3 lg:py-5 px-4 min-h-[145px]"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/qt-7.png') }}"
                                alt="Website building"
                                class="w-[80px] lg:w-[110px] h-auto object-contain mr-2 lg:mr-4 flex-shrink-0"
                            />
                            <p class="text-gray-800">
                                Xây dựng website học tập nhanh chóng mà không cần biết gì về
                                lập trình
                            </p>
                        </div>
                    </div>

                    <!-- Feature 8 -->
                    <div class="relative pt-6" data-aos-lazy="flip-up">
                        <div
                            class="feature-header text-white py-3 px-6 rounded-lg text-center font-medium"
                        >
                            Gia tăng thu nhập
                        </div>
                        <div
                                class="bg-[#fff1c5] rounded-[25px] border-r-[3px] border-b-[3px] border-[#2a53d1] flex gap-2 py-3 lg:py-5 px-4 min-h-[145px]"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/qt-8.png') }}"
                                alt="Income increase"
                                class="w-[80px] lg:w-[110px] h-auto object-contain mr-2 lg:mr-4 flex-shrink-0"
                            />
                            <p class="text-gray-800">
                                Gia tăng thêm thu nhập
                                <span class="font-bold">KHỦNG</span> đều đặn hàng tháng từ
                                chính kiến thức của mình
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mx-auto !px-3 lg:px-4 pb-10 max-w-6xl">
        <!-- Funnel Icon -->
        <div class="flex justify-center">
            <img
                src="{{ asset('assets/frontend/mst-academy/assets/images/arrow-bottom.png') }}"
                alt="Funnel"
                class="w-[60px] h-auto animate-bounce"
                data-aos-lazy="fade-down"
                data-aos-delay="300"
                data-aos-duration="1000"
                data-aos-repeat="true"
            />
        </div>

        <!-- Main Heading -->
        <h1
            class="text-2xl md:text-3xl lg:text-4xl font-bold text-center mb-4 mt-4"
            data-aos-lazy="fade-up"
        >
            Công thức này áp dụng cho tất cả<br/>các mảng đào tạo
        </h1>

        <!-- Subheading -->
        <p
            class="text-center text-gray-700 mb-12 max-w-3xl mx-auto"
            data-aos-lazy="fade-up"
        >
            Nhiều chuyên gia đã ứng dụng cách làm này và đang phát<br
                class="hidden md:block"
            />
            triển học viên liên tục hàng ngày
        </p>

        <!-- Video Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 md:px-20">
            <!-- Video 1 -->
            <div class="flex flex-col" data-aos-lazy="zoom-in">
                <h3 class="text-xl font-bold text-center mb-4 underline">
                    Helen Hải - Spa
                </h3>
                <div class="relative w-full aspect-[9/16] bg-black">
                    <iframe loading="lazy"
                            data-src="https://www.facebook.com/plugins/video.php?height=476&amp;href=https%3A%2F%2Fwww.facebook.com%2Fchuyengia.dieutridakhoahoc%2Fvideos%2F537555265992117%2F&amp;show_text=false&amp;width=267&amp;t=0"
                            width="100%"
                            height="100%"
                            style="border: none; overflow: hidden"
                            scrolling="no"
                            frameborder="0"
                            allowfullscreen="true"
                            allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
                    ></iframe>
                </div>
            </div>

            <!-- Video 2 -->
            <div class="flex flex-col" data-aos-lazy="zoom-in">
                <h3 class="text-xl font-bold text-center mb-4 underline">
                    Vương Cường - Livestream
                </h3>
                <div class="relative w-full aspect-[9/16] bg-black">
                    <iframe loading="lazy"
                            data-src="https://www.facebook.com/plugins/video.php?height=476&amp;href=https%3A%2F%2Fwww.facebook.com%2FCuccuCameraLivestream%2Fvideos%2F958160435736275%2F&amp;show_text=false&amp;width=267&amp;t=0"
                            width="100%"
                            height="100%"
                            style="border: none; overflow: hidden"
                            scrolling="no"
                            frameborder="0"
                            allowfullscreen="true"
                            allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
                    ></iframe>
                </div>
            </div>

            <!-- Video 3 -->
            <div class="flex flex-col" data-aos-lazy="zoom-in">
                <h3 class="text-xl font-bold text-center mb-4 underline">
                    Bắc Thủy - Thai Giáo
                </h3>
                <div class="relative w-full aspect-[9/16] bg-black">
                    <iframe loading="lazy"
                            data-src="https://www.facebook.com/plugins/video.php?height=476&amp;href=https%3A%2F%2Fwww.facebook.com%2FbacthuycatEDC%2Fvideos%2F587408123988096%2F&amp;show_text=false&amp;width=267&amp;t=0"
                            width="100%"
                            height="100%"
                            style="border: none; overflow: hidden"
                            scrolling="no"
                            frameborder="0"
                            allowfullscreen="true"
                            allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
                    ></iframe>
                </div>
            </div>
            <!-- Video 3 -->
            <div class="flex flex-col" data-aos-lazy="zoom-in">
                <h3 class="text-xl font-bold text-center mb-4 underline">
                    Chu Ngọc Ánh - FnB
                </h3>
                <div class="relative w-full aspect-[9/16] bg-black">
                    <iframe loading="lazy"
                            data-src="https://www.facebook.com/plugins/video.php?height=476&amp;href=https%3A%2F%2Fwww.facebook.com%2Fchungocanhcoach%2Fvideos%2F2788769814648040%2F&amp;show_text=false&amp;width=267&amp;t=0"
                            width="100%"
                            height="100%"
                            style="border: none; overflow: hidden"
                            scrolling="no"
                            frameborder="0"
                            allowfullscreen="true"
                            allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
                    ></iframe>
                </div>
            </div>
            <!-- Video 3 -->
            <div class="flex flex-col" data-aos-lazy="zoom-in">
                <h3 class="text-xl font-bold text-center mb-4 underline">
                    Thùy Linh - Yoga
                </h3>
                <div class="relative w-full aspect-[9/16] bg-black">
                    <iframe loading="lazy"
                            data-src="https://www.facebook.com/plugins/video.php?height=476&amp;href=https%3A%2F%2Fwww.facebook.com%2Fyogadep.kimedia%2Fvideos%2F895837311838808%2F&amp;show_text=false&amp;width=267&amp;t=0"
                            width="100%"
                            height="100%"
                            style="border: none; overflow: hidden"
                            scrolling="no"
                            frameborder="0"
                            allowfullscreen="true"
                            allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
                    ></iframe>
                </div>
            </div>
            <!-- Video 3 -->
            <div class="flex flex-col" data-aos-lazy="zoom-in">
                <h3 class="text-xl font-bold text-center mb-4 underline">
                    Quý - Marketing
                </h3>
                <div class="relative w-full aspect-[9/16] bg-black">
                    <iframe loading="lazy"
                            data-src="https://www.facebook.com/plugins/video.php?height=476&amp;href=https%3A%2F%2Fwww.facebook.com%2Fmstacademy.vn%2Fvideos%2F801345551707981%2F&amp;show_text=false&amp;width=267&amp;t=0"
                            width="100%"
                            height="100%"
                            style="border: none; overflow: hidden"
                            scrolling="no"
                            frameborder="0"
                            allowfullscreen="true"
                            allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
                    ></iframe>
                </div>
            </div>
            <!-- Video 3 -->
            <div class="flex flex-col" data-aos-lazy="zoom-in">
                <h3 class="text-xl font-bold text-center mb-4 underline">
                    Lê Na - FnB
                </h3>
                <div class="relative w-full aspect-[9/16] bg-black">
                    <iframe loading="lazy"
                            data-src="https://www.facebook.com/plugins/video.php?height=476&href=https%3A%2F%2Fwww.facebook.com%2Flenafnb%2Fvideos%2F990940195938303%2F&show_text=false&width=267&t=0"
                            width="100%"
                            height="100%"
                            style="border: none; overflow: hidden"
                            scrolling="no"
                            frameborder="0"
                            allowfullscreen="true"
                            allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
                    ></iframe>
                </div>
            </div>
            <!-- Video 3 -->
            <div class="flex flex-col" data-aos-lazy="zoom-in">
                <h3 class="text-xl font-bold text-center mb-4 underline">
                    Tony Hoàn - Chạy bộ
                </h3>
                <div class="relative w-full aspect-[9/16] bg-black">
                    <iframe loading="lazy"
                            data-src="https://www.facebook.com/plugins/video.php?height=476&href=https%3A%2F%2Fwww.facebook.com%2Fcoachtonyhoan%2Fvideos%2F1712514399357541%2F&show_text=false&width=267&t=0"
                            width="100%"
                            height="100%"
                            style="border: none; overflow: hidden"
                            scrolling="no"
                            frameborder="0"
                            allowfullscreen="true"
                            allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
                    ></iframe>
                </div>
            </div>
            <!-- Video 3 -->
            <div class="flex flex-col" data-aos-lazy="zoom-in">
                <h3 class="text-xl font-bold text-center mb-4 underline">
                    Huyền - Giọng Nói
                </h3>
                <div class="relative w-full aspect-[9/16] bg-black">
                    <iframe loading="lazy"
                            data-src="https://www.facebook.com/plugins/video.php?height=476&href=https%3A%2F%2Fwww.facebook.com%2F61564656506034%2Fvideos%2F2024014018090422%2F&show_text=false&width=267&t=0"
                            width="100%"
                            height="100%"
                            style="border: none; overflow: hidden"
                            scrolling="no"
                            frameborder="0"
                            allowfullscreen="true"
                            allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
                    ></iframe>
                </div>
            </div>
        </div>

        <div
            class="text-center justify-start mx-auto text-base lg:text-xl mt-10"
            data-aos-lazy="fade-up"
        >
            <span class="text-[#1e1e1e] font-normal"
            >Và còn nhiều chuyên gia nữa và quan trọng họ </span
            ><br/><span class="text-[#1e1e1e] font-normal underline"
            >không phải là những người quá nổi tiếng</span
            ><span class="text-[#1e1e1e] font-normal"> <br/></span
            ><span class="text-[#1e1e1e] font-extrabold">NHƯNG</span
            ><span class="text-[#1e1e1e] font-normal"> vẫn </span
            ><span class="text-[#1e1e1e] font-extrabold"
            >bán khóa học được một cách đơn giản và nhẹ nhàng</span
            >
        </div>

        <div class="flex justify-center mt-8" data-aos-lazy="fade-up">
            <img
                src="{{ asset('assets/frontend/mst-academy/assets/images/feedback-hv.png') }}"
                alt="Funnel"
                class="w-full max-w-[437px] h-auto"
            />
        </div>
        <div
            class="youtube-video-container mt-5"
            data-video-src="https://www.youtube.com/embed/MsbpzhzFdcI?autoplay=1&rel=0&modestbranding=1"
            data-poster="{{ asset('assets/frontend/mst-academy/assets/images/maxresdefault.jpg') }}"
            data-aos-lazy="fade-up"
        >
            <div
                class="relative aspect-video bg-black rounded-lg overflow-hidden group"
            >
                <!-- Poster Image -->
                <div class="poster-wrapper absolute inset-0 z-10">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/maxresdefault.jpg') }}"
                        alt="Video Thumbnail"
                        class="w-full h-full object-cover"
                    />

                    <!-- Play Button -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <button
                            class="play-button w-16 h-16 bg-blue-500 bg-opacity-80 rounded-full flex items-center justify-center transition-transform duration-300 hover:scale-110 focus:outline-none"
                        >
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-8 w-8 text-white"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                            >
                                <path
                                    fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                                    clip-rule="evenodd"
                                />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- YouTube iframe container (initially empty) -->
                <div class="iframe-container hidden w-full h-full"></div>
            </div>
        </div>
    </div>

    <!-- Course Content Section -->
    <div class="bg-[#051f4d] text-white pt-12">
        <div class="max-w-[1200px] mx-auto !px-3 lg:px-4">
            <!-- Header Section -->
            <div class="text-center mb-12">
                <h1 class="text-3xl font-bold mb-6" data-aos-lazy="fade-up">
                    Và đây là nội dung chi tiết về khoá học này
                </h1>
                <p class="text-base lg:text-lg mb-2" data-aos-lazy="fade-up">
                    Tất cả kiến thức đã đóng gói thành bộ video online
                </p>
                <p class="text-base lg:text-lg" data-aos-lazy="fade-up">
                    Bạn có thể học bất cứ lúc nào bạn muốn.
                </p>
            </div>

            <!-- Main Content Section -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12">
                <!-- Left Column - Course Topics -->
                <div class="lg:w-1/2" data-aos-lazy="fade-right">
                    <div class="flex items-center gap-3 mb-6 flex-wrap">
                        <div
                            class="inline-block bg-white text-[#001337] font-bold text-xl px-4 py-1 rounded-full"
                        >
                            Phần 1:
                        </div>
                        <h2 class="text-lg font-bold">
                            Tư duy xây dựng bán khoá học online
                        </h2>
                    </div>

                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >E-learning kiếm tiền khủng như thế nào? CaseStudy 2 nhân sự
                    và casestudy 10 nhân sự</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Hiểu 7 mô hình kinh doanh</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Hiểu công thức chỉnh tạo nên mảng E-learning thành công</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Tất cả các kỹ năng cần thiết để làm tốt E-learning</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Các thiết bị/phần mềm cần thiết - chi phí siêu rẻ</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Nếu bắt đầu từ số 0 thì làm như nào</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Nếu đang có khoá học/ đang dạy thì sẽ bắt đầu hay tối ưu như
                    nào?</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Chi phí rẻ bất ngờ khi làm E-learning</span
                            >
                        </li>
                    </ul>
                </div>

                <!-- Right Column - Course Summary -->
                <div class="lg:w-1/2" data-aos-lazy="fade-left">
                    <div
                        class="youtube-video-container"
                        data-video-src="https://www.youtube.com/embed/SanLTpVg_Uk"
                        data-poster="{{ asset('assets/frontend/mst-academy/assets/images/hqdefault1.jpg') }}"
                    >
                        <div
                            class="relative aspect-video bg-black rounded-lg overflow-hidden group"
                        >
                            <!-- Poster Image -->
                            <div class="poster-wrapper absolute inset-0 z-10">
                                <img
                                    src="{{ asset('assets/frontend/mst-academy/assets/images/hqdefault1.jpg') }}"
                                    alt="Video Thumbnail"
                                    class="w-full h-full object-cover"
                                />

                                <!-- Play Button -->
                                <div
                                    class="absolute inset-0 flex items-center justify-center"
                                >
                                    <button
                                        class="play-button w-16 h-16 bg-blue-500 bg-opacity-80 rounded-full flex items-center justify-center transition-transform duration-300 hover:scale-110 focus:outline-none"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-8 w-8 text-white"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                                                clip-rule="evenodd"
                                            />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- YouTube iframe container (initially empty) -->
                            <div class="iframe-container hidden w-full h-full"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Section -->
            <div class="text-center mt-16">
                <div
                    class="w-full text-center justify-start text-[#fff4ce] text-xl font-extrabold underline"
                    data-aos-lazy="fade-up"
                >
                    Sau phần này bạn có thể xác định được cách bán chính xác cho khoá
                    học của mình
                </div>
            </div>

            <div class="flex flex-col lg:flex-row gap-4 mt-8">
                <div class="w-full lg:w-2/5" data-aos-lazy="fade-right">
                    <img src="{{ asset('assets/frontend/mst-academy/assets/images/map-1.png') }}" alt=""
                         class="w-full rounded-lg md:h-full md:object-cover"/>
                </div>
                <div class="w-full lg:w-3/5" data-aos-lazy="fade-left">
                    <img src="{{ asset('assets/frontend/mst-academy/assets/images/map-2.png') }}" alt=""
                         class="w-full rounded-lg md:h-full md:object-cover"/>
                </div>
            </div>
            <div class="w-full" data-aos-lazy="fade-down">
                <img
                    src="{{ asset('assets/frontend/mst-academy/assets/images/line-connect.svg') }}"
                    alt=""
                    class="xl:ml-[50%]"
                />
            </div>
            <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
                <div
                    class="youtube-video-container"
                    data-video-src="https://www.youtube.com/embed/7Q5OvgANcZ8"
                    data-poster="{{ asset('assets/frontend/mst-academy/assets/images/hqdefault.jpg') }}"
                    data-aos-lazy="fade-right"
                >
                    <div
                        class="relative aspect-video bg-black rounded-lg overflow-hidden group"
                    >
                        <!-- Poster Image -->
                        <div class="poster-wrapper absolute inset-0 z-10">
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/hqdefault.jpg') }}"
                                alt="Video Thumbnail"
                                class="w-full h-full object-cover"
                            />

                            <!-- Play Button -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <button
                                    class="play-button w-16 h-16 bg-blue-500 bg-opacity-80 rounded-full flex items-center justify-center transition-transform duration-300 hover:scale-110 focus:outline-none"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-8 w-8 text-white"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                                            clip-rule="evenodd"
                                        />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- YouTube iframe container (initially empty) -->
                        <div class="iframe-container hidden w-full h-full"></div>
                    </div>
                </div>
                <div class="w-full pt-8" data-aos-lazy="fade-left">
                    <div class="flex items-center gap-3 mb-6 flex-wrap">
                        <div
                            class="inline-block bg-white text-[#001337] font-bold text-xl px-4 py-1 rounded-full"
                        >
                            Phần 2:
                        </div>
                        <h2 class="text-lg font-bold">
                            Xác định khách hàng và dòng sản phẩm bền vững
                        </h2>
                    </div>

                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Xác định khách hàng mục tiêu</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Lên chuỗi các khoá học bền vững theo từng level khách
                    hàng</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Hiểu tâm lý mua khoá học để chọn làm sản phẩm phù hợp</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Tạo ra một bảng kế hoạch kinh doanh đầy đủ dựa theo chuỗi sản
                    phẩm trên</span
                            >
                        </li>
                    </ul>
                </div>
            </div>

            <div class="text-center mt-16" data-aos-lazy="fade-up">
                <div
                    class="w-full text-center justify-start text-[#fff4ce] text-xl font-extrabold underline"
                >
                    Tạo ra một bảng kế hoạch kinh doanh đầy đủ dựa theo chuỗi sản phẩm
                    trên
                </div>
            </div>
            <div class="flex gap-4 mt-8 flex-col lg:flex-row" data-aos-lazy="fade-up">
                <div class="lg:w-[45%]" data-aos-lazy="fade-right">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/map-3.png') }}"
                        alt=""
                        class="w-full h-full object-cover rounded-lg"
                    />
                </div>
                <div class="lg:w-[55%]" data-aos-lazy="fade-left">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/map-4.png') }}"
                        alt=""
                        class="w-full h-full object-cover rounded-lg"
                    />
                </div>
            </div>
            <div class="flex" data-aos-lazy="fade-up">
                <div class="lg:w-[45%]" data-aos-lazy="fade-right"></div>
                <div class="lg:w-[55%]" data-aos-lazy="fade-left">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/line-connect.svg') }}"
                        alt=""
                        class="transform -scale-x-100"
                    />
                </div>
            </div>
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12">
                <!-- Left Column - Course Topics -->
                <div class="lg:w-1/2" data-aos-lazy="fade-right">
                    <div class="flex items-center gap-3 mb-6 flex-wrap">
                        <div
                            class="inline-block bg-white text-[#001337] font-bold text-xl px-4 py-1 rounded-full"
                        >
                            Phần 3:
                        </div>
                        <h2 class="text-lg font-bold">
                            Đóng gói khoá học online nhanh chóng
                        </h2>
                    </div>

                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Cấu trúc một khoá học online</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Hiểu 7 mô hình kinh doanh</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Tư duy xây dựng bài giảng đơn giản theo từng bước</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Sử dụng AI để tên nội dung bài giảng chỉ với 5 phút</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Hướng dẫn tạo Slide bài học nhanh chóng</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Hướng dẫn sử dụng OBS</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Edit video siêu dễ với capcut</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Nền tảng duy nhất bạn cần sử dụng để tạo website học online
                    và các hoạt động bán khoá học</span
                            >
                        </li>
                    </ul>
                </div>

                <!-- Right Column - Course Summary -->
                <div class="lg:w-1/2" data-aos-lazy="fade-left">
                    <div
                        class="youtube-video-container"
                        data-video-src="https://www.youtube.com/embed/kZ417S_GfgM"
                        data-poster="{{ asset('assets/frontend/mst-academy/assets/images/hqdefault3.jpg') }}"
                    >
                        <div
                            class="relative aspect-video bg-black rounded-lg overflow-hidden group"
                        >
                            <!-- Poster Image -->
                            <div class="poster-wrapper absolute inset-0 z-10">
                                <img
                                    src="{{ asset('assets/frontend/mst-academy/assets/images/hqdefault3.jpg') }}"
                                    alt="Video Thumbnail"
                                    class="w-full h-full object-cover"
                                />

                                <!-- Play Button -->
                                <div
                                    class="absolute inset-0 flex items-center justify-center"
                                >
                                    <button
                                        class="play-button w-16 h-16 bg-blue-500 bg-opacity-80 rounded-full flex items-center justify-center transition-transform duration-300 hover:scale-110 focus:outline-none"
                                    >
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            class="h-8 w-8 text-white"
                                            viewBox="0 0 20 20"
                                            fill="currentColor"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                                                clip-rule="evenodd"
                                            />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- YouTube iframe container (initially empty) -->
                            <div class="iframe-container hidden w-full h-full"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div
                class="w-full max-w-[1139px] p-6 relative rounded-[20px] border border-[#fff4ce] border-dashed bg-[#002084] my-8"
                data-aos-lazy="fade-up"
            >
                <div class="text-white text-base font-semibold">
                    Việc đóng gói khoá học rất đơn giản bản chỉ cần có: Laptop và điện
                    thoại không cần thiết bị nào cầu kỳ <br/>
                    Sau đó hãy lên slide bài giảng thông qua canva và quay lại màn hình
                    là thành bài giảng <br/>
                    Đối với các khoá học cần quay quá trình thì bạn cũng chỉ cần điện
                    thoại <br/>
                    <br/>
                    là quay được như khoá học yoga ở trên cũng đều quay hết bằng điên
                    thoại Nội dung bài giảng thì hoàn toàn có thể sử dụng AI để lên giáo
                    án và thêm các kinh nghiệm của bạn vào
                </div>
            </div>

            <div class="text-center mt-16" data-aos-lazy="fade-up">
                <div
                    class="w-full text-center justify-start text-[#fff4ce] text-xl font-extrabold underline"
                >
                    Sau phần này bạn có thể tạo ra khoá học đơn giản và nền tảng học cho
                    học viên với chi phí thấp
                </div>
            </div>

            <div class="flex flex-col lg:flex-row gap-4 mt-8">
                <div class="w-full lg:w-3/5" data-aos-lazy="fade-right">
                    <img src="{{ asset('assets/frontend/mst-academy/assets/images/map-5.png') }}" alt=""
                         class="w-full rounded-lg"/>
                </div>
                <div class="w-full lg:w-2/5" data-aos-lazy="fade-left">
                    <img src="{{ asset('assets/frontend/mst-academy/assets/images/map-6.png') }}" alt=""
                         class="w-full rounded-lg md:h-full md:object-cover"/>
                </div>
            </div>
                <div class="w-full" data-aos-lazy="fade-down">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/line-connect.svg') }}"
                        alt=""
                        class="xl:ml-[50%]"
                    />
                </div>

            <div class="grid grid-cols-1 xl:grid-cols-2 gap-4">
                <div
                    class="youtube-video-container lg:mt-[60px]"
                    data-video-src="https://www.youtube.com/embed/-Pj2knI1BE0"
                    data-poster="{{ asset('assets/frontend/mst-academy/assets/images/hqdefault4.jpg') }}"
                    data-aos-lazy="fade-right"
                >
                    <div
                        class="relative aspect-video bg-black rounded-lg overflow-hidden group"
                    >
                        <!-- Poster Image -->
                        <div class="poster-wrapper absolute inset-0 z-10">
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/hqdefault4.jpg') }}"
                                alt="Video Thumbnail"
                                class="w-full h-full object-cover"
                            />

                            <!-- Play Button -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <button
                                    class="play-button w-16 h-16 bg-blue-500 bg-opacity-80 rounded-full flex items-center justify-center transition-transform duration-300 hover:scale-110 focus:outline-none"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-8 w-8 text-white"
                                        viewBox="0 0 20 20"
                                        fill="currentColor"
                                    >
                                        <path
                                            fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                                            clip-rule="evenodd"
                                        />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- YouTube iframe container (initially empty) -->
                        <div class="iframe-container hidden w-full h-full"></div>
                    </div>
                </div>
                <div class="w-full pt-8" data-aos-lazy="fade-left">
                    <div class="flex items-center gap-3 mb-6 flex-wrap">
                        <div
                            class="inline-block bg-white text-[#001337] font-bold text-xl px-4 py-1 rounded-full"
                        >
                            Phần 4:
                        </div>
                        <h2 class="text-lg font-bold">Marketing và bán khoá học</h2>
                    </div>

                    <ul class="space-y-3">
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Hiểu về phễu bán hàng</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Thiết kế phễu bán hàng</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Bán khoá học thông qua thương hiệu cá nhân</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Tạo ra trang Lading Page giới thiệu khoá học và công thức
                    landing page</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Viết content bán hàng và kịch bản quảng cáo chuyển đổi khách
                    hàng cao</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Chuẩn bị fanpage để quảng cáo</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Chuẩn bị tài khoản quảng cáo</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Chạy quảng cáo qua tin nhắn</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Chạy quảng cáo chuyển đổi</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Chuẩn bị tài khoản tiktok</span>
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md"
                            >Chạy quảng cáo tiktok chuyển đổi</span
                            >
                        </li>
                        <li class="flex items-start">
                            <div
                                class="flex-shrink-0 w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center mt-1"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M5 13l4 4L19 7"
                                    />
                                </svg>
                            </div>
                            <span class="ml-3 text-md">Tư duy bán hàng lần 2,3,4,5</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="text-center mt-16" data-aos-lazy="fade-up">
                <div
                    class="w-full mb-6 text-center justify-start text-[#fff4ce] text-xl font-extrabold underline"
                >
                    Sau phần này bạn sẽ marketing bắt đầu bán khoá học của bạn
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-8" data-aos-lazy="fade-up">
                <div class="w-full" data-aos-lazy="fade-right">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/map-7.png') }}"
                        alt=""
                        class="w-full h-full object-cover rounded-lg"
                    />
                </div>
                <div class="w-full" data-aos-lazy="fade-left">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/map-8.png') }}"
                        alt=""
                        class="w-full h-full object-cover rounded-lg"
                    />
                </div>
            </div>

            <div class="w-full flex justify-center mt-8" data-aos-lazy="zoom-out">
                <div class="mt-8 mb-12 relative">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/circle-grid.png') }}"
                        class="absolute -top-3 -left-3"
                    />
                    <a href="#regis-section"
                       class="relative text-white font-bold px-16 rounded-full text-xl md:text-2xl transition duration-300 transform hover:scale-105 btn-register-now"
                    >
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/btn-regis-now.png') }}"
                            alt="ĐĂNG KÝ NGAY"
                            class="w-full h-[70px] absolute top-0 left-0 !max-h-[inherit]"
                        />
                        <span class="relative z-10 !translate-y-[10px]">ĐĂNG KÝ NGAY</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Testimonials Section -->
    <div
        class="bg-gradient-to-r from-[#002084] via-[#0e34ac] to-[#002084] text-white py-16"
    >
        <div class="container max-w-[1200px] mx-auto px-3 md:px-8">
            <!-- Section Heading -->
            <h2
                class="text-lg md:text-4xl font-bold text-center mb-2 lg:mb-4"
                data-aos-lazy="fade-up"
            >
                Rất nhiều chuyên gia đã ra khóa và đang
            </h2>
            <h2
                class="text-lg md:text-4xl font-bold text-center text-yellow-400 mb-4 lg:mb-8 underline"
                data-aos-lazy="fade-up"
            >
                tuyển sinh thành công
                <span class="inline-block ml-2 w-[25px] lg:w-[35px]">
                <img src="{{ asset('assets/frontend/mst-academy/assets/images/rocket-icon.svg') }}"
                /></span>
            </h2>

            <!-- Section Description -->
            <div class="text-center max-w-3xl mx-auto mb-16" data-aos-lazy="fade-up">
                <p class="text-base lg:text-xl">
                    Bất cứ mảng kiến thức nào cũng có thể làm được
                    <span class="font-bold">và bạn cũng thế!</span>
                </p>
            </div>

            <!-- Testimonial Cards -->
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Testimonial Card 1 -->
                <div
                    class="bg-[#000E38] rounded-lg overflow-hidden border border-blue-700"
                    data-aos-lazy="zoom-in"
                >
                    <!-- Card Image -->
                    <div class="relative">
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/cg-1.png') }}"
                            alt="Helen Hải"
                            class="w-full"
                            onerror="this.src='https://via.placeholder.com/400x250/0D47A1/FFFFFF?text=Course+Creator'; this.onerror=null;"
                        />
                    </div>

                    <!-- Card Content -->
                    <div class="p-3">
                        <h3 class="text-xl font-bold mb-1">Chuyên gia: Helen Hải</h3>
                        <h4 class="text-lg font-bold mb-4">
                            Khóa học: Ma trận dịch vụ Spa
                        </h4>

                        <p class="mb-3 text-gray-300 italic text-lg">
                            Trước khi làm khóa online:
                        </p>

                        <!-- Before Challenges -->
                        <div class="space-y-2 mb-8">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-times text-white text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-gray-300">
                                    <strong class="underline">Tốn nhiều thời gian</strong> để
                                    dạy trực tiếp
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-times text-white text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-gray-300">
                                    Dạy đi dạy lại nhiều một mảng kiến thức
                                    <strong class="underline"
                                    >sinh ra cảm giác nhàm chán</strong
                                    >
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-times text-white text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-gray-300">
                                    Không ứng dụng marketing online
                                    <strong class="underline"
                                    >số lượng học viên không được đều.</strong
                                    >
                                </p>
                            </div>
                        </div>

                        <!-- After Results -->
                        <div
                            class="bg-[#ebbd42] rounded-[100px] text-[#002084] text-center py-3 font-bold rounded-lg translate-y-[10px]"
                        >
                            Sau khi chuyển sang bán Elearning
                        </div>

                        <div
                            class="space-y-3 bg-[#002084] rounded-[20px] border border-[#ffbd4d] border-dashed px-3 py-6"
                        >
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>
                        <span class="font-bold underline"
                        >Đạt doanh số trăm triệu</span
                        >
                                    ngày sau 1 tháng
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>
                                    Xây dựng được cộng đồng và bán được những gói tư vấn giá cao
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>
                                    Giảm thời gian đào tạo và có thêm thời gian mở rộng kinh
                                    doanh sang mảng khác
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>Xây dựng được THCN qua khóa Elearning</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 2 -->
                <div
                    class="bg-[#000E38] rounded-lg overflow-hidden border border-blue-700"
                    data-aos-lazy="zoom-in"
                >
                    <!-- Card Image -->
                    <div class="relative">
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/cg-2.png') }}"
                            alt="Hoàng Lê Na"
                            class="w-full"
                            onerror="this.src='https://via.placeholder.com/400x250/0D47A1/FFFFFF?text=Course+Creator'; this.onerror=null;"
                        />
                    </div>

                    <!-- Card Content -->
                    <div class="p-3">
                        <h3 class="text-xl font-bold mb-1">Chuyên gia: Hoàng Lê Na</h3>
                        <h4 class="text-lg font-bold mb-4">Khóa học: Vận Hành F&B</h4>

                        <p class="mb-3 text-gray-300 italic text-lg">
                            Trước khi làm khóa online:
                        </p>

                        <!-- Before Challenges -->
                        <div class="space-y-2 mb-8">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-times text-white text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-gray-300 font-bold underline">
                                    Chưa có kinh nghiệm làm đào tạo
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-times text-white text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-gray-300">Không có thương hiệu cá nhân</p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-times text-white text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-gray-300">
                                    Không quá giỏi về công nghệ, chỉ có kinh nghiệm và kỹ năng
                                    chuyên môn FnB
                                </p>
                            </div>
                        </div>

                        <!-- After Results -->
                        <div
                            class="bg-[#ebbd42] rounded-[100px] text-[#002084] text-center py-3 font-bold rounded-lg translate-y-[10px]"
                        >
                            Sau khi chuyển sang bán Elearning
                        </div>

                        <div
                            class="space-y-3 bg-[#002084] rounded-[20px] border border-[#ffbd4d] border-dashed px-3 py-6"
                        >
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>
                        <span class="font-bold underline"
                        >Đạt doanh số trăm triệu</span
                        >
                                    ngày sau 1 tháng
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>
                                    Xây dựng được cộng đồng và bán được những gói tư vấn giá cao
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>
                                    Giảm thời gian đào tạo và có thêm thời gian mở rộng kinh
                                    doanh sang mảng khác
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>Xây dựng được THCN qua khóa Elearning</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Testimonial Card 3 -->
                <div
                    class="bg-[#000E38] rounded-lg overflow-hidden border border-blue-700"
                    data-aos-lazy="zoom-in"
                >
                    <!-- Card Image -->
                    <div class="relative">
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/cg-3.png') }}"
                            alt="Tony Hoàn"
                            class="w-full"
                            onerror="this.src='https://via.placeholder.com/400x250/0D47A1/FFFFFF?text=Course+Creator'; this.onerror=null;"
                        />
                    </div>

                    <!-- Card Content -->
                    <div class="p-3">
                        <h3 class="text-xl font-bold mb-1">Chuyên gia: Tony Hoàn</h3>
                        <h4 class="text-lg font-bold mb-4">Khóa học: 21 ngày chạy bộ</h4>

                        <p class="mb-3 text-gray-300 italic text-lg">Trước khi làm khóa online:</p>

                        <!-- Before Challenges -->
                        <div class="space-y-2 mb-8">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-times text-white text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-gray-300">
                                    Chỉ đào tạo trong cộng đồng quen biết
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-times text-white text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-gray-300">Không có thương hiệu cá nhân</p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-times text-white text-xs"></i>
                                    </div>
                                </div>
                                <p class="text-gray-300">
                                    Không chắc chắn là khóa học chạy bộ có thể
                                    <span class="font-bold underline"
                                    >bán online thành công</span
                                    >
                                </p>
                            </div>
                        </div>

                        <!-- After Results -->
                        <div
                            class="bg-[#ebbd42] rounded-[100px] text-[#002084] text-center py-3 font-bold rounded-lg translate-y-[10px]"
                        >
                            Sau khi chuyển sang bán Elearning
                        </div>

                        <div
                            class="space-y-3 bg-[#002084] rounded-[20px] border border-[#ffbd4d] border-dashed px-3 py-6"
                        >
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>
                        <span class="font-bold underline"
                        >Đạt doanh số trăm triệu</span
                        >
                                    ngày sau 1 tháng
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>
                                    Xây dựng được cộng đồng và bán được những gói tư vấn giá cao
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>
                                    Giảm thời gian đào tạo và có thêm thời gian mở rộng kinh
                                    doanh sang mảng khác
                                </p>
                            </div>

                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3 mt-1">
                                    <div
                                        class="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center"
                                    >
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                </div>
                                <p>Xây dựng được THCN qua khóa Elearning</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Showcase Section -->
    <div class="bg-blue-900 py-16">
        <div class="container max-w-[1440px] mx-auto px-4 md:px-8">
            <!-- Course Grid -->
            <div
                class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-3 gap-y-14"
            >
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="100"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg1.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Khoá học tài chính cá nhân<br/>Chuyên gia: Hoàng Mai
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg1-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg1-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="200"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg2.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Khủng Hoảng hiện Sinh<br/>Chuyên Gia: Thanh Vân
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg2-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg2-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="300"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg3.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Quản trị trường mầm non<br/>Chuyên Gia: Thanh Nhàn
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg3-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg3-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="400"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg4.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Nghệ Thuật Thuyết Trình<br/>Chuyên gia: Tô Hiền
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg4-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg4-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="500"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg5.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Phun xăm chân mày<br/>Chuyên gia: Hải Nam
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg5-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg5-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="600"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg6.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Bán hàng ngành làm đẹp<br/>Chuyên gia: Diệu Thu
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg6-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg6-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="700"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg7.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Trải nghiệm khách hàng Spa<br/>Chuyên gia: Giang Lê
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg7-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg7-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="800"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg8.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Giảng dạy tiếng Anh<br/>Chuyên gia: Bùi Huệ
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg8-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg8-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="900"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg9.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Nghệ thuật giảng dạy<br/>Chuyên gia: Huỳnh Khôi
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg9-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg9-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="1000"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg10.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Nghệ thuật kê đơn<br/>Bác Sĩ: Quỳnh
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg10-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg10-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="1100"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg11.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Tài Chính Cho Sếp<br/>Chuyên gia: Minh Đỗ
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg11-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg11-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
                <div
                    class="h-[210px] relative bg-white rounded-[14.40px] p-2"
                    data-aos-lazy="fade-up"
                    data-aos-delay="1200"
                >
                    <div class="flex items-center gap-2">
                        <img
                            class="w-[46.75px] h-[46.75px] rounded-full"
                            src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg12.png') }}"
                        />
                        <div class="text-[#111111] text-sm font-extrabold">
                            Cải thiện giọng nói<br/>Chuyên Gia: Huyền Voice
                        </div>
                    </div>
                    <!-- <img
                        class="w-full !max-h-[inherit] rounded-[11.01px] object-cover translate-y-3 mx-auto h-[160px]"
                        src="{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg12-detail.png') }}"
                    /> -->
                    <div class="w-full h-[195px] rounded-lg" style="
                        background: url('{{ asset('assets/frontend/mst-academy/assets/images/chuyengia/cg12-detail.png') }}');
                        background-repeat: no-repeat;
                        background-position: left top;
                        background-size: cover;
                        background-attachment: scroll;
                        background-origin: content-box;
                        "></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Instructor Profile Section -->
    <div class="container mx-auto px-4 py-12 max-w-6xl bg-white">
        <div class="text-center mb-8">
            <h2
                class="text-xl md:text-4xl font-bold text-gray-900"
                data-aos-lazy="fade-up"
            >
                VÀ MÌNH SẼ LÀ NGƯỜI HƯỚNG DẪN BẠN TRỰC TIẾP
            </h2>
            <p class="text-lg text-gray-700 mt-2" data-aos-lazy="fade-up">
                Chuyên gia thực chiến hướng dẫn bạn từng bước làm chi tiết
            </p>
        </div>

        <div class="flex flex-col md:flex-row items-center justify-center gap-8">
            <!-- Left side - Profile Image and Name -->
            <div class="w-full md:w-1/3 flex flex-col items-center">
                <div class="relative">
                    <!-- Decorative element -->
                    <div class="absolute left-3 top-3 w-16 h-16">
                        <svg viewBox="0 0 100 100" class="w-full h-full">
                            <defs>
                                <pattern
                                    id="diagonalHatch"
                                    patternUnits="userSpaceOnUse"
                                    width="10"
                                    height="10"
                                >
                                    <path
                                        d="M-1,1 l2,-2 M0,10 l10,-10 M9,11 l2,-2"
                                        stroke="#FF6B6B"
                                        stroke-width="2"
                                    />
                                </pattern>
                            </defs>
                            <circle cx="50" cy="50" r="50" fill="url(#diagonalHatch)"/>
                        </svg>
                    </div>

                    <!-- Profile Image -->
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/main-avt.png') }}"
                        alt="Chuyên gia Mai Kim Quý"
                        class="size-[300px] object-cover rounded-full"
                        data-aos-lazy="zoom-in"
                    />
                </div>

                <div class="text-center mt-6">
                    <h3 class="text-2xl font-bold text-gray-900">
                        Chuyên gia: Mai Kim Quý
                    </h3>
                    <p class="text-xl text-gray-700">Sáng lập: KIMEDIA</p>
                </div>
            </div>

            <!-- Right side - Experience Boxes -->
            <div class="w-full md:w-2/3 space-y-6">
                <div class="relative" data-aos-lazy="fade-up" data-aos-delay="100">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/box-shadow-red.png') }}"
                        alt=""
                        class="w-full !h-[180px] lg:!h-auto"
                    />
                    <div
                        class="flex gap-4 absolute top-0 left-0 w-full h-full items-center px-4"
                    >
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/dl-icon-1.png') }}" alt=""/>
                        <div>
                            <p class="text-gray-800 font-medium">
                                Hơn 9 năm kinh nghiệm trong Digital Marketing: quảng cáo FB,
                                quảng cáo GG, quảng cáo tiktok... 4 năm kinh doanh online
                            </p>
                        </div>
                    </div>
                </div>
                <div class="relative" data-aos-lazy="fade-up" data-aos-delay="200">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/box-shadow-blue.png') }}"
                        alt=""
                        class="w-full !h-[180px] lg:!h-auto"
                    />
                    <div
                        class="flex gap-4 absolute top-0 left-0 w-full h-full items-center px-4"
                    >
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/dl-icon-2.png') }}" alt=""/>
                        <div>
                            <p class="text-gray-800 font-medium">
                                Kinh nghiệm trong đa ngành hàng: thời trang,<br/>
                                giáo dục, mỹ phẩm, công nghệ, Spa, sự kiện...
                            </p>
                        </div>
                    </div>
                </div>
                <div class="relative" data-aos-lazy="fade-up" data-aos-delay="300">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/box-shadow-yellow.png') }}"
                        alt=""
                        class="w-full !h-[180px] lg:!h-auto"
                    />
                    <div
                        class="flex gap-4 absolute top-0 left-0 w-full h-full items-center px-4"
                    >
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/dl-icon-3.png') }}" alt=""/>
                        <div>
                            <p class="text-gray-800 font-medium">
                                Founder KIMEDIA Giáo dục trực tuyến: <br/>Engmates, FEDU,
                                MSTs, Yoga Đẹp… <br/>Marketing Agency
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div
            class="flex flex-col md:flex-row items-center justify-center gap-8 mt-8"
        >
            <div class="w-full md:w-2/3 space-y-6 !order-2 md:!order-1">
                <div class="relative" data-aos-lazy="fade-up" data-aos-delay="100">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/box-shadow-red.png') }}"
                        alt=""
                        class="w-full !h-[180px] lg:!h-auto"
                    />
                    <div
                        class="flex gap-4 absolute top-0 left-0 w-full h-full items-center px-4"
                    >
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/dl-icon-1.png') }}" alt=""/>
                        <div>
                            <p class="text-gray-800 font-medium">
                                8 Năm kinh nghiệm quản lý Marketing tại các công ty công nghệ,
                                giáo dục
                            </p>
                        </div>
                    </div>
                </div>
                <div class="relative" data-aos-lazy="fade-up" data-aos-delay="200">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/box-shadow-blue.png') }}"
                        alt=""
                        class="w-full !h-[180px] lg:!h-auto"
                    />
                    <div
                        class="flex gap-4 absolute top-0 left-0 w-full h-full items-center px-4"
                    >
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/dl-icon-2.png') }}" alt=""/>
                        <div>
                            <p class="text-gray-800 font-medium">
                                8 năm Copywriter và tư vấn thương hiệu
                            </p>
                        </div>
                    </div>
                </div>
                <div class="relative" data-aos-lazy="fade-up" data-aos-delay="300">
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/box-shadow-yellow.png') }}"
                        alt=""
                        class="w-full !h-[180px] lg:!h-auto"
                    />
                    <div
                        class="flex gap-4 absolute top-0 left-0 w-full h-full items-center px-4"
                    >
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/dl-icon-3.png') }}" alt=""/>
                        <div>
                            <p class="text-gray-800 font-medium">
                                4 năm Quản lý sale và Marketing, Giảng viên đóng gói khoá học
                                và quảng cáo trực tuyến của Getacademy thị trường Vietnam
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full md:w-1/3 flex flex-col items-center !order-1 md:!order-2">
                <div class="relative" data-aos-lazy="zoom-in">
                    <!-- Profile Image -->
                    <img
                        src="{{ asset('assets/frontend/mst-academy/assets/images/main-avt2.png') }}"
                        alt="Chuyên gia Mai Kim Quý"
                        class="size-[300px] object-cover rounded-full"
                    />
                </div>

                <div class="text-center mt-6">
                    <h3 class="text-2xl font-bold text-gray-900">
                        Chuyên gia: Hiếu Nguyễn
                    </h3>
                    <p class="text-xl text-gray-700">Giám đốc dự án</p>
                </div>
            </div>
        </div>
    </div>

    <div class="flex justify-center my-6">
        <iframe loading="lazy"
                class="lg:h-[500px] !h-[380px]"
                name="__tt_embed__v36674775540400730"
                sandbox="allow-popups allow-popups-to-escape-sandbox allow-scripts allow-top-navigation allow-same-origin"
                data-src="https://www.tiktok.com/embed/@maikimquy.msts"
                style="
            width: 100%;
            height: 500px;
            display: block;
            visibility: unset;
            max-height: 500px;
            overflow: hidden;
            "
        ></iframe>
    </div>
    <div class="flex justify-center">
        <img
            src="{{ asset('assets/frontend/mst-academy/assets/images/arrow-bottom.png') }}"
            alt="Funnel"
            class="w-[60px] h-auto animate-bounce"
            data-aos-lazy="fade-down"
            data-aos-delay="300"
            data-aos-duration="1000"
            data-aos-repeat="true"
        />
    </div>

    <!-- Instructor Experience Section -->
    <div class="py-16 relative overflow-hidden section-event">
        <div class="absolute bottom-0 right-0">
            <div
                class="w-36 h-36 transform rotate-45 translate-x-12 translate-y-12"
            >
                <div class="w-full h-full border-8 border-blue-200"></div>
            </div>
        </div>

        <div class="container max-w-[1200px] mx-auto px-4 md:px-8 relative z-10">
            <div class="max-w-6xl mx-auto">
                <!-- Introduction Text -->
                <div class="text-center mb-12">
                    <p class="text-xl text-gray-700" data-aos-lazy="fade-up">
                        Mình cũng đã áp dụng công thức này cho
                    </p>
                    <h2
                        class="text-3xl md:text-4xl font-bold text-gray-900 mt-2"
                        data-aos-lazy="fade-up"
                    >
                        7 sản phẩm <span class="text-gray-700 font-normal">của mình</span>
                    </h2>
                </div>

                <!-- Brand Logos -->
                <div
                    class="grid grid-cols-2 md:grid-cols-3 gap-6 mb-16 max-w-[700px] mx-auto"
                >
                    <!-- Brand 1 -->
                    <div
                        class="flex justify-center items-center p-4"
                        data-aos-lazy="fade-up"
                        data-aos-delay="100"
                    >
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/partner-1.png') }}"
                            alt="ENGMATES"
                            class="max-w-full h-auto"
                        />
                    </div>

                    <!-- Brand 2 -->
                    <div
                        class="flex justify-center items-center p-4"
                        data-aos-lazy="fade-up"
                        data-aos-delay="200"
                    >
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/partner-2.png') }}"
                            alt="KIMEDIA"
                            class="max-w-full h-auto"
                        />
                    </div>

                    <!-- Brand 3 -->
                    <div
                        class="flex justify-center items-center p-4"
                        data-aos-lazy="fade-up"
                        data-aos-delay="300"
                    >
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/partner-3.png') }}"
                            alt="febudesign"
                            class="max-w-full h-auto"
                        />
                    </div>

                    <!-- Brand 4 -->
                    <div
                        class="flex justify-center items-center p-4"
                        data-aos-lazy="fade-up"
                        data-aos-delay="400"
                    >
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/partner-4.png') }}"
                            alt="leotra"
                            class="max-w-full h-auto"
                        />
                    </div>

                    <!-- Brand 5 -->
                    <div
                        class="flex justify-center items-center p-4"
                        data-aos-lazy="fade-up"
                        data-aos-delay="500"
                    >
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/partner-5.png') }}"
                            alt="YOGADEP"
                            class="max-w-full h-auto"
                        />
                    </div>

                    <!-- Brand 6 -->
                    <div
                        class="flex justify-center items-center p-4"
                        data-aos-lazy="fade-up"
                        data-aos-delay="600"
                    >
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/partner-6.png') }}"
                            alt="MSTs"
                            class="max-w-full h-auto"
                        />
                    </div>
                </div>

                <!-- Getcourse Partner Section -->
                <div class="mt-20 mb-12 text-center">
                    <h3
                        class="text-2xl font-bold text-gray-900 mb-2"
                        data-aos-lazy="fade-up"
                    >
                <span class="underline"
                >Mình cũng đang là đối tác và cũng đang là giảng viên
                </span>
                        <span class="font-normal"> của</span>
                    </h3>
                    <p class="text-xl text-gray-900" data-aos-lazy="fade-up">
                        <strong class="">Getcourse</strong> - một top công ty quốc tế hàng
                        đầu về giáo dục
                    </p>
                </div>

                <!-- Image Gallery -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Left: Big image -->
                    <div class="md:col-span-1" data-aos-lazy="zoom-out" data-aos-delay="100">
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/ev1.png') }}"
                            alt="Main image"
                            class="w-full h-full object-cover rounded-lg shadow-md duration-300 hover:scale-105"
                        />
                    </div>

                    <!-- Right: 2x2 grid -->
                    <div class="md:col-span-2 grid grid-cols-2 grid-rows-2 gap-4">
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/ev2.png') }}"
                            alt="Image 2"
                            class="w-full h-full object-cover rounded-lg shadow-md duration-300 hover:scale-105"
                            data-aos-lazy="zoom-out"
                            data-aos-delay="200"
                        />
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/ev3.png') }}"
                            alt="Image 3"
                            class="w-full h-full object-cover rounded-lg shadow-md duration-300 hover:scale-105"
                            data-aos-lazy="zoom-out"
                            data-aos-delay="300"
                        />
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/ev4.png') }}"
                            alt="Image 4"
                            class="w-full h-full object-cover rounded-lg shadow-md duration-300 hover:scale-105"
                            data-aos-lazy="zoom-out"
                            data-aos-delay="400"
                        />
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/ev5.png') }}"
                            alt="Image 5"
                            class="w-full h-full object-cover rounded-lg shadow-md duration-300 hover:scale-105"
                            data-aos-lazy="zoom-out"
                            data-aos-delay="500"
                        />
                    </div>
                </div>

                <!-- Speaker Section -->
                <div class="mt-10 text-center" data-aos-lazy="fade-up">
                <span class="text-xl text-gray-700 mb-3">
                Các sự kiện về kinh doanh giáo dục mình cũng là
                </span>
                    <strong class="text-2xl underline font-bold text-gray-900">
                        speaker chia <br/>
                        sẻ kinh nghiệm kinh doanh giáo dục
                    </strong>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-8">
                    <div data-aos-lazy="zoom-out" data-aos-delay="100">
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/workshop-1.png') }}" alt=""/>
                    </div>
                    <div data-aos-lazy="zoom-out" data-aos-delay="200">
                        <img src="{{ asset('assets/frontend/mst-academy/assets/images/workshop-2.png') }}" alt=""/>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-gradient-to-br from-[#051f4d] to-[#022e88] py-10">
        <div class="container mx-auto max-w-[1200px]">
            <!-- Header Section -->
            <div class="text-center pb-8 md:py-12 text-white">
                <h1 class="text-xl md:text-4xl font-bold mb-2" data-aos-lazy="fade-up">
                    Và những
                    <span class="text-yellow-500">quyền lợi độc quyền</span><br /> khi
                    bạn <span class="text-yellow-500">tham gia học lộ trình này.</span>
                </h1>
            </div>

            <!-- Benefits Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
                <!-- Benefit 1 -->
                <div data-aos-lazy="fade-right">
                    <div class="mb-2 w-full">
                        <div
                            class="relative text-white text-center mx-auto rounded-lg font-bold text-lg w-[90%]"
                        >
                            <img src="{{ asset('assets/frontend/mst-academy/assets/images/dx-title.png') }}" alt=""/>
                            <span
                                class="absolute top-3 md:top-6 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full uppercase text-sm md:text-md"
                            >THAM GIA CỘNG ĐỒNG KINH DOANH KHÓA HỌC</span
                            >
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-lg">
                        <!-- Header -->
                        <div class="relative">
                            <div class="pt-10 px-4">
                                <img
                                    src="{{ asset('assets/frontend/mst-academy/assets/images/anh_5.png') }}"
                                    alt="Cộng đồng kinh doanh"
                                    class="w-full h-auto rounded-lg border border-gray-200"
                                />
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="p-4 text-center">
                            <p class="text-gray-800 font-medium">
                                Cộng đồng riêng về kinh doanh khóa học/giáo dục sẽ được
                                support trực tiếp từ mình chứ không phải là ai khác
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Benefit 2 -->
                <div data-aos-lazy="fade-left">
                    <div class="mb-2 w-full">
                        <div
                            class="relative text-white text-center mx-auto rounded-lg font-bold text-lg w-[90%]"
                        >
                            <img src="{{ asset('assets/frontend/mst-academy/assets/images/dx-title.png') }}" alt=""/>
                            <span
                                class="absolute top-3 md:top-6 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full uppercase text-sm md:text-md"
                            >Nhóm Support 1-1 nếu bạn là học viên Pro</span
                            >
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-lg">
                        <!-- Header -->
                        <div class="relative">
                            <div class="pt-10 px-4">
                                <img
                                    loading="lazy"
                                    src="{{ asset('assets/frontend/mst-academy/assets/images/0430.gif') }}"
                                    alt="Nhóm support 1-1"
                                    class="w-full h-auto rounded-lg border border-gray-200"
                                />
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="p-4 text-center">
                            <p class="text-gray-800 font-medium">
                                Nhóm support 1-1 với học viên giúp bạn tạo ra được kết quả
                                nhanh chóng, đồng hành và hỗ trợ lâu dài
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Benefit 3 -->
                <div data-aos-lazy="fade-right">
                    <div class="mb-2 w-full">
                        <div
                            class="relative text-white text-center mx-auto rounded-lg font-bold text-lg w-[90%]"
                        >
                            <img src="{{ asset('assets/frontend/mst-academy/assets/images/dx-title.png') }}" alt=""/>
                            <span
                                class="absolute top-3 md:top-6 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full uppercase text-sm md:text-md"
                            >Tài khoản học online không giới hạn</span
                            >
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-lg">
                        <!-- Header -->
                        <div class="relative">
                            <div class="pt-10 px-4">
                                <img
                                    loading="lazy"
                                    src="{{ asset('assets/frontend/mst-academy/assets/images/gift_2.gif') }}"
                                    alt="Tài khoản học online"
                                    class="w-full h-auto rounded-lg border border-gray-200"
                                />
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="p-4 text-center">
                            <p class="text-gray-800 font-medium">
                                Học online trọn đời trên mọi thiết bị: điện thoại, máy tính
                                chỉ cần có kết nối internet không cần đến trung tâm tốn thời
                                gian
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Benefit 4 -->
                <div data-aos-lazy="fade-left">
                    <div class="mb-2 w-full">
                        <div
                            class="relative text-white text-center mx-auto rounded-lg font-bold text-lg w-[90%]"
                        >
                            <img src="{{ asset('assets/frontend/mst-academy/assets/images/dx-title.png') }}" alt=""/>
                            <span
                                class="absolute top-3 md:top-6 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full uppercase text-sm md:text-md"
                            >Lớp Zoom hỗ trợ tư vấn (Lớp pro)</span
                            >
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-lg">
                        <!-- Header -->
                        <div class="relative">
                            <div class="pt-10 px-4">
                                <img
                                    src="{{ asset('assets/frontend/mst-academy/assets/images/bn-4.png') }}"
                                    alt="Lớp Zoom hỗ trợ"
                                    class="w-full h-auto rounded-lg border border-gray-200"
                                />
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="p-4 text-center">
                            <p class="text-gray-800 font-medium">
                                Lớp học Zoom này sẽ học trực tiếp cho tất cả các bạn học viên
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bonus Gift Section -->
    <div class="py-8 max-w-[1020px] mx-auto relative">
        <img
            src="{{ asset('assets/frontend/mst-academy/assets/images/rounded-top.png') }}"
            alt=""
            class="w-full max-w-[500px] h-auto absolute -top-5 left-1/2 -translate-x-1/2"
        />
        <div class="text-center px-4 md:px-8">
            <h1
                class="text-3xl md:text-4xl font-bold text-gray-800 mb-2"
                data-aos-lazy="fade-up"
            >
                Bộ quà tặng
            </h1>
            <div class="flex justify-center items-center" data-aos-lazy="fade-up">
                <img src="{{ asset('assets/frontend/mst-academy/assets/images/price-title.png') }}" alt=""/>
                <span class="md:text-5xl font-bold txt-gra text-3xl font-black italic"
                >2.997.000đ</span
                >
            </div>
            <p
                class="text-xl md:text-3xl font-extrabold text-gray-800 mt-4"
                data-aos-lazy="fade-up"
            >
                dành riêng cho bạn khi đăng ký lộ trình này
            </p>
        </div>

        <!-- Gift Package Section -->
        <div class="!p-3 md:p-8">
            <!-- Gift 1 -->
            <div class="border border-blue-900 rounded-lg mb-8" data-aos-lazy="fade-up">
                <!-- Gift Header -->
                <div
                    class="bg-[#002084] text-white p-3 md:p-4 flex items-center justify-between relative"
                >
                    <div class="flex justify-center lg:justify-start gap-2 items-center flex-wrap md:flex-nowrap">
                        <div
                            class="bg-white text-[#002084] font-bold px-3 py-1 rounded-full text-sm md:text-base"
                        >
                            Quà tặng 1:
                        </div>
                        <h2 class="text-lg md:text-xl font-bold">
                            Khóa học quảng cáo Facebook từ Zero tới Hero
                        </h2>
                    </div>
                    <div class="text-yellow-300 text-2xl">★</div>
                    <div class="absolute -top-10 lg:top-[-50px] -right-10">
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/gift-course.png') }}"
                            alt="Khóa học Facebook"
                            class="w-[100px] lg:w-[150px] h-auto"
                        />
                    </div>
                </div>

                <!-- Gift Content -->
                <div class="p-4 md:p-6 flex flex-col md:flex-row gap-6">
                    <!-- Image -->
                    <div class="md:w-1/2 flex-shrink-0">
                        <div
                            class="relative rounded-lg overflow-hidden border border-gray-200"
                        >
                            <img
                                loading="lazy"
                                src="{{ asset('assets/frontend/mst-academy/assets/images/0813-20240813133503-afguc.gif') }}"
                                alt="Khóa học Facebook"
                                class="w-full h-auto"
                            />
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="md:w-1/2">
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Công thức phát triển Fanpage từ
                        <span class="font-bold">0 - 50.000 follower thật</span>
                        (không mua like)</span
                        >
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Chạy quảng cáo nhắn tin bán hàng</span
                        >
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Chạy quảng cáo website bán hàng</span
                        >
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        ><span class="font-bold"
                            >Cấu trúc quảng cáo 1-1-N, 1-N-1</span
                            >
                        để tự động tìm nội dung quảng cáo tốt nhất và khách hàng
                        chuẩn</span
                        >
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Công thức làm quảng cáo tiết kiệm chi phí</span
                        >
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Bí mật giúp mình bán hơn 30.000 đơn hàng trên
                        Facebook</span
                        >
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Price -->
                <div class="bg-blue-50 p-4 text-center">
                    <div class="flex items-center justify-center flex-wrap">
                        <span class="text-lg md:text-xl font-bold mr-2">Trị giá:</span>
                        <span
                            class="text-2xl md:text-3xl font-bold text-blue-600 line-through font-black"
                        >3.400.000 VNĐ</span
                        >
                        <span class="ml-2 text-gray-700 text-"
                        >(tặng kèm trong combo)</span
                        >
                    </div>
                </div>
            </div>
            <!-- Gift 2 -->
            <div class="border border-blue-900 rounded-lg mb-8" data-aos-lazy="fade-up">
                <!-- Gift Header -->
                <div
                    class="bg-[#002084] text-white p-3 md:p-4 flex items-center justify-between relative"
                >
                    <div class="flex justify-center lg:justify-start gap-2 items-center flex-wrap md:flex-nowrap">
                        <div
                            class="bg-white text-[#002084] font-bold px-3 py-1 rounded-full text-sm md:text-base"
                        >
                            Quà tặng 2:
                        </div>
                        <h2 class="text-lg md:text-xl font-bold">
                            24 file công việc/ kế hoạch Marketing
                        </h2>
                    </div>
                    <div class="text-yellow-300 text-2xl">★</div>
                    <div class="absolute -top-0 lg:top-[-50px] -right-10">
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/gift-course.png') }}"
                            alt="Khóa học Facebook"
                            class="w-[100px] lg:w-[150px] h-auto"
                        />
                    </div>
                </div>

                <!-- Gift Content -->
                <div class="p-4 md:p-6 flex flex-col md:flex-row gap-6">
                    <!-- Image -->
                    <div class="md:w-1/2 flex-shrink-0">
                        <div
                            class="relative rounded-lg overflow-hidden border border-gray-200"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/gift-khoa-hoc-2.png') }}"
                                alt="Khóa học Facebook"
                                class="w-full h-auto"
                            />
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="md:w-1/2">
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Cung cấp cho học viên
                        <span class="font-bold"
                        >24 bộ tài liệu Marketing và kinh doanh</span
                        ></span
                        >
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Đã được thiết lập sẵn, ứng dụng cho
                        <span class="font-bold"
                        >mọi kế hoạch Marketing, mọi ngành nghề</span
                        ></span
                        >
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Chỉ cần
                        <span class="font-bold"
                        >chỉnh sửa thay đổi theo dự án của bạn</span
                        >
                        là ứng dụng được luôn</span
                        >
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Price -->
                <div class="bg-blue-50 p-4 text-center">
                    <div class="flex items-center justify-center flex-wrap">
                        <span class="text-lg md:text-xl font-bold mr-2">Trị giá:</span>
                        <span
                            class="text-2xl md:text-3xl font-bold text-blue-600 line-through font-black"
                        >3.400.000 VNĐ</span
                        >
                        <span class="ml-2 text-gray-700 text-"
                        >(tặng kèm trong combo)</span
                        >
                    </div>
                </div>
            </div>
            <!-- Gift 3 -->
            <div class="border border-blue-900 rounded-lg mb-8" data-aos-lazy="fade-up">
                <!-- Gift Header -->
                <div
                    class="bg-[#002084] text-white p-3 md:p-4 flex items-center justify-between relative"
                >
                    <div class="flex justify-center lg:justify-start gap-2 items-center flex-wrap md:flex-nowrap">
                        <div
                            class="bg-white text-[#002084] font-bold px-3 py-1 rounded-full text-sm md:text-base"
                        >
                            Quà tặng 3:
                        </div>
                        <h2 class="text-lg md:text-xl font-bold">
                            1 tháng sử dụng phần mềm để xây dựng nền tảng E-learning
                        </h2>
                    </div>
                    <div class="text-yellow-300 text-2xl">★</div>
                    <div class="absolute -top-0 lg:top-[-50px] -right-10">
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/gift-course.png') }}"
                            alt="Khóa học Facebook"
                            class="w-[100px] lg:w-[150px] h-auto"
                        />
                    </div>
                </div>

                <!-- Gift Content -->
                <div class="p-4 md:p-6 flex flex-col md:flex-row gap-6">
                    <!-- Image -->
                    <div class="md:w-1/2 flex-shrink-0">
                        <div
                            class="relative rounded-lg overflow-hidden border border-gray-200"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/anh_3.png') }}"
                                alt="Khóa học Facebook"
                                class="w-full h-auto"
                            />
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="md:w-1/2">
                        <ul class="space-y-3">
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Tự tạy xây ra 1 trang website hay nền tảng E-learning của
                        riêng mình</span
                        >
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Để có thể đưa khóa học của bạn lên trực tuyến
                        <span class="font-bold"
                        >ngay lập tức trên Internet</span
                        ></span
                        >
                                </div>
                            </li>
                            <li class="flex items-start">
                                <div
                                    class="flex-shrink-0 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mt-0.5"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="h-4 w-4 text-white"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                                <div class="ml-3">
                        <span class="font-medium"
                        >Chỉ cần
                        <span class="font-bold"
                        >chỉnh sửa thay đổi theo dự án của bạn</span
                        >
                        là ứng dụng được luôn</span
                        >
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Price -->
                <div class="bg-blue-50 p-4 text-center">
                    <div class="flex items-center justify-center flex-wrap">
                        <span class="text-lg md:text-xl font-bold mr-2">Trị giá:</span>
                        <span
                            class="text-2xl md:text-3xl font-bold text-blue-600 line-through font-black"
                        >999.000 VNĐ</span
                        >
                        <span class="ml-2 text-gray-700 text-"
                        >(tặng kèm trong combo)</span
                        >
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pricing and Registration Section -->
    <div class="bg-gradient-to-br from-[#051f4d] to-[#022e88]">
        <div class="container mx-auto px-3 lg:px-4 py-8 max-w-6xl">
            <main
                class="flex-grow pt-8 md:px-8 lg:px-16 flex flex-col items-center justify-center"
            >
                <div class="max-w-6xl mx-auto text-center z-10">
                    <!-- Heading -->
                    <h1
                        class="text-2xl text-white md:text-3xl lg:text-4xl font-bold mb-6"
                        data-aos-lazy="fade-up"
                    >
                        Đăng Ký Ngay
                        <span class="text-yellow-400"
                        >Lộ Trình Khóa học E-Learning Master</span
                        >
                    </h1>

                    <!-- CTA Button -->
                    <div
                        class="inline-block font-black bg-white text-[#002084] font-bold p-4 rounded-full text-md md:text-xl lg:text-2xl mb-4"
                        data-aos-lazy="fade-up"
                    >
                        Để Nhận Trọn Bộ Quà Tặng Và Ưu Đãi
                    </div>

                    <!-- Subheading -->
                    <p class="text-lg md:text-xl mb-6 text-white" data-aos-lazy="fade-up">
                        Ưu đãi học phí sẽ luôn tăng khi đạt mốc học viên
                    </p>
                </div>

                <!-- Devices Display -->
                <div
                    class="devices relative w-full max-w-5xl mx-auto"
                    data-aos-lazy="zoom-in"
                >
                    <!-- Devices -->
                    <div class="flex justify-center">
                        <img
                            src="{{ asset('assets/frontend/mst-academy/assets/images/v-device.png') }}"
                            alt="Devices"
                            class="w-full h-auto"
                        />
                    </div>

                    <!-- Platform/Base -->
                    <div class="platform"></div>
                </div>
            </main>
            <div id="regis-section"
                 class="main-container pt-10 flex flex-wrap md:!flex-nowrap gap-8 justify-center items-center"
            >
                <!-- Left Section -->
                <div class="w-full md:w-1/2" data-aos-lazy="fade-right">
                    <!-- Pricing Section -->
                    <div class="relative">
                        <!-- Original Price -->
                        <div
                            class="bg-white text-[#002084] font-bold text-2xl py-2 px-6 rounded-full inline-block absolute top-[-30px] left-10 font-bold line-through text-[#002084] text-3xl font-black"
                        >
                            {{ $formatted_original_price }}đ
                        </div>

                        <!-- Discounted Price -->
                        <div
                            class="bg-[#5271ff] text-white rounded-full px-6 mt-2 flex items-end italic text-xl !pt-9 !pb-6 text-center justify-center"
                        >
                            <span class="mr-2 text-md lg:text-xl whitespace-nowrap">CHỈ CÒN:</span>
                            <div
                                class="border-0 text-center italic text-white text-[35px] [text-shadow:_1px_2px_1px_rgb(0_32_132_/_1.00)] font-bold py-0"
                            >

                                {{ $formatted_sale_price }}đ
                            </div>
                        </div>

                        <!-- Hot Deal Badge -->
                        <div
                            class="absolute right-0 top-[-30px] transform translate-x-1/4 -translate-y-1/4"
                        >
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/hot-deal.png') }}"
                                class="w-[100px] h-auto"
                                alt="Hot Deal"
                            />
                        </div>
                    </div>

                    <!-- Countdown Timer -->
                    <div class="bg-white rounded-lg px-4 py-2 table mx-auto w-fit">
                        <div
                            class="flex justify-center space-x-4 countdown-container"
                            data-target-date="2025-06-03"
                        >
                            <div class="countdown-item">
                                <div
                                    class="bg-gradient-to-b from-[#032cab] to-[#002084] rounded-lg text-white text-4xl font-bold w-16 h-16 flex items-center justify-center days-count"
                                >
                                    00
                                </div>
                                <div class="text-center text-sm mt-1">NGÀY</div>
                            </div>
                            <div class="countdown-item">
                                <div
                                    class="bg-gradient-to-b from-[#032cab] to-[#002084] rounded-lg text-white text-4xl font-bold w-16 h-16 flex items-center justify-center hours-count"
                                >
                                    00
                                </div>
                                <div class="text-center text-sm mt-1">GIỜ</div>
                            </div>
                            <div class="countdown-item">
                                <div
                                    class="bg-gradient-to-b from-[#032cab] to-[#002084] rounded-lg text-white text-4xl font-bold w-16 h-16 flex items-center justify-center minutes-count"
                                >
                                    00
                                </div>
                                <div class="text-center text-sm mt-1">PHÚT</div>
                            </div>
                            <div class="countdown-item">
                                <div
                                    class="bg-gradient-to-b from-[#032cab] to-[#002084] rounded-lg text-white text-4xl font-bold w-16 h-16 flex items-center justify-center seconds-count"
                                >
                                    00
                                </div>
                                <div class="text-center text-sm mt-1">GIÂY</div>
                            </div>
                        </div>
                    </div>

                    <!-- Features List -->
                    <div class="text-white space-y-3 my-5">
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2 mt-1">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd"
                                    />
                                </svg>
                            </div>
                            <div class="text-lg">Khóa học E-learning Master</div>
                        </div>
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2 mt-1">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd"
                                    />
                                </svg>
                            </div>
                            <div class="text-lg">
                                Cộng đồng >1000 người về kd khóa học
                                <span class="text-yellow-400 whitespace-nowrap">(Trị giá: 999.000đ)</span>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2 mt-1">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                >
                                    <path
                                        fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd"
                                    />
                                </svg>
                            </div>
                            <div class="text-lg">Buổi 1-1 về Tư vấn định hướng</div>
                        </div>
                    </div>

                    <!-- Gift Section -->
                    <div
                        class="border border-dashed border-blue-400 rounded-lg !p-4 lg:p-6 space-y-4 bg-[#002084]"
                    >
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="26"
                                    height="26"
                                    viewBox="0 0 26 26"
                                    fill="none"
                                >
                                    <path
                                        d="M14.9643 8.48232H11.034C9.40619 8.48232 6.95822 7.10583 6.05425 6.19554C4.78207 4.91387 4.78207 2.82622 6.05583 1.54455C7.29325 0.29923 9.45202 0.29923 10.691 1.54455C11.3753 2.23359 13.1927 5.0324 12.943 6.90038H13.0552C12.8055 5.0324 14.623 2.23359 15.3088 1.54455C16.5447 0.29923 18.7034 0.29607 19.944 1.54455C21.2194 2.82622 21.2194 4.91229 19.944 6.19396C19.04 7.10425 16.5921 8.48232 14.9643 8.48232ZM14.9643 6.90196C16.0563 6.90196 18.1424 5.76727 18.822 5.08139C19.4857 4.41448 19.4857 3.3272 18.822 2.66028C18.1803 2.01392 17.0662 2.01708 16.4277 2.66028C15.3626 3.73177 14.3037 6.46895 14.6719 6.84981C14.6735 6.84981 14.7399 6.90196 14.9643 6.90196ZM8.37422 2.16089C7.92224 2.16089 7.49712 2.33789 7.17631 2.66028C6.51414 3.3272 6.51414 4.41448 7.17631 5.08139C7.85744 5.76727 9.94193 6.90196 11.034 6.90196C11.26 6.90196 11.3263 6.85139 11.3263 6.85139C11.6945 6.46737 10.6357 3.73019 9.57055 2.66028C9.24974 2.33789 8.82462 2.16089 8.37422 2.16089Z"
                                        fill="#F44336"
                                    />
                                    <path
                                        d="M2.96323 12.2695V22.3063C2.96323 23.0978 3.60701 23.7401 4.39705 23.7401H21.6029C22.3944 23.7401 23.0368 23.0978 23.0368 22.3063V12.2695H2.96323Z"
                                        fill="#FFC107"
                                    />
                                    <path
                                        d="M23.0368 7.96875H2.96324C2.17321 7.96875 1.52942 8.61254 1.52942 9.40257V12.9871C1.52942 13.3829 1.8506 13.704 2.24633 13.704H23.7537C24.1494 13.704 24.4706 13.3829 24.4706 12.9871V9.40257C24.4706 8.6111 23.8282 7.96875 23.0368 7.96875Z"
                                        fill="#FFD54F"
                                    />
                                    <path
                                        d="M15.6112 7.96875H10.3888V23.7408H15.6112V7.96875Z"
                                        fill="#F44336"
                                    />
                                </svg>
                            </div>
                            <div class="text-white">
                                <span class="font-bold">Quà tặng 1:</span> Khóa học quảng cáo
                                Facebook
                                <span class="text-yellow-400 whitespace-nowrap">(Trị giá: 999.000)</span>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="26"
                                    height="26"
                                    viewBox="0 0 26 26"
                                    fill="none"
                                >
                                    <path
                                        d="M14.9643 8.48232H11.034C9.40619 8.48232 6.95822 7.10583 6.05425 6.19554C4.78207 4.91387 4.78207 2.82622 6.05583 1.54455C7.29325 0.29923 9.45202 0.29923 10.691 1.54455C11.3753 2.23359 13.1927 5.0324 12.943 6.90038H13.0552C12.8055 5.0324 14.623 2.23359 15.3088 1.54455C16.5447 0.29923 18.7034 0.29607 19.944 1.54455C21.2194 2.82622 21.2194 4.91229 19.944 6.19396C19.04 7.10425 16.5921 8.48232 14.9643 8.48232ZM14.9643 6.90196C16.0563 6.90196 18.1424 5.76727 18.822 5.08139C19.4857 4.41448 19.4857 3.3272 18.822 2.66028C18.1803 2.01392 17.0662 2.01708 16.4277 2.66028C15.3626 3.73177 14.3037 6.46895 14.6719 6.84981C14.6735 6.84981 14.7399 6.90196 14.9643 6.90196ZM8.37422 2.16089C7.92224 2.16089 7.49712 2.33789 7.17631 2.66028C6.51414 3.3272 6.51414 4.41448 7.17631 5.08139C7.85744 5.76727 9.94193 6.90196 11.034 6.90196C11.26 6.90196 11.3263 6.85139 11.3263 6.85139C11.6945 6.46737 10.6357 3.73019 9.57055 2.66028C9.24974 2.33789 8.82462 2.16089 8.37422 2.16089Z"
                                        fill="#F44336"
                                    />
                                    <path
                                        d="M2.96323 12.2695V22.3063C2.96323 23.0978 3.60701 23.7401 4.39705 23.7401H21.6029C22.3944 23.7401 23.0368 23.0978 23.0368 22.3063V12.2695H2.96323Z"
                                        fill="#FFC107"
                                    />
                                    <path
                                        d="M23.0368 7.96875H2.96324C2.17321 7.96875 1.52942 8.61254 1.52942 9.40257V12.9871C1.52942 13.3829 1.8506 13.704 2.24633 13.704H23.7537C24.1494 13.704 24.4706 13.3829 24.4706 12.9871V9.40257C24.4706 8.6111 23.8282 7.96875 23.0368 7.96875Z"
                                        fill="#FFD54F"
                                    />
                                    <path
                                        d="M15.6112 7.96875H10.3888V23.7408H15.6112V7.96875Z"
                                        fill="#F44336"
                                    />
                                </svg>
                            </div>
                            <div class="text-white">
                                <span class="font-bold">Quà tặng 2:</span> 24 file công việc/
                                kế hoạch Marketing
                                <span class="text-yellow-400 whitespace-nowrap">(Trị giá: 999.000)</span>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="text-yellow-400 mr-2">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="26"
                                    height="26"
                                    viewBox="0 0 26 26"
                                    fill="none"
                                >
                                    <path
                                        d="M14.9643 8.48232H11.034C9.40619 8.48232 6.95822 7.10583 6.05425 6.19554C4.78207 4.91387 4.78207 2.82622 6.05583 1.54455C7.29325 0.29923 9.45202 0.29923 10.691 1.54455C11.3753 2.23359 13.1927 5.0324 12.943 6.90038H13.0552C12.8055 5.0324 14.623 2.23359 15.3088 1.54455C16.5447 0.29923 18.7034 0.29607 19.944 1.54455C21.2194 2.82622 21.2194 4.91229 19.944 6.19396C19.04 7.10425 16.5921 8.48232 14.9643 8.48232ZM14.9643 6.90196C16.0563 6.90196 18.1424 5.76727 18.822 5.08139C19.4857 4.41448 19.4857 3.3272 18.822 2.66028C18.1803 2.01392 17.0662 2.01708 16.4277 2.66028C15.3626 3.73177 14.3037 6.46895 14.6719 6.84981C14.6735 6.84981 14.7399 6.90196 14.9643 6.90196ZM8.37422 2.16089C7.92224 2.16089 7.49712 2.33789 7.17631 2.66028C6.51414 3.3272 6.51414 4.41448 7.17631 5.08139C7.85744 5.76727 9.94193 6.90196 11.034 6.90196C11.26 6.90196 11.3263 6.85139 11.3263 6.85139C11.6945 6.46737 10.6357 3.73019 9.57055 2.66028C9.24974 2.33789 8.82462 2.16089 8.37422 2.16089Z"
                                        fill="#F44336"
                                    />
                                    <path
                                        d="M2.96323 12.2695V22.3063C2.96323 23.0978 3.60701 23.7401 4.39705 23.7401H21.6029C22.3944 23.7401 23.0368 23.0978 23.0368 22.3063V12.2695H2.96323Z"
                                        fill="#FFC107"
                                    />
                                    <path
                                        d="M23.0368 7.96875H2.96324C2.17321 7.96875 1.52942 8.61254 1.52942 9.40257V12.9871C1.52942 13.3829 1.8506 13.704 2.24633 13.704H23.7537C24.1494 13.704 24.4706 13.3829 24.4706 12.9871V9.40257C24.4706 8.6111 23.8282 7.96875 23.0368 7.96875Z"
                                        fill="#FFD54F"
                                    />
                                    <path
                                        d="M15.6112 7.96875H10.3888V23.7408H15.6112V7.96875Z"
                                        fill="#F44336"
                                    />
                                </svg>
                            </div>
                            <div class="text-white">
                                <span class="font-bold">Quà tặng 3:</span> Trải nghiệm phần
                                mềm tạo trang E-learning
                                <span class="text-yellow-400 whitespace-nowrap">(Trị giá: 999.000)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Section - Registration Form -->
                <div class="w-full md:w-1/2 bg-white rounded-lg" data-aos-lazy="fade-left">
                    <div
                        class="text-center bg-gradient-to-b from-white to-[#fff3e8] p-6 rounded-tl-2xl rounded-tr-2xl"
                    >
                        <h2 class="text-lg lg:text-2xl font-bold text-[#051c45]">
                            Lộ trình khóa học
                            <span class="text-blue-600">E-learning Master</span>
                        </h2>
                        <p class="text-gray-700">Đăng ký nhận ưu đãi ngay!</p>
                        <div class="flex justify-center mt-4">
                            <img
                                src="{{ asset('assets/frontend/mst-academy/assets/images/arrow-bottom.png') }}"
                                alt="Funnel"
                                class="w-[40px] h-auto  animate-bounce"
                                data-aos-lazy="fade-down"
                                data-aos-delay="300"
                                data-aos-duration="1000"
                                data-aos-repeat="true"
                            />
                        </div>
                    </div>

                    <div class="p-6 rounded-2xl">
                        <div class="rounded-lg border border-yellow-400 p-2 lg:p-4">
                            <form class="space-y-4" id="registration-form">

                                @if(Auth()->check())
                                    <div id="success-message">
                                        <div class="flex flex-col items-center justify-center py-8 text-center">
                                            <img class="mx-auto h-auto"
                                                 src="{{ asset('assets/frontend/mst-academy/assets/images/regis-success.png') }}"
                                                 alt="Đăng ký thành công">

                                            <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                                               id="redirect-link"
                                               class="flex items-center justify-center gap-2 w-full bg-gradient-to-r from-yellow-500 to-red-500 text-white font-bold py-3 px-4 rounded-md text-xl hover:from-yellow-600 hover:to-red-600 transition duration-300 mt-4"
                                            >
                                                Vào học ngay
                                                <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35"
                                                     viewBox="0 0 35 35" fill="none">
                                                    <path
                                                        d="M15.1619 9.53757C14.9622 9.20404 14.5909 9.01257 14.2032 9.04203L5.87063 9.6857C5.02702 9.75091 4.27555 10.1931 3.8088 10.8989L0.311899 16.188C-0.0419945 16.7233 -0.0983222 17.3932 0.161305 17.98C0.420863 18.5667 0.954404 18.9756 1.58843 19.0738L8.34502 20.1203C8.39759 20.1285 8.45009 20.1325 8.50218 20.1325C8.84828 20.1324 9.17572 19.957 9.36596 19.6593L15.1462 10.6166C15.3556 10.289 15.3616 9.87116 15.1619 9.53757Z"
                                                        fill="#FFA700"/>
                                                    <path
                                                        d="M25.4621 19.839C25.1285 19.6393 24.7106 19.6454 24.383 19.8548L15.3404 25.635C14.998 25.8539 14.8171 26.2543 14.8793 26.6559L15.9257 33.4125C16.024 34.0466 16.4329 34.5801 17.0197 34.8397C17.2638 34.9476 17.5222 35.0009 17.7793 35.0009C18.1403 35.0009 18.499 34.8957 18.8116 34.6891L24.1008 31.1921C24.8066 30.7255 25.2488 29.974 25.3139 29.1303L25.9576 20.7978C25.9876 20.4101 25.7956 20.0388 25.4621 19.839Z"
                                                        fill="#F7B84C"/>
                                                    <path
                                                        d="M13.1844 28.1829C13.5925 28.591 14.1724 28.7819 14.7416 28.6867C23.089 27.2909 29.6945 20.3796 32.2092 15.5177C35.0016 10.1191 35.105 4.41415 34.9591 1.73859C34.909 0.821761 34.1782 0.0909361 33.2614 0.0408974C30.5858 -0.10498 24.8809 -0.0015534 19.4823 2.79084C14.6205 5.30555 7.70913 11.911 6.31338 20.2584C6.21822 20.8277 6.40908 21.4075 6.81718 21.8157L13.1844 28.1829Z"
                                                        fill="#F1F1FB"/>
                                                    <path
                                                        d="M34.4353 0.564453L10.0007 24.999L13.1843 28.1826C13.5924 28.5907 14.1723 28.7816 14.7416 28.6864C23.089 27.2906 29.6944 20.3793 32.2092 15.5175C35.0015 10.1189 35.105 4.41394 34.959 1.73838C34.934 1.27997 34.7388 0.868035 34.4353 0.564453Z"
                                                        fill="#D7D6FB"/>
                                                    <path
                                                        d="M23.9756 16.1494C22.6625 16.1495 21.35 15.6498 20.3503 14.6502C19.382 13.6819 18.8486 12.3943 18.8486 11.0249C18.8486 9.65547 19.382 8.36793 20.3503 7.39963C22.3492 5.40068 25.6018 5.40061 27.6008 7.39963C28.5692 8.36793 29.1025 9.65547 29.1025 11.0249C29.1025 12.3943 28.5692 13.6819 27.6008 14.6502C26.6015 15.6496 25.2883 16.1493 23.9756 16.1494Z"
                                                        fill="#466288"/>
                                                    <path
                                                        d="M6.31338 20.2593C6.21822 20.8285 6.40908 21.4084 6.81718 21.8165L13.1844 28.1837C13.5925 28.5918 14.1724 28.7826 14.7416 28.6875C16.558 28.3838 18.2917 27.8187 19.9189 27.0707L7.9302 15.082C7.18215 16.7092 6.61709 18.443 6.31338 20.2593Z"
                                                        fill="#466288"/>
                                                    <path
                                                        d="M1.04611 28.8439C1.30847 28.8439 1.57097 28.7437 1.77113 28.5435L5.11889 25.1958C5.51934 24.7954 5.51934 24.1461 5.11889 23.7456C4.71851 23.3452 4.0693 23.3452 3.66878 23.7456L0.321089 27.0934C-0.0793604 27.4938 -0.0793604 28.1431 0.321089 28.5435C0.521313 28.7438 0.783677 28.8439 1.04611 28.8439Z"
                                                        fill="white"/>
                                                    <path
                                                        d="M8.187 26.814C7.78662 26.4136 7.13734 26.4136 6.73689 26.814L0.300581 33.2503C-0.0998682 33.6506 -0.0998682 34.2999 0.300581 34.7004C0.500806 34.9006 0.763237 35.0007 1.02567 35.0007C1.2881 35.0007 1.55053 34.9005 1.75069 34.7003L8.187 28.264C8.58745 27.8636 8.58745 27.2143 8.187 26.814Z"
                                                        fill="white"/>
                                                    <path
                                                        d="M11.255 29.8823C10.8546 29.4819 10.2053 29.4819 9.80489 29.8823L6.4572 33.23C6.05675 33.6304 6.05675 34.2797 6.4572 34.6801C6.65742 34.8803 6.91985 34.9805 7.18222 34.9805C7.44458 34.9805 7.70708 34.8803 7.90724 34.6801L11.255 31.3324C11.6554 30.932 11.6554 30.2827 11.255 29.8823Z"
                                                        fill="white"/>
                                                    <path
                                                        d="M0.300537 34.699C0.500762 34.8992 0.763193 34.9993 1.02562 34.9993C1.28806 34.9993 1.55049 34.8991 1.75064 34.6989L8.18695 28.2626C8.5874 27.8622 8.5874 27.2129 8.18695 26.8125L0.300537 34.699Z"
                                                        fill="white"/>
                                                    <path
                                                        d="M10.0007 24.9979L13.1843 28.1816C13.5924 28.5897 14.1723 28.7805 14.7416 28.6854C16.5579 28.3816 18.2916 27.8166 19.9188 27.0685L13.9245 21.0742L10.0007 24.9979Z"
                                                        fill="#354A67"/>
                                                    <path
                                                        d="M20.3503 14.649C21.35 15.6486 22.6625 16.1484 23.9756 16.1482C25.2883 16.1481 26.6015 15.6483 27.6008 14.649C28.5692 13.6806 29.1025 12.3931 29.1025 11.0237C29.1025 9.65428 28.5692 8.36674 27.6008 7.39844L20.3503 14.649Z"
                                                        fill="#354A67"/>
                                                    <path
                                                        d="M23.9756 7.94922C23.1879 7.94922 22.4001 8.24904 21.8005 8.84876C21.2195 9.42975 20.8994 10.2022 20.8994 11.0239C20.8994 11.8456 21.2195 12.618 21.8005 13.199C22.9998 14.3984 24.9515 14.3984 26.1508 13.199C26.7318 12.618 27.0518 11.8455 27.0518 11.0238C27.0518 10.2021 26.7318 9.42968 26.1508 8.84869C25.5511 8.24904 24.7634 7.94922 23.9756 7.94922Z"
                                                        fill="#4BBEFD"/>
                                                    <path
                                                        d="M26.1506 8.84961L21.8003 13.1999C22.9997 14.3993 24.9513 14.3994 26.1506 13.1999C26.7316 12.6189 27.0516 11.8464 27.0516 11.0247C27.0516 10.2031 26.7317 9.4306 26.1506 8.84961Z"
                                                        fill="#0590FB"/>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                @else
                                    <div id="form-content" class="flex flex-col gap-3">
                                        <div class="relative">
                                            <label
                                                class="block text-sm text-gray-700 bg-white absolute -top-1 left-3 z-10"
                                            >Họ và tên *:</label
                                            >
                                            <input
                                                type="text"
                                                name="user_name"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                required
                                            />
                                        </div>
                                        <div class="relative">
                                            <label
                                                class="block text-sm text-gray-700 bg-white absolute -top-1 left-3 z-10"
                                            >Email *:</label
                                            >
                                            <input
                                                type="email"
                                                name="user_email"
                                                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                required
                                            />
                                        <div class="email-error-message text-red-500 text-sm mt-1 hidden"></div>
                                        </div>
                                        <div class="relative">
                                            <label
                                                class="block text-sm text-gray-700 bg-white absolute -top-1 left-3 z-10"
                                            >Tạo mật khẩu *:</label
                                            >
                                            <div class="relative">
                                                <input
                                                    type="password"
                                                    name="user_password"
                                                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                    required
                                                />
                                                <div
                                                    class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer toggle-password"
                                                >
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        class="h-5 w-5 text-gray-400"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                        stroke="currentColor"
                                                    >
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                                        />
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                                                        />
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="password-error-message text-red-500 text-sm mt-1 hidden"></div>
                                        </div>
                                        <div>
                                            <label class="block text-sm text-gray-700"
                                            >Số điện thoại *:</label
                                            >
                                            <div class="flex">
                                                <div
                                                    class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500"
                                                >
                                                    <img
                                                        src="https://upload.wikimedia.org/wikipedia/commons/2/21/Flag_of_Vietnam.svg"
                                                        alt="Vietnam flag"
                                                        class="h-4 w-6 mr-1"
                                                    />
                                                    +84
                                                </div>
                                                <input
                                                    type="tel"
                                                    name="user_phone"
                                                    class="flex-1 block w-full border border-gray-300 rounded-r-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                    required
                                                />
                                            </div>
                                        </div>

                                        <button
                                            type="submit"
                                            id="submit-btn"
                                            class="w-full bg-gradient-to-r from-yellow-500 to-red-500 text-white font-bold py-3 px-4 rounded-md text-xl hover:from-yellow-600 hover:to-red-600 transition duration-300 mt-4"
                                        >
                                            ĐĂNG KÝ NGAY
                                        </button>

                                        {{--                                    <div class="text-center text-sm text-gray-600 mt-4">--}}
                                        {{--                                        Bạn đã có tài khoản?--}}
                                        {{--                                        <a href="#" class="text-orange-500 hover:underline"--}}
                                        {{--                                        >Ấn vào đây để đăng nhập</a--}}
                                        {{--                                        >--}}
                                        {{--                                    </div>--}}
                                    </div>

                                    <div id="loading" class="hidden">
                                        <div class="flex flex-col items-center justify-center py-8">
                                            <div
                                                class="w-16 h-16 border-4 border-t-orange-500 border-b-orange-500 border-l-gray-200 border-r-gray-200 rounded-full animate-spin"></div>
                                            <p class="mt-4 text-gray-700 font-medium">Đang xử lý...</p>
                                        </div>
                                    </div>
                                @endif
                                <div id="error-message" class="hidden">
                                    <div
                                        class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
                                        role="alert">
                                        <strong class="font-bold">Lỗi đăng ký!</strong>
                                        <span
                                            class="block sm:inline error-text">Đã có lỗi xảy ra. Vui lòng thử lại sau.</span>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer class="bg-white py-10 border-t border-gray-200">
        <div class="container mx-auto max-w-6xl px-4">
            <div class="flex flex-col md:flex-row gap-8">
                <!-- Left Side - Company Info -->
                <div class="w-full md:w-1/2 space-y-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">
                            MSTs Academy - Nền Tảng Học Tập Toàn Diện
                        </h2>
                        <h3 class="text-xl font-bold text-gray-800">
                            Dành Cho Người Đi Làm
                        </h3>
                        <p class="text-lg font-bold text-gray-700 mt-2">
                            SẢN PHẨM THUỘC CÔNG TY TNHH KIMEDIA
                        </p>
                    </div>

                    <div class="space-y-3">
                        <!-- Hotline -->
                        <div class="flex items-center">
                            <div
                                class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-3"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                    />
                                </svg>
                            </div>
                            <span class="text-gray-700">Hotline: 084 684 3456</span>
                        </div>

                        <!-- Address -->
                        <div class="flex items-center">
                            <div
                                class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-3"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                                    />
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                                    />
                                </svg>
                            </div>
                            <span class="text-gray-700"
                            >Address: 33 ngõ 41 Thái Hà, Hà Nội</span
                            >
                        </div>

                        <!-- Second Hotline -->
                        <div class="flex items-center">
                            <div
                                class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-3"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                    />
                                </svg>
                            </div>
                            <span class="text-gray-700">Hotline: 0974.161.005</span>
                        </div>

                        <!-- Website -->
                        <div class="flex items-center">
                            <div
                                class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-3"
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-4 w-4 text-white"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
                                    />
                                </svg>
                            </div>
                            <span class="text-gray-700"
                            >Website:
                    <a
                        href="https://www.mstacademy.vn/"
                        class="text-blue-600 hover:underline"
                    >https://www.mstacademy.vn/</a
                    ></span
                            >
                        </div>
                    </div>

                    <!-- Social Media Icons -->
{{--                    <div class="flex space-x-4 pt-2">--}}
{{--                        <!-- Facebook -->--}}
{{--                        <a--}}
{{--                            href="#"--}}
{{--                            class="social-icon w-10 h-10 rounded-full bg-blue-800 flex items-center justify-center"--}}
{{--                        >--}}
{{--                            <svg--}}
{{--                                xmlns="http://www.w3.org/2000/svg"--}}
{{--                                class="h-5 w-5 text-white"--}}
{{--                                fill="currentColor"--}}
{{--                                viewBox="0 0 24 24"--}}
{{--                            >--}}
{{--                                <path--}}
{{--                                    d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"--}}
{{--                                />--}}
{{--                            </svg>--}}
{{--                        </a>--}}

{{--                        <!-- TikTok -->--}}
{{--                        <a--}}
{{--                            href="#"--}}
{{--                            class="social-icon w-10 h-10 rounded-full bg-black flex items-center justify-center"--}}
{{--                        >--}}
{{--                            <svg--}}
{{--                                xmlns="http://www.w3.org/2000/svg"--}}
{{--                                class="h-5 w-5 text-white"--}}
{{--                                fill="currentColor"--}}
{{--                                viewBox="0 0 24 24"--}}
{{--                            >--}}
{{--                                <path--}}
{{--                                    d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"--}}
{{--                                />--}}
{{--                            </svg>--}}
{{--                        </a>--}}

{{--                        <!-- YouTube -->--}}
{{--                        <a--}}
{{--                            href="#"--}}
{{--                            class="social-icon w-10 h-10 rounded-full bg-red-600 flex items-center justify-center"--}}
{{--                        >--}}
{{--                            <svg--}}
{{--                                xmlns="http://www.w3.org/2000/svg"--}}
{{--                                class="h-5 w-5 text-white"--}}
{{--                                fill="currentColor"--}}
{{--                                viewBox="0 0 24 24"--}}
{{--                            >--}}
{{--                                <path--}}
{{--                                    d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"--}}
{{--                                />--}}
{{--                            </svg>--}}
{{--                        </a>--}}

{{--                        <!-- Instagram -->--}}
{{--                        <a--}}
{{--                            href="#"--}}
{{--                            class="social-icon w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 via-pink-500 to-yellow-500 flex items-center justify-center"--}}
{{--                        >--}}
{{--                            <svg--}}
{{--                                xmlns="http://www.w3.org/2000/svg"--}}
{{--                                class="h-5 w-5 text-white"--}}
{{--                                fill="currentColor"--}}
{{--                                viewBox="0 0 24 24"--}}
{{--                            >--}}
{{--                                <path--}}
{{--                                    d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"--}}
{{--                                />--}}
{{--                            </svg>--}}
{{--                        </a>--}}
{{--                    </div>--}}
                </div>

                <!-- Right Side - Google Map -->
                <div class="w-full md:w-1/2 h-80 md:h-auto relative">
                    <div
                        class="absolute top-0 left-0 bg-white px-3 py-1 shadow-md z-10 rounded-br-md"
                    >
                        <div class="flex items-center">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-4 w-4 text-red-500 mr-1"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                                />
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                                />
                            </svg>
                            <span class="text-sm font-medium"
                            >33 Ngõ 41 P Thái Hà, Trung Liệt, Đống Đa, Hà Nội</span
                            >
                        </div>
                        <div
                            class="text-xs text-blue-600 mt-1 hover:underline cursor-pointer"
                        >
                            Xem bản đồ lớn hơn
                        </div>
                    </div>

                    <!-- Google Map iframe -->
                    <iframe loading="lazy"
                            data-src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3724.6963246511!2d105.81764081476292!3d21.007905986010126!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ac828a08b8c7%3A0xd0905b6adcd3b3b1!2zMzMgTmfDtSA0MSBQLiBUaMOhaSBIw6AsIMSQ4buRbmcgxJBhLCBIw6AgTuG7mWksIFZp4buHdCBOYW0!5e0!3m2!1svi!2s!4v1651234567890!5m2!1svi!2s"
                            class="w-full h-80 md:h-full border-0 rounded-lg shadow-md"
                            allowfullscreen=""
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade"
                    >
                    </iframe>
                </div>
            </div>
        </div>
    </footer>
</div>
@if (get_frontend_settings('recaptcha_status'))
    @push('js')
        <script
            src="https://www.google.com/recaptcha/api.js?render={{ get_frontend_settings('recaptcha_sitekey') }}"></script>
    @endpush
@endif

@push('js')
    <script src="{{ asset('assets/frontend/mst-academy/assets/libs/aos/aos-*******-beta.6.js') }}"></script>
    <script>
        AOS.init({
            once: true,
            duration: 650,
        });

        $(document).ready(function() {
            // $('[data-src]').each(function(ifram) {
            //    $(this).attr("src", $(this).data('src'))
            // })
        })


        $(window).on("load", function () {

            // aos lazy
            $('[data-aos-lazy]').each(function name(i, element) {
                $(element).attr("data-aos", $(element).data("aos-lazy"))
            })
            AOS.refreshHard()

            // iframe lazy
            $('[data-src]').each(function(ifram) {
               $(this).attr("src", $(this).data('src'))
            })
        })
    </script>
@endpush
<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Update countdown every second
        function updateCountdown() {
            const currentDate = new Date();

            // Tạo một ngày mới ở thời điểm 00:00 của ngày hôm sau
            const tomorrow = new Date(currentDate);
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);

            // Tính thời gian còn lại
            const difference = tomorrow - currentDate;

            // Calculate remaining time
            const days = Math.floor(difference / (1000 * 60 * 60 * 24));
            const hours = Math.floor(
                (difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
            );
            const minutes = Math.floor(
                (difference % (1000 * 60 * 60)) / (1000 * 60)
            );
            const seconds = Math.floor((difference % (1000 * 60)) / 1000);

            // Format numbers to always display with two digits
            document.querySelector(".days-count").textContent = days
                .toString()
                .padStart(2, "0");
            document.querySelector(".hours-count").textContent = hours
                .toString()
                .padStart(2, "0");
            document.querySelector(".minutes-count").textContent = minutes
                .toString()
                .padStart(2, "0");
            document.querySelector(".seconds-count").textContent = seconds
                .toString()
                .padStart(2, "0");
        }

        // Update the countdown immediately and then every second
        updateCountdown();
        setInterval(updateCountdown, 1000);
    });
</script>

<script>
    // Generic function to initialize all YouTube players
    document.addEventListener("DOMContentLoaded", function () {
        const videoContainers = document.querySelectorAll(
            ".youtube-video-container"
        );

        videoContainers.forEach((container) => {
            const videoSrc = container.getAttribute("data-video-src");
            const posterWrapper = container.querySelector(".poster-wrapper");
            const iframeContainer = container.querySelector(".iframe-container");

            // Make the entire poster wrapper clickable
            posterWrapper.addEventListener("click", function () {
                // Hide poster
                posterWrapper.style.display = "none";

                // Show and populate iframe
                iframeContainer.classList.remove("hidden");

                // Create and add iframe
                const iframe = document.createElement("iframe");
                iframe.src = videoSrc;
                iframe.className = "absolute inset-0 w-full h-full";
                iframe.setAttribute("frameborder", "0");
                iframe.setAttribute("allowfullscreen", "");
                iframe.setAttribute(
                    "allow",
                    "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                );

                iframeContainer.appendChild(iframe);
            });
        });
    });
</script>
<script>
    $(document).ready(function () {
        // Toggle password visibility
        $('.toggle-password').click(function () {
            var passwordInput = $(this).parent().find('input');
            var type = passwordInput.attr('type') === 'password' ? 'text' : 'password';
            passwordInput.attr('type', type);
        });

        // Password validation function
        function validatePassword(password) {
            if (password && password.length < 8) {
                return 'Mật khẩu phải có ít nhất 8 kí tự';
            }
            return null;
        }
        // Email validation function
        function validateEmail(email) {
            // Regular expression for basic email validation
            var emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
            if (email && !emailPattern.test(email)) {
                return 'Email không hợp lệ';
            }
            return null;
        }

        // Validate password on input
        $('input[name="user_password"]').on('input', function() {
            var password = $(this).val();
            var errorMessage = validatePassword(password);

            if (errorMessage) {
                $('.password-error-message').text(errorMessage).removeClass('hidden');

            } else {
                $('.password-error-message').addClass('hidden');
            }
        });
        $('input[name="user_email"]').on('input', function() {
            var email = $(this).val();
            var errorMessage = validateEmail(email);

            if (errorMessage) {
                $('.email-error-message').text(errorMessage).removeClass('hidden');
            } else {
                $('.email-error-message').addClass('hidden');
            }
        });

        // Handle form submission
        $('#registration-form').submit(function (e) {
            try {
                e.preventDefault();

                // Validate password before submission
                var password = $('input[name="user_password"]').val();
                var passwordError = validatePassword(password);

                if (passwordError) {
                    $('.password-error-message').text(passwordError).removeClass('hidden');
                    return false; // Prevent form submission
                }
                var email = $('input[name="user_email"]').val();
                var emailError = validateEmail(email);

                if (emailError) {
                    $('.email-error-message').text(emailError).removeClass('hidden');
                    return false; // Prevent form submission
                }

                // Show loading
                $('#form-content').addClass('hidden');
                $('#loading').removeClass('hidden');

                // Get form data
                var formData = {
                    name: $('input[name="user_name"]').val(),
                    phone: $('input[name="user_phone"]').val(),
                    email: $('input[name="user_email"]').val(),
                    course_slug: "{{$course_details->slug}}",
                    password: $('input[name="user_password"]').val(),
                    password_confirmation: $('input[name="user_password"]').val(),
                    _token: $('meta[name="csrf-token"]').attr('content'),
                };

                @if (get_frontend_settings('recaptcha_status'))
                // Execute reCAPTCHA v3
                grecaptcha.ready(function () {
                    grecaptcha.execute('{{ get_frontend_settings('recaptcha_sitekey') }}', {action: 'submit'}).then(function (token) {
                        // Add token to form data
                        formData['g-recaptcha-response'] = token;

                        // Submit the form with the token
                        submitFormWithRecaptcha(formData);
                    }).catch(function (error) {
                        console.error('reCAPTCHA error:', error);
                        $('#loading').addClass('hidden');
                        $('#form-content').removeClass('hidden');
                        $('#error-message').removeClass('hidden');
                        $('.error-text').text('Lỗi xác thực captcha. Vui lòng thử lại.');
                    });
                });
                @else
                // Submit without reCAPTCHA
                submitFormWithRecaptcha(formData);
                @endif
            } catch (error) {
                console.error('Error submitting form:', error);
                $('#loading').addClass('hidden');
                $('#form-content').removeClass('hidden');
                $('#error-message').removeClass('hidden');
            }
        });

        function submitFormWithRecaptcha(formData) {
            // Send AJAX request
            $.ajax({
                type: 'POST',
                url: '{{ route("ajax.register.learning.page") }}',
                data: formData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                beforeSend: function (xhr) {
                    xhr.setRequestHeader('Accept', 'application/json');
                },
                success: function (response) {
                    // Hide loading
                    $('#loading').addClass('hidden');
                    if (response.success) {
                        if (typeof fbq !== 'undefined') {
                            fbq('track', 'Purchase', {
                                value: {{$sale_price ?? 0}},
                                currency: "VND"
                            });
                        }

                        window.location.href = response.url_redirect;

                        // Show success message
                        $('#success-message').removeClass('hidden');

                        // Update message with user's name if available
                        if (response.data && response.data.name) {
                            $('.user-message').text('Cảm ơn ' + response.data.name + ' đã đăng ký. Chúng tôi sẽ liên hệ với bạn sớm nhất.');
                        }

                        // Redirect after 2 seconds if URL is provided
                        // if (response.url_redirect) {
                        //     var countdown = 2;
                        //     $('.countdown').text(countdown);

                        //     var countdownInterval = setInterval(function() {
                        //         countdown--;
                        //         $('.countdown').text(countdown);

                        //         if (countdown <= 0) {
                        //             clearInterval(countdownInterval);
                        //             window.location.href = response.url_redirect;
                        //         }
                        //     }, 1000);
                        // } else {
                        //     $('.redirect-message').addClass('hidden');
                        // }
                    } else {
                        // Handle success: false case
                        $('#form-content').removeClass('hidden');
                        $('#error-message').removeClass('hidden');

                        if (response.errors) {
                            var errorMessage = '';
                            $.each(response.errors, function (field, errors) {
                                errorMessage += errors.join(', ') + '<br>';
                            });
                            $('.error-text').html(errorMessage);
                        } else if (response.message) {
                            $('.error-text').text(response.message);
                        } else {
                            $('.error-text').text('Đã có lỗi xảy ra. Vui lòng thử lại sau.');
                        }

                        // Hide error message after 5 seconds
                        setTimeout(function () {
                            $('#error-message').addClass('hidden');
                        }, 5000);
                    }
                },
                error: function (xhr, status, error) {
                    // Hide loading
                    $('#loading').addClass('hidden');

                    // Show form and error message
                    $('#form-content').removeClass('hidden');
                    $('#error-message').removeClass('hidden');

                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        var errorMessage = '';
                        var messageObj = xhr.responseJSON.message;

                        // Check if message is an object with validation errors
                        if (typeof messageObj === 'object') {
                            // Loop through each field's errors and take only the first error
                            $.each(messageObj, function(field, errors) {
                                if (Array.isArray(errors) && errors.length > 0) {
                                    errorMessage += errors[0] + '<br>';
                                }
                            });

                            if (errorMessage) {
                                $('.error-text').html(errorMessage);
                            } else {
                                $('.error-text').text('Đã có lỗi xảy ra. Vui lòng thử lại sau.');
                            }
                        } else {
                            // If message is a string
                            $('.error-text').text(messageObj);
                        }
                    } else {
                        // Fallback to the original error handling approach
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (response.errors) {
                                var errorMessage = '';
                                $.each(response.errors, function (field, errors) {
                                    if (Array.isArray(errors) && errors.length > 0) {
                                        errorMessage += errors[0] + '<br>';
                                    }
                                });

                                if (errorMessage) {
                                    $('.error-text').html(errorMessage);
                                } else {
                                    $('.error-text').text('Đã có lỗi xảy ra. Vui lòng thử lại sau.');
                                }
                            } else if (response.message) {
                                $('.error-text').text(response.message);
                            } else {
                                $('.error-text').text('Đã có lỗi xảy ra. Vui lòng thử lại sau.');
                            }
                        } catch (e) {
                            $('.error-text').text('Đã có lỗi xảy ra. Vui lòng thử lại sau.');
                        }
                    }

                    $('#error-message').addClass('hidden');
                }
            });
        }
    });
</script>
<script>
    // Facebook Event Tracking Function
    function fbTrackEvent() {
        // Get data attributes from the button
        const button = document.getElementById('submit-btn');
        const eventName = button.getAttribute('data-fb-track');
        const value = parseFloat(button.getAttribute('data-value'));
        const currency = button.getAttribute('data-currency');

        // Check if FB is available and track event if it is
        if (typeof FB !== 'undefined' && FB.AppEvents) {
            FB.AppEvents.logPurchase(value, currency);
        } else if (typeof fbq !== 'undefined') {
            // Fallback to fbq if FB.AppEvents is not available

        } else {
            console.log('Facebook tracking not available');
        }
    }
</script>
</html>


@extends('layouts.default_home_dieu_thu')
@section('content')

<header class="header fixed top-0 left-0 bg-white w-full z-20">
    <div
        class="lg:max-w-full !px-10 xl:max-w-5xl 2xl:max-w-6xl mx-auto xl:!px-0 xl:!py-0.5 flex items-center justify-between"
    >
        <!-- Logo -->
        <img src="{{ asset('assets/frontend/dieu-thu/assets/images/logo.png') }}" alt="MGKS" class="h-15 w-auto" />

        <!-- Navigation Menu -->
        <nav class="hidden lg:flex 2xl:gap-x-0 items-center text-center font-sf-pro-bold">
            <a
                href="#gioi-thieu"
                class="lg:w-40 xl:w-40 2xl:w-[188px] h-10 text-black flex items-center justify-center border-0 border-b border-solid border-transparent hover:!text-orange-500 hover:!border-orange-500 hover:!border-b font-medium transition-colors"
                >Giới thiệu</a
            >
            <a
                href="#khoa-hoc"
                class="lg:w-40 xl:w-40 2xl:w-[188px] h-10 text-black flex items-center justify-center border-0 border-b border-solid border-transparent hover:!text-orange-500 hover:!border-orange-500 hover:!border-b font-medium transition-colors"
                >Danh sách khóa học</a
            >
            <a
                href="#danh-gia"
                class="lg:w-40 xl:w-40 2xl:w-[188px] h-10 text-black flex items-center justify-center border-0 border-b border-solid border-transparent hover:!text-orange-500 hover:!border-orange-500 hover:!border-b font-medium transition-colors"
                >Đánh giá</a
            >
            <a
                href="#lien-he"
                class="lg:w-40 xl:w-40 2xl:w-[188px] h-10 text-black flex items-center justify-center border-0 border-b border-solid border-transparent hover:!text-orange-500 hover:!border-orange-500 hover:!border-b font-medium transition-colors"
                >Liên hệ</a
            >
        </nav>

        <!-- Login Button -->
        <div class="hidden lg:flex items-center">
            @if(!Auth()->check())
                <button
                    class="text-lg lg:w-40 2xl:w-[188px] h-10 bg-gradient-to-b from-[#ff9f15] to-[#e93a00] hover:bg-orange-600 text-white rounded-full font-bold transition-colors font-sf-pro-bold login-button"
                    data-bs-toggle="modal"
                    data-bs-target="#modal-auth"
                >
                    ĐĂNG NHẬP
                </button>
            @else
                <!-- User Profile Dropdown -->
                <div class="relative">
                    <button
                        class="lg:flex hidden bg-white border border-gray-200 text-gray-700 !py-1.5 pl-1.5 pr-4 rounded-full items-center hover:bg-gray-50 transition-colors duration-200"
                        id="user-menu-button"
                        aria-expanded="false"
                        data-bs-toggle="dropdown"
                    >
                        <i class="bi bi-person-circle text-2xl mr-2 {{ Auth()->user()->photo ? 'hidden' : '' }}"></i>
                        @if(Auth()->user()->photo)
                            <img
                                src="{{ get_image(Auth()->user()->photo) }}"
                                alt="User Avatar" 
                                width="30"
                                height="30"
                                class="w-[30px] h-[30px] mr-2 object-cover object-center rounded-full"
                            />
                        @endif
                        <span class="text-sm font-medium">{{ ucfirst(Auth()->user()->name) }}</span>
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <!-- Dropdown Menu -->
                    <div
                        class="dropdown-menu block absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 !py-2 z-50 hidden"
                        id="user-dropdown">
                        <!-- User Info Header -->
                        <div class="!px-4 !py-3 border-b border-gray-100">
                            <div class="flex items-center">
                                @if(Auth()->user()->photo)
                                    <img
                                        src="{{ get_image(Auth()->user()->photo) }}"
                                        alt="User Avatar"
                                        class="w-10 h-10 rounded-full object-cover mr-3"
                                    />
                                @else
                                    <i class="bi bi-person-circle text-4xl mr-3"></i>
                                @endif
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ ucfirst(Auth()->user()->name) }}</p>
                                    <p class="text-xs text-gray-500">{{ Auth()->user()->email }}</p>
                                    @php
                                        $enrollment_status = enroll_status(isset($course_details)?$course_details->id:0, Auth()->user()->id);
                                    @endphp
                                    @if($enrollment_status == 'valid')
                                        <span
                                            class="inline-flex items-center !px-2 !py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
                                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                            PRO
                                        </span>
                                    @else
                                        <span
                                            class="inline-flex items-center !px-2 !py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mt-1">
                                            FREE
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!-- Menu Items -->
                        @if (in_array(auth()->user()->role, ['admin', 'instructor']))
                            <a href="{{ route(auth()->user()->role . '.dashboard') }}"
                                class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                </svg>
                                {{ get_phrase('Dashboard') }}
                            </a>
                        @endif

                        @if (Auth()->user()->role != 'admin')
                            <a href="{{ route('my.courses') }}"
                                class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                {{ get_phrase('My Courses') }}
                            </a>

                            <a href="{{ route('my.profile') }}"
                                class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                {{ get_phrase('My Profile') }}
                            </a>

                            <a href="{{ route('my.affiliate') }}"
                                class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                                {{ get_phrase('Affiliate') }}
                            </a>

                            <a href="{{ route('wishlist') }}"
                                class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                                {{ get_phrase('Wishlist') }}
                            </a>

                            <a href="{{ route('message') }}"
                                class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                                {{ get_phrase('Message') }}
                            </a>

                            <a href="{{ route('purchase.history') }}"
                                class="flex items-center !px-4 !py-2 text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ get_phrase('Purchase History') }}
                            </a>
                        @endif

                        <div class="border-t border-gray-100 mt-2 !pt-2">
                            <a href="{{ route('logout.course') }}"
                                class="flex items-center !px-4 !py-2 text-sm text-red-600 hover:bg-red-50">
                                <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                {{ get_phrase('Log Out') }}
                            </a>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Mobile menu button -->
        <button class="block lg:hidden menu-toggle">
            <img src="{{ asset('assets/frontend/dieu-thu/assets/images/bar.svg') }}" alt="" class="w-6 h-6" />
        </button>
    </div>

    <!-- Mobile Navigation Menu (hidden by default) -->
    <div
        id="mobile-menu"
        class="hidden !pt-0 !pb-4 absolute !pt-5 top-14 left-0 !px-8 w-full bg-white z-50"
    >
        <div class="flex flex-col space-y-3">
            <a
                href="#gioi-thieu"
                class="lg:w-40 xl:w-40 2xl:w-[188px] h-10 text-black flex items-center justify-center border-0 border-b border-solid border-transparent hover:!text-orange-500 hover:!border-orange-500 hover:!border-b font-medium transition-colors"
                >Giới thiệu</a
            >
            <a
                href="#khoa-hoc"
                class="lg:w-40 xl:w-40 2xl:w-[188px] h-10 text-black flex items-center justify-center border-0 border-b border-solid border-transparent hover:!text-orange-500 hover:!border-orange-500 hover:!border-b font-medium transition-colors"
                >Danh sách khóa học</a
            >
            <a
                href="#danh-gia"
                class="lg:w-40 xl:w-40 2xl:w-[188px] h-10 text-black flex items-center justify-center border-0 border-b border-solid border-transparent hover:!text-orange-500 hover:!border-orange-500 hover:!border-b font-medium transition-colors"
                >Đánh giá</a
            >
            <a
                href="#lien-he"
                class="lg:w-40 xl:w-40 2xl:w-[188px] h-10 text-black flex items-center justify-center border-0 border-b border-solid border-transparent hover:!text-orange-500 hover:!border-orange-500 hover:!border-b font-medium transition-colors"
                >Liên hệ</a
            >
            @if(!Auth()->check())
                <button
                    class="text-lg lg:w-40 2xl:w-[188px] h-10 bg-gradient-to-b from-[#ff9f15] to-[#e93a00] hover:bg-orange-600 text-white rounded-full font-bold transition-colors font-sf-pro-bold login-button"
                    data-bs-toggle="modal"
                    data-bs-target="#modal-auth"
                >
                    ĐĂNG NHẬP
                </button>
            @else
                <!-- Mobile User Profile -->
                <div class="border-t !pt-3 mt-3">
                    <div class="flex items-center mb-3">
                        @if(Auth()->user()->photo)
                            <img
                                src="{{ get_image(Auth()->user()->photo) }}"
                                alt="User Avatar"
                                class="w-10 h-10 rounded-full object-cover mr-3"
                            />
                        @else
                            <i class="bi bi-person-circle text-4xl mr-3"></i>
                        @endif
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ ucfirst(Auth()->user()->name) }}</p>
                            <p class="text-xs text-gray-500">{{ Auth()->user()->email }}</p>
                        </div>
                    </div>

                    @if (in_array(auth()->user()->role, ['admin', 'instructor']))
                        <a href="{{ route(auth()->user()->role . '.dashboard') }}"
                            class="block !py-2 text-sm text-gray-700 hover:text-orange-500">
                            {{ get_phrase('Dashboard') }}
                        </a>
                    @endif

                    @if (Auth()->user()->role != 'admin')
                        <a href="{{ route('my.courses') }}"
                            class="block !py-2 text-sm text-gray-700 hover:text-orange-500">
                            {{ get_phrase('My Courses') }}
                        </a>
                        <a href="{{ route('my.profile') }}"
                            class="block !py-2 text-sm text-gray-700 hover:text-orange-500">
                            {{ get_phrase('My Profile') }}
                        </a>
                        <a href="{{ route('wishlist') }}"
                            class="block !py-2 text-sm text-gray-700 hover:text-orange-500">
                            {{ get_phrase('Wishlist') }}
                        </a>
                        <a href="{{ route('purchase.history') }}"
                            class="block !py-2 text-sm text-gray-700 hover:text-orange-500">
                            {{ get_phrase('Purchase History') }}
                        </a>
                    @endif

                    <a href="{{ route('logout.course') }}"
                        class="block !py-2 text-sm text-red-600 hover:text-red-700 border-t !pt-2 mt-2">
                        {{ get_phrase('Log Out') }}
                    </a>
                </div>
            @endif
        </div>
    </div>
</header>
<div class="h-16 w-full"></div>

<section
    class="hero relative overflow-hidden bg-center-left bg-cover xl:bg-fill bg-origin-content bg-center bg-no-repeat"
    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-bg.jpg') }})"
>
    <div
        class="container xl:max-w-5xl 2xl:max-w-6xl mx-auto !px-10 !pt-10 xl:!pt-4 xl:!px-0 !py-4 relative z-10"
    >
        <div class="flex flex-col xl:flex-row justify-between items-center">
            <!-- Left Content -->
            <div
                class="w-full md:max-w-md xl:w-1/2 lg:max-w-[500px] mx-auto xl:mx-0"
                data-x-aos="fade-right"
                data-aos-duration="800"
            >
                <!-- Academy Badge -->
                <div
                    class="w-full text-center md:w-fit text-sm md:text-xl md:leading-[1.6] !px-6 !py-2 mb-8 flex items-center justify-center text-white bg-repear-x rounded-[34px_0px] font-normal relative overflow-hidden"
                    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }})"
                    data-x-aos="fade-down"
                    data-aos-delay="200"
                >
                    <span
                        class="absolute right-1 translate-x-1/2 -bottom-2 mix-blend-screen"
                    >
                        <div
                            src="{{ asset('assets/frontend/dieu-thu/assets/images/star.png') }}"
                            alt=""
                            class="w-[92px] h-[92px] inline-block bg-cover bg-origin-content bg-position-[left_top] bg-no-repeat"
                            style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/star.png') }})"
                        ></div>
                    </span>
                    MGK INTERNATIONAL BEAUTY ACADEMY

                    <span class="absolute left-2 -translate-x-1/2 top-0 mix-blend-screen">
                        <div
                            src="{{ asset('assets/frontend/dieu-thu/assets/images/star.png') }}"
                            alt=""
                            class="w-[92px] h-[92px] inline-block bg-cover bg-origin-content bg-position-[left_top] bg-no-repeat"
                            style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/star.png') }})"
                        ></div>
                    </span>
                </div>

                <!-- Main Heading -->

                <h1
                    class="w-fit text-3xl md:text-5xl lg:text-6xl font-bold font-lastone text-amber-900 leading-tight mb-8"
                    data-x-aos="fade-up"
                    data-aos-delay="400"
                >
                    <p>Học để</p>
                    <p class="ml-10 md:ml-40">Làm nghề</p>
                    <p>Học để</p>
                    <p
                        class="text-6xl md:text-9xl xl:text-[140px] -mt-8 md:-mt-20 !px-1 font-heartbeat font-normal gradient-text ml-20 md:ml-40"
                    >
                        Làm chủ
                    </p>
                </h1>
                <!-- Description -->
                <p
                    class="text-black text-lg text-center mb-8 leading-relaxed"
                    data-x-aos="fade-up"
                    data-aos-delay="600"
                >
                    Học viện đào tạo quốc tế trực tuyến về thẩm mỹ hàng đầu tại Việt Nam,
                    giúp bạn thành công trên lộ trình từ người mới bắt đầu đến chủ spa
                    chuyên nghiệp.
                </p>

                <!-- CTA Button -->
                <a
                    href="#khoa-hoc"
                    class="text-center text-base md:text-lg block md:w-60 w-full bg-gradient-to-b from-[#ff9f15] to-[#e93a00] mx-auto hover:bg-orange-600 text-white !px-8 !py-4 rounded-full font-semibold shadow-lg pulse-scaled"
                    data-x-aos="zoom-in"
                    data-aos-delay="800"
                >
                    XEM CÁC KHÓA HỌC
                </a>
            </div>

            <!-- Right Content - Image -->
            <div
                class="relative xl:w-fit xl:max-w-[450px]"
                data-x-aos="fade-left"
                data-aos-duration="800"
                data-aos-delay="300"
            >
                <img
                    src="{{ asset('assets/frontend/dieu-thu/assets/images/hero-right.png') }}"
                    alt="Beauty Academy Instructor"
                    class="xl:w-[414.5px] h-auto w-full md:w-auto"
                    width="414.48"
                    height="669.8"
                />
            </div>
        </div>
    </div>
</section>

<section
    id="gioi-thieu"
    class="about relative overflow-hidden bg-center-left bg-cover xl:bg-fill bg-no-repeat !py-10 lg:!py-20"
    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/about-bg.jpg') }})"
>
    <!-- Background overlay -->

    <div class="container xl:max-w-6xl mx-auto !px-5 md:!px-10 xl:!px-0 relative z-10">
        <!-- Main Content -->
        <div class="flex flex-col-reverse xl:flex-row items-center gap-6 md:gap-12 mb-15">
            <!-- Left - Video Thumbnail -->
            <div
                class="w-fit mx-auto xl:mx-0 xl:w-1/2"
                data-x-aos="fade-right"
                data-aos-duration="800"
            >
                <div
                    class="youtube-video-container"
                    data-video-src="https://www.youtube.com/embed/pqDrLmK6Xsg?si=F0GlIbj7-xg7sPPi&amp;autoplay=1&amp;rel=0&amp;modestbranding"
                    data-poster="{{ asset('assets/frontend/dieu-thu/assets/images/youtube-thumbnail.png') }}"
                >
                    <div
                        class="video-container relative overflow-hidden border-[6px] rounded-[20px] border-solid border-[#ffecaa]"
                    >
                        <div class="poster-wrapper">
                            <img
                                src="{{ asset('assets/frontend/dieu-thu/assets/images/about-ytb-thumbnail.jpg') }}"
                                alt="MGK Beauty Academy Video"
                                class="w-full h-auto aspect-video md:aspect-auto md:w-[620px] md:!h-[349px] object-cover"
                            />
                            <!-- Play button overlay -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div
                                    class="w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform cursor-pointer"
                                >
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="100%"
                                        height="100%"
                                        preserveAspectRatio="none"
                                        viewBox="0 0 408.7 408.7"
                                        fill="rgba(0, 0, 0, 0.5)"
                                    >
                                        <polygon
                                            fill="#fff"
                                            points="163.5 296.3 286.1 204.3 163.5 112.4 163.5 296.3"
                                        ></polygon>
                                        <path
                                            d="M204.3,0C91.5,0,0,91.5,0,204.3S91.5,408.7,204.3,408.7s204.3-91.5,204.3-204.3S316.7,0,204.3,0ZM163.5,296.3V112.4l122.6,91.9Z"
                                            transform="translate(0 0)"
                                        ></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- YouTube iframe container (initially empty) -->
                        <div
                            class="iframe-container hidden aspect-video md:aspect-auto md:w-[620px] md:h-[349px]"
                        ></div>
                    </div>
                </div>
            </div>

            <!-- Right - Content -->
            <div
                class="w-full lg:max-w-2xl xl:w-1/2"
                data-x-aos="fade-left"
                data-aos-duration="800"
                data-aos-delay="200"
            >
                <!-- Badge -->
                <div
                    class="w-full text-center md:text-left md:w-fit text-xl xl:text-[30px] mb-6 xl:leading-[1.2] text-white !px-6 !py-2 rounded-[25px_0px] font-sf-pro-bold bg-origin-padding bg-fill bg-center-left bg-no-repeat font-bold"
                    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }})"
                    data-x-aos="fade-down"
                    data-aos-delay="400"
                >
                    VỀ MGK Beauty
                </div>

                <!-- Heading -->
                <h2
                    class="flex items-start text-xl md:text-3xl lg:text-4xl xl:text-[25px] font-bold text-amber-900 mb-6"
                    data-x-aos="fade-up"
                    data-aos-delay="600"
                >
                    <span> Nơi </span>
                    <span
                        class="text-7xl md:text-[100px] leading-none font-heartbeat font-light text-black gradient-text"
                        >Khởi Đầu</span
                    >
                </h2>

                <!-- Subtitle -->
                <h3
                    class="text-lg md:text-xl xl:text-[25px] font-semibold text-amber-900 mb-4 md:mb-6"
                    data-x-aos="fade-up"
                    data-aos-delay="700"
                >
                    SỰ NGHIỆP LÀM ĐẸP CHUYÊN NGHIỆP
                </h3>

                <!-- Description -->
                <div
                    class="text-black text-sm md:text-base lg:text-lg"
                    data-x-aos="fade-up"
                    data-aos-delay="800"
                >
                    <p class="mb-2">
                        MGK Beauty là
                        <strong> bệ phóng vững chắc cho hàng ngàn học viên </strong> chinh
                        phục sự nghiệp trong ngành spa, chăm sóc da và thẩm mỹ.
                    </p>
                    <p>
                        Với hơn <strong> 10+ năm kinh nghiệm</strong>, MGK Beauty tự hào là
                        <strong> đơn vị đào tạo thực chiến </strong> , giúp học viên không
                        chỉ nắm chắc kỹ năng tay nghề mà còn biết cách vận hành – kinh doanh
                        – làm chủ cơ sở spa sau khi hoàn thành khóa học.
                    </p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            <!-- Card 1 -->
            <div
                class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg"
                data-x-aos="fade-up"
                data-aos-delay="200"
            >
                <div class="w-fit max-w-full h-20 mx-auto mb-4">
                    <img
                        src="{{ asset('assets/frontend/dieu-thu/assets/images/about-student.png') }}"
                        alt="Students"
                        class="w-auto h-20 object-contain"
                    />
                </div>
                <div
                    class="text-3xl md:text-[50px] font-sf-pro-bold gradient-text stroke-[4px] stroke-white leading-[1.4] mb-2"
                >
                    10,000
                </div>
                <p class="text-sm md:text-base text-black leading-[1.4]">
                    Học viên có việc làm và mức thu nhập ổn định.
                </p>
            </div>

            <!-- Card 2 -->
            <div
                class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg"
                data-x-aos="fade-up"
                data-aos-delay="400"
            >
                <div class="w-fit max-w-full h-20 mx-auto mb-4">
                    <img
                        src="{{ asset('assets/frontend/dieu-thu/assets/images/about-teacher.png') }}"
                        alt="Teachers"
                        class="w-auto h-20 object-contain"
                    />
                </div>
                <div
                    class="text-3xl md:text-[50px] font-sf-pro-bold gradient-text stroke-[4px] stroke-white leading-[1.4] mb-2"
                >
                    1000
                </div>
                <p class="text-sm md:text-base text-black leading-[1.4]">
                    Học viên đã được đào tạo trở thành nhà quản lý và chủ các cơ sở làm đẹp.
                </p>
            </div>

            <!-- Card 3 -->
            <div
                class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg"
                data-x-aos="fade-up"
                data-aos-delay="600"
            >
                <div class="w-fit max-w-full h-20 mx-auto mb-4">
                    <img
                        src="{{ asset('assets/frontend/dieu-thu/assets/images/about-worlds.png') }}"
                        alt="Global"
                        class="w-auto h-20 object-contain"
                    />
                </div>
                <div
                    class="text-3xl md:text-[50px] font-sf-pro-bold gradient-text stroke-[4px] stroke-white leading-[1.4] mb-2"
                >
                    100
                </div>
                <p class="text-sm md:text-base text-black leading-[1.4]">
                    Học viên trở thành cố vấn và cung cấp nhân sự cho chuỗi cơ sở trong và
                    ngoài nước.
                </p>
            </div>

            <!-- Card 4 -->
            <div
                class="bg-white/90 backdrop-blur-sm rounded-2xl p-6 text-center shadow-lg"
                data-x-aos="fade-up"
                data-aos-delay="800"
            >
                <div class="w-full max-w-full h-20 flex items-center justify-center mb-4">
                    <img
                        src="{{ asset('assets/frontend/dieu-thu/assets/images/about-partner.png') }}"
                        alt="Partners"
                        class="my-auto w-auto h-15 object-contain"
                    />
                </div>
                <div
                    class="text-3xl md:text-[50px] font-sf-pro-bold gradient-text stroke-[4px] stroke-white leading-[1.4] mb-2"
                >
                    10
                </div>
                <p class="text-sm md:text-base text-black leading-[1.4]">
                    Doanh nghiệp thẩm mỹ đang được MGK BEAUTY làm cố vấn cho thương hiệu.
                </p>
            </div>
        </div>
    </div>
</section>

<section
    class="offer relative overflow-hidden bg-cover lg:bg-fill bg-[50%_0%] bg-no-repeat !pt-0 !py-10 lg:!py-20 !px-5 md:!px-8 lg:!px-10 xl:!px-0"
    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/offer-bg.jpg') }})"
>
    <div
        class="container !px-6 md:!px-8 !py-8 md:!py-4 lg:max-w-4xl lg:!py-8 lg:rounded-3xl lg:!px-10 xl:pl-20 xl:pr-16 xl:!py-10 xl:rounded-[30px] xl:max-w-6xl border-2 border-solid border-white mx-auto relative z-10"
        style="
            background-image: radial-gradient(
                circle,
                rgba(255, 255, 255, 0.34),
                rgba(255, 255, 255, 0.7)
            );
        "
    >
        <!-- Header Section -->
        <div class="text-center mb-4">
            <h2
                class="text-lg md:text-2xl lg:text-3xl xl:text-[35px] font-sf-pro-bold text-center font-normal text-amber-900 mb-4"
                style="text-shadow: rgba(0, 0, 0, 0.39) 2px 2px 2px"
                data-x-aos="fade-down"
                data-aos-duration="800"
            >
                <div>
                    TẠI MGK BEAUTY ACADEMY,<br />
                    BẠN CÓ THỂ HỌC GÌ?
                </div>
            </h2>
            <p
                class="text-black text-base md:text-lg leading-[1.6] mx-auto w-fit"
                data-x-aos="fade-up"
                data-aos-delay="200"
            >
                Chúng tôi cung cấp bộ kỹ năng trọn vẹn và toàn diện từ thực hành nghề đến
                quản trị hiệu quả
            </p>
        </div>

        <!-- Skills Section 1 -->
        <div class="mb-10 md:mb-20">
            <div class="flex flex-col lg:flex-row items-center">
                <!-- Left Content -->
                <div
                    class="w-full lg:w-1/2"
                    data-x-aos="fade-right"
                    data-aos-duration="800"
                >
                    <h3
                        class="text-lg md:text-2xl lg:text-xl xl:text-[25px] xl:leading-[1.6] font-bold text-[#49241c] mb-5"
                        data-x-aos="fade-up"
                        data-aos-delay="200"
                    >
                        <span class="bg-[#fde233]">KỸ NĂNG LÀM NGHỀ</span>
                        CHUẨN QUỐC TẾ
                    </h3>

                    <!-- Skill Item 1 -->
                    <div
                        class="flex items-start gap-4 mb-6"
                        data-x-aos="fade-up"
                        data-aos-delay="400"
                    >
                        <div
                            class="w-11 h-11 text-base lg:text-lg xl:text-xl xl:leading-[1.4] bg-cover bg-origin-content bg-[50%_0%] bg-repeat text-white rounded-full flex items-center justify-center font-bold flex-shrink-0"
                            style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }})"
                        >
                            01
                        </div>
                        <div class="text-black font-normal leading-[1.4]">
                            <h4 class="text-lg lg:text-lg xl:text-xl mb-2">
                                Kỹ thuật viên Spa chuẩn Hàn
                            </h4>
                            <p class="text-sm md:text-base">
                                Chương trình gồm 3 cấp độ: Cơ bản, Công Nghệ Cao và Master
                                Da Liễu, giúp học viên nắm vững kỹ thuật, công nghệ và dược
                                mỹ phẩm.
                            </p>
                        </div>
                    </div>

                    <!-- Skill Item 2 -->
                    <div
                        class="flex items-start gap-4"
                        data-x-aos="fade-up"
                        data-aos-delay="600"
                    >
                        <div
                            class="w-11 h-11 text-base lg:text-lg xl:text-xl xl:leading-[1.4] bg-cover bg-origin-content bg-[50%_0%] bg-repeat text-white rounded-full flex items-center justify-center font-bold flex-shrink-0"
                            style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }})"
                        >
                            02
                        </div>
                        <div class="font-normal text-black leading-[1.4]">
                            <h4 class="text-lg lg:text-lg xl:text-xl mb-2">
                                Kỹ năng chăm sóc và bán hàng ngành làm đẹp
                            </h4>
                            <p class="text-sm md:text-base">
                                Cung cấp nền tảng kiến thức và quy trình chuyên nghiệp trong
                                bán hàng, chăm sóc khách hàng, giúp xây dựng đội ngũ hiệu
                                quả.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Right Image -->
                <div
                    class="w-full lg:w-1/2"
                    data-x-aos="fade-left"
                    data-aos-duration="800"
                    data-aos-delay="300"
                >
                    <div class="relative">
                        <img
                            src="{{ asset('assets/frontend/dieu-thu/assets/images/offer-right-1.png') }}"
                            alt=""
                            srcset=""
                            width="113.898px"
                            height="209.572px"
                            class="w-20 md:w-28 lg:w-24 h-auto translate-y-0 md:translate-y-1/2 lg:translate-y-[20%] xl:w-[113.898px] xl:h-[209.572px] xl:translate-y-[15%] absolute top-0 left-0"
                        />
                        <img
                            src="{{ asset('assets/frontend/dieu-thu/assets/images/offer-right-center.png') }}"
                            alt="Professional Beauty Skills"
                            class="relative z-[1] w-full h-auto -left-[12%] md:-left-10 xl:-left-[20%]"
                        />

                        <img
                            src="{{ asset('assets/frontend/dieu-thu/assets/images/offer-right-3.png') }}"
                            alt=""
                            srcset=""
                            width="154"
                            height="258"
                            class="w-20 md:w-40 lg:w-36 h-auto xl:w-[154px] xl:h-[258px] absolute top-0 right-0"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Skills Section 2 -->
        <div>
            <div class="flex flex-col lg:flex-row-reverse gap-2 md:gap-12">
                <!-- Right Content -->
                <div class="w-full lg:w-1/2" data-x-aos="fade-left" data-aos-duration="800">
                    <h3
                        class="text-lg md:text-2xl lg:text-xl xl:text-[25px] xl:leading-[1.6] font-bold text-[#49241c] mb-5"
                        data-x-aos="fade-up"
                        data-aos-delay="200"
                    >
                        <span class="bg-[#fde233]">KỸ NĂNG QUẢN TRỊ</span> ĐỂ TỰ XÂY DỰNG
                        DOANH NGHIỆP SINH LỜI VÀ HIỆU QUẢ
                    </h3>

                    <!-- Management Skills -->
                    <div class="space-y-6">
                        <!-- Skill Item 1 -->
                        <div
                            class="flex items-start gap-4"
                            data-x-aos="fade-up"
                            data-aos-delay="400"
                        >
                            <div
                                class="w-11 h-11 text-base lg:text-lg xl:text-xl xl:leading-[1.4] bg-cover bg-origin-content bg-[50%_0%] bg-repeat text-white rounded-full flex items-center justify-center font-bold flex-shrink-0"
                                style="
                                    background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }});
                                "
                            >
                                01
                            </div>
                            <div class="font-normal text-black leading-[1.4]">
                                <h4 class="text-lg lg:text-lg xl:text-xl mb-2">
                                    Nghệ thuật quản trị nhân sự ngành làm đẹp
                                </h4>
                                <p class="text-sm md:text-base">
                                    Giúp chủ spa xây dựng đội ngũ trung thành, quản lý hiệu
                                    quả, giảm nghỉ việc, tăng hiệu suất và doanh thu vượt
                                    trội.
                                </p>
                            </div>
                        </div>

                        <!-- Skill Item 2 -->
                        <div
                            class="flex items-start gap-4"
                            data-x-aos="fade-up"
                            data-aos-delay="500"
                        >
                            <div
                                class="w-11 h-11 text-base lg:text-lg xl:text-xl xl:leading-[1.4] bg-cover bg-origin-content bg-[50%_0%] bg-repeat text-white rounded-full flex items-center justify-center font-bold flex-shrink-0"
                                style="
                                    background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }});

                                "
                            >
                                02
                            </div>
                            <div class="font-normal text-black leading-[1.4]">
                                <h4 class="text-lg lg:text-lg xl:text-xl mb-2">
                                    Tài chính kiểm soát lợi nhuận ngành làm đẹp
                                </h4>
                                <p class="text-sm md:text-base">
                                    Hướng dẫn kiểm soát chi phí, tối ưu dòng tiền, gia tăng
                                    lợi nhuận và tránh thất thoát.
                                </p>
                            </div>
                        </div>

                        <!-- Skill Item 3 -->
                        <div
                            class="flex items-start gap-4"
                            data-x-aos="fade-up"
                            data-aos-delay="600"
                        >
                            <div
                                class="w-11 h-11 text-base lg:text-lg xl:text-xl xl:leading-[1.4] bg-cover bg-origin-content bg-[50%_0%] bg-repeat text-white rounded-full flex items-center justify-center font-bold flex-shrink-0"
                                style="
                                    background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }});

                                "
                            >
                                03
                            </div>
                            <div class="font-normal text-black leading-[1.4]">
                                <h4 class="text-lg lg:text-lg xl:text-xl mb-2">
                                    Kỹ năng quản trị vận hành & setup hệ thống chuỗi
                                </h4>
                                <p class="text-sm md:text-base">
                                    Hỗ trợ xây dựng và vận hành hệ thống spa bài bản, mở
                                    rộng chi nhánh hiệu quả, kiểm soát đồng bộ và phát triển
                                    chuỗi cơ sở bền vững.
                                </p>
                            </div>
                        </div>

                        <!-- Skill Item 4 -->
                        <div
                            class="flex items-start gap-4"
                            data-x-aos="fade-up"
                            data-aos-delay="700"
                        >
                            <div
                                class="w-11 h-11 text-base lg:text-lg xl:text-xl xl:leading-[1.4] bg-cover bg-origin-content bg-[50%_0%] bg-repeat text-white rounded-full flex items-center justify-center font-bold flex-shrink-0"
                                style="
                                    background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }});
                                "
                            >
                                04
                            </div>
                            <div class="font-normal text-black leading-[1.4]">
                                <h4 class="text-lg lg:text-lg xl:text-xl mb-2">
                                    Xây dựng đội ngũ, kỹ năng chăm sóc, tư vấn online
                                </h4>
                                <p class="text-sm md:text-base">
                                    Cung cấp kiến thức, kỹ năng và công cụ thiết yếu để xây
                                    dựng đội ngũ tư vấn CSKH Online hiệu quả, giúp tăng
                                    doanh thu và giữ chân khách hàng.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Left Images -->
                <div
                    class="w-full relative min-h-[400px] md:min-h-[750px] lg:min-h-0 lg:w-1/2 flex items-end lg:items-center"
                    data-x-aos="fade-right"
                    data-aos-duration="800"
                    data-aos-delay="300"
                >
                    <img
                        src="{{ asset('assets/frontend/dieu-thu/assets/images/offer-right-2.png') }}"
                        alt="Management Skills 1"
                        class="w-full h-auto -translate-y-2/3 lg:-translate-y-full top-1/2 xl:top-0 xl:translate-y-0 absolute left-0"
                    />
                    <img
                        src="{{ asset('assets/frontend/dieu-thu/assets/images/offer-left-2.png') }}"
                        alt="Management Skills 1"
                        class="w-full h-auto xl:mt-auto relative z-[1]"
                    />
                </div>
            </div>
        </div>
    </div>
</section>

<section
    class="target relative overflow-hidden bg-cover lg:bg-fill bg-[50%_0%] bg-no-repeat"
    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/target-bg.jpg') }})"
>
    <!-- Background overlay -->

    <div
        class="container !px-5 md:!px-10 lg:max-w-4xl xl:!px-0 xl:!py-0 xl:max-w-6xl mx-auto relative z-10"
    >
        <!-- Header -->
        <h2
            class="text-lg md:text-2xl lg:text-3xl xl:text-[35px] xl:leading-[1.2] text-center mb-6 lg:mb-14 font-sf-pro-bold text-amber-900"
            data-x-aos="fade-down"
            data-aos-duration="800"
        >
            MGK ACADEMY BEAUTY <br />
            DÀNH CHO NHỮNG AI?
        </h2>

        <!-- Target Cards Grid -->
        <div
            class="grid grid-cols-1 gap-x-6 gap-y-15 lg:gap-x-10 lg:gap-y-15 md:grid-cols-2 xl:grid-cols-4 xl:gap-x-10 mb-8"
        >
            <!-- Card 1: Người mới bắt đầu -->
            <div
                class="text-center flex flex-col"
                data-x-aos="fade-up"
                data-aos-delay="200"
            >
                <div class="relative h-[175px] md:h-[215px] w-full flex items-end">
                    <div
                        class="w-[160px] h-[160px] md:w-[215px] md:h-[215px] p-3 rounded-full absolute top-0 right-0 overflow-hidden bg-gradient-150 from-[#e0a000] to-white"
                        style="
                            background-image: linear-gradient(
                                150deg,
                                var(--tw-gradient-stops)
                            );
                            box-shadow: rgb(0, 0, 0) 10px 0px 20px -15px;
                        "
                    >
                        <img
                            src="{{ asset('assets/frontend/dieu-thu/assets/images/target-1.jpg') }}"
                            alt="Người mới bắt đầu"
                            class="w-full h-full object-cover rounded-full"
                        />
                    </div>
                    <div
                        class="w-full h-1/2 bg-white border-0 border-r border-solid border-[#ffeda1]"
                    >
                        <div
                            class="w-full h-full bg-gradient-to-br from-[#e0a000] to-white rounded-[15px_0px_200px]"
                        ></div>
                    </div>
                </div>
                <div class="relative flex-auto">
                    <div
                        class="absolute border border-solid border-[#ffea94] w-[70px] h-[70px] transition-transform hover:rotate-0 rotate-45 bottom-0 left-1/2 -translate-x-1/2 translate-y-1/3 z-[1]"
                        style="background-image: linear-gradient(#49241c, #875a0b)"
                    ></div>

                    <div
                        class="relative z-[1] !pt-5 !pb-[92px] rounded-b-[15px] bg-white border-t-0 border border-solid border-[#ffeda1] !px-5 h-full"
                    >
                        <h3
                            class="text-base lg:text-lg xl:leading-[1.6] font-sf-pro-bold text-[#875a0b] !pb-4"
                        >
                            NGƯỜI MỚI BẮT ĐẦU
                        </h3>

                        <div class="w-20 h-px bg-orange-500 mx-auto mb-4"></div>

                        <ul
                            class="text-sm lg:text-base leading-[1.4] text-black space-y-1 text-left"
                        >
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-black rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                Muốn học nghề spa, chăm sóc da từ cơ bản
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-black rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                Chưa có định hướng rõ ràng, cần được dẫn dắt
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-black rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                Sợ học xong không làm được – cần đào tạo thực hành kỹ
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-black rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                <span>
                                    Ưu điểm: Có <strong>khóa học online tại nhà</strong> ,
                                    tiết kiệm thời gian & chi phí
                                </span>
                            </li>
                        </ul>

                        <div
                            class="absolute left-1/2 bottom-0 -translate-x-1/2 translate-y-[20%]"
                        >
                            <div
                                class="w-15 h-15 bg-[#fde233] rounded-full flex items-center justify-center shadow-lg transform"
                            >
                                <span
                                    class="text-base xl:text-[30px] leading-[1.1] text-amber-900 font-sf-pro-bold transform"
                                >
                                    01
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 2: Chủ spa - Người sáp mở spa -->
            <div
                class="text-center flex flex-col"
                data-x-aos="fade-up"
                data-aos-delay="400"
            >
                <div class="relative h-[175px] md:h-[215px] w-full flex items-end">
                    <div
                        class="w-[160px] h-[160px] md:w-[215px] md:h-[215px] p-3 rounded-full absolute top-0 right-0 overflow-hidden bg-gradient-150 from-[#e0a000] to-white shadow-lg"
                        style="
                            background-image: linear-gradient(
                                150deg,
                                var(--tw-gradient-stops)
                            );
                            box-shadow: rgb(0, 0, 0) 10px 0px 20px -15px;
                        "
                    >
                        <img
                            src="{{ asset('assets/frontend/dieu-thu/assets/images/target-2.jpg') }}"
                            alt="Chủ spa"
                            class="w-full h-full object-cover rounded-full"
                        />
                    </div>
                    <div
                        class="w-full h-1/2 bg-white border-0 border-r border-solid border-[#ffeda1]"
                    >
                        <div
                            class="w-full h-full bg-gradient-to-br from-[#e0a000] to-white rounded-[15px_0px_200px]"
                        ></div>
                    </div>
                </div>
                <div class="relative flex-auto">
                    <div
                        class="absolute border border-solid border-[#ffea94] w-[70px] h-[70px] transition-transform hover:rotate-0 rotate-45 bottom-0 left-1/2 -translate-x-1/2 translate-y-1/3 z-[1]"
                        style="background-image: linear-gradient(#49241c, #875a0b)"
                    ></div>

                    <div
                        class="relative z-[1] !pt-5 !pb-[92px] rounded-b-[15px] bg-white border-t-0 border border-solid border-[#ffeda1] !px-5"
                    >
                        <h3
                            class="text-base lg:text-lg xl:leading-[1.6] font-sf-pro-bold text-[#875a0b] !pb-4"
                        >
                            CHỦ SPA - <br />NGƯỜI SẮP MỞ SPA
                        </h3>

                        <div class="w-20 h-px bg-orange-500 mx-auto mb-4"></div>
                        <ul
                            class="text-sm lg:text-base leading-[1.4] text-black space-y-1 text-left"
                        >
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                Đang điều hành spa nhưng rối quy trình, thiếu kỹ năng quản
                                lý
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                Không biết cách giữ khách, marketing yếu
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                Cần lộ trình vận hành – phát triển spa bài bản
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                <span>
                                    Ưu điểm: Có khóa học chuyên sâu về
                                    <strong> quản trị & phát triển kinh doanh spa </strong>
                                </span>
                            </li>
                        </ul>

                        <div
                            class="absolute left-1/2 bottom-0 -translate-x-1/2 translate-y-[20%]"
                        >
                            <div
                                class="w-15 h-15 bg-[#fde233] rounded-full flex items-center justify-center shadow-lg transform"
                            >
                                <span
                                    class="text-base xl:text-[30px] leading-[1.1] text-amber-900 font-sf-pro-bold transform"
                                >
                                    02
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 3: Kỹ thuật viên đang làm việc -->
            <div
                class="text-center flex flex-col"
                data-x-aos="fade-up"
                data-aos-delay="600"
            >
                <div class="relative h-[175px] md:h-[215px] w-full flex items-end">
                    <div
                        class="w-[160px] h-[160px] md:w-[215px] md:h-[215px] p-3 rounded-full absolute top-0 right-0 overflow-hidden bg-gradient-150 from-[#e0a000] to-white shadow-lg"
                        style="
                            background-image: linear-gradient(
                                150deg,
                                var(--tw-gradient-stops)
                            );
                            box-shadow: rgb(0, 0, 0) 10px 0px 20px -15px;
                        "
                    >
                        <img
                            src="{{ asset('assets/frontend/dieu-thu/assets/images/target-3.jpg') }}"
                            alt="Kỹ thuật viên"
                            class="w-full h-full object-cover rounded-full"
                        />
                    </div>
                    <div
                        class="w-full h-1/2 bg-white border-0 border-r border-solid border-[#ffeda1]"
                    >
                        <div
                            class="w-full h-full bg-gradient-to-br from-[#e0a000] to-white rounded-[15px_0px_200px]"
                        ></div>
                    </div>
                </div>

                <div class="relative flex-auto">
                    <div
                        class="absolute border border-solid border-[#ffea94] w-[70px] h-[70px] transition-transform hover:rotate-0 rotate-45 bottom-0 left-1/2 -translate-x-1/2 translate-y-1/3 z-[1]"
                        style="background-image: linear-gradient(#49241c, #875a0b)"
                    ></div>

                    <div
                        class="relative z-[1] !pt-5 !pb-[92px] rounded-b-[15px] bg-white border-t-0 border border-solid border-[#ffeda1] !px-5 h-full"
                    >
                        <h3
                            class="text-base lg:text-lg xl:leading-[1.6] font-sf-pro-bold text-[#875a0b] !pb-4"
                        >
                            KỸ THUẬT VIÊN ĐANG <br />LÀM VIỆC
                        </h3>

                        <div class="w-20 h-px bg-orange-500 mx-auto mb-4"></div>
                        <ul
                            class="text-sm lg:text-base leading-[1.4] text-black space-y-1 text-left"
                        >
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                Tay nghề chưa vững, kỹ thuật lỗi thời
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                <span>
                                    Muốn nâng cấp tay nghề để
                                    <strong> tăng thu nhập – thăng tiến </strong>
                                </span>
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                Cần học thêm công nghệ – xu hướng làm đẹp mới
                            </li>
                        </ul>

                        <div
                            class="absolute left-1/2 bottom-0 -translate-x-1/2 translate-y-[20%]"
                        >
                            <div
                                class="w-15 h-15 bg-[#fde233] rounded-full flex items-center justify-center shadow-lg transform"
                            >
                                <span
                                    class="text-base xl:text-[30px] leading-[1.1] text-amber-900 font-sf-pro-bold transform"
                                >
                                    03
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 4: Phụ nữ muốn tự chủ tài chính -->
            <div
                class="text-center flex flex-col"
                data-x-aos="fade-up"
                data-aos-delay="800"
            >
                <div class="relative h-[175px] md:h-[215px] w-full flex items-end">
                    <div
                        class="w-[160px] h-[160px] md:w-[215px] md:h-[215px] p-3 rounded-full absolute top-0 right-0 overflow-hidden bg-gradient-150 from-[#e0a000] to-white shadow-lg"
                        style="
                            background-image: linear-gradient(
                                150deg,
                                var(--tw-gradient-stops)
                            );
                            box-shadow: rgb(0, 0, 0) 10px 0px 20px -15px;
                        "
                    >
                        <img
                            src="{{ asset('assets/frontend/dieu-thu/assets/images/target-4.jpg') }}"
                            alt="Phụ nữ tự chủ"
                            class="w-full h-full object-cover rounded-full"
                        />
                    </div>
                    <div
                        class="w-full h-1/2 bg-white border-0 border-r border-solid border-[#ffeda1]"
                    >
                        <div
                            class="w-full h-full bg-gradient-to-br from-[#e0a000] to-white rounded-[15px_0px_200px]"
                        ></div>
                    </div>
                </div>

                <div class="relative flex-auto">
                    <div
                        class="absolute border border-solid border-[#ffea94] w-[70px] h-[70px] transition-transform hover:rotate-0 rotate-45 bottom-0 left-1/2 -translate-x-1/2 translate-y-1/3 z-[1]"
                        style="background-image: linear-gradient(#49241c, #875a0b)"
                    ></div>

                    <div
                        class="relative z-[1] !pt-5 !pb-[92px] rounded-b-[15px] h-full bg-white border-t-0 border border-solid border-[#ffeda1] !px-5"
                    >
                        <h3
                            class="text-base lg:text-lg xl:leading-[1.6] font-sf-pro-bold text-[#875a0b] !pb-4"
                        >
                            PHỤ NỮ MUỐN TỰ CHỦ TÀI CHÍNH
                        </h3>

                        <div class="w-20 h-px bg-orange-500 mx-auto mb-4"></div>
                        <ul
                            class="text-sm lg:text-base leading-[1.4] text-black space-y-1 text-left"
                        >
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                <span>
                                    Mong muốn có nghề trong tay để
                                    <strong> tự kinh doanh – làm chủ </strong>
                                </span>
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                Không có nhiều thời gian, vướng bận con cái, gia đình
                            </li>
                            <li class="flex items-start">
                                <span
                                    class="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"
                                ></span>
                                <span>
                                    Ưu điểm: <strong> Học online linh hoạt, </strong>học lại
                                    không giới hạn
                                </span>
                            </li>
                        </ul>

                        <div
                            class="absolute left-1/2 bottom-0 -translate-x-1/2 translate-y-[20%]"
                        >
                            <div
                                class="w-15 h-15 bg-[#fde233] rounded-full flex items-center justify-center shadow-lg transform"
                            >
                                <span
                                    class="text-base xl:text-[30px] leading-[1.1] text-amber-900 font-sf-pro-bold transform"
                                >
                                    04
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom text -->
        <p
            class="mt-10 !pb-4 md:!pb-10 lg:!pb-0 lg:mt-[84px] xl:max-w-[704px] text-base lg:text-lg leading-[1.6] text-center text-black mx-auto"
        >
            Bạn là ai không quan trọng – quan trọng là bạn sẵn sàng thay đổi. MGK sẽ đồng
            hành cùng bạn từ bước đầu tiên đến khi làm chủ.
        </p>
    </div>
</section>

<section
    class="advantage relative overflow-hidden bg-cover lg:bg-fill bg-[50%_0%] bg-no-repeat"
    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/advantage-bg.jpg') }})"
>
    <!-- Background overlay -->

    <div
        class="container !px-5 md:!px-10 xl:!px-0 !py-5 md:!py-10 xl:max-w-6xl mx-auto relative z-10"
    >
        <!-- Header -->
        <div class="text-center mb-8 lg:mb-16" data-x-aos="fade-up">
            <h2
                class="text-lg md:text-2xl lg:text-3xl xl:text-[35px] xl:leading-[1.4] font-sf-pro-bold text-amber-900"
            >
                LỢI THẾ KHI HỌC TẠI MGK
            </h2>
        </div>

        <!-- Advantage Cards Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Card 1: Học tập linh hoạt -->
            <div class="relative" data-x-aos="fade-up" data-aos-delay="200">
                <div
                    class="rounded-[22px] p-5 shadow-lg h-full"
                    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }})"
                >
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div
                                class="w-15 h-15 md:w-[120px] md:h-[120px] bg-cover bg-center bg-no-repeat rounded-full flex items-center justify-center"
                                style="
                                    background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/advantage-item-bg.png') }});
                                "
                            >
                                <img
                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/advantage-1.png') }}"
                                    alt="Học tập linh hoạt"
                                    class="w-5 h-5 md:w-10 md:h-10"
                                    width="53.151px"
                                    height="35.8071px"
                                />
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3
                                class="text-lg lg:text-xl xl:text-[25px] xl:leading-[1.6] font-sf-pro-bold text-white mb-2"
                            >
                                Học tập linh hoạt
                            </h3>
                            <p class="text-white text-base leading-[1.7]">
                                Bạn không cần phải lo mình ở xa thành phố hay đi lại, bạn có
                                thể học được mọi nơi, mọi lúc với nền tảng học trực tuyến
                                hiện đại.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 2: Hỗ trợ từ giảng viên -->
            <div class="relative" data-x-aos="fade-up" data-aos-delay="400">
                <div
                    class="rounded-[22px] p-5 shadow-lg h-full"
                    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }})"
                >
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div
                                class="w-15 h-15 md:w-[120px] md:h-[120px] bg-cover bg-center bg-no-repeat rounded-full flex items-center justify-center"
                                style="
                                    background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/advantage-item-bg.png') }});
                                "
                            >
                                <img
                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/advantage-2.png') }}"
                                    alt="Học tập linh hoạt"
                                    class="h-6 md:h-12"
                                    width-="53.151px"
                                    height-="47.8071px"
                                />
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3
                                class="text-lg lg:text-xl xl:text-[25px] xl:leading-[1.6] font-sf-pro-bold text-white mb-2"
                            >
                                Hỗ trợ từ giảng viên
                            </h3>
                            <p class="text-white text-base leading-[1.7]">
                                Để hiệu quả học được tốt nhất, bạn luôn được giáo viên hỗ
                                trợ và giải đáp thắc mắc trong quá trình học.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 3: Cơ hội việc làm -->
            <div class="relative" data-x-aos="fade-up" data-aos-delay="600">
                <div
                    class="rounded-[22px] p-5 shadow-lg h-full"
                    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }})"
                >
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div
                                class="w-15 h-15 md:w-[120px] md:h-[120px] bg-cover bg-center bg-no-repeat rounded-full flex items-center justify-center"
                                style="
                                    background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/advantage-item-bg.png') }});
                                "
                            >
                                <img
                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/advantage-3.png') }}"
                                    alt="Học tập linh hoạt"
                                    class="w-6 h-6 md:w-[52px] md:h-[52px]"
                                    width="53.151px"
                                    height="52.8071px"
                                />
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3
                                class="text-lg lg:text-xl xl:text-[25px] xl:leading-[1.6] font-sf-pro-bold text-white mb-2"
                            >
                                Cơ hội việc làm
                            </h3>
                            <p class="text-white text-base leading-[1.7]">
                                Bạn sẽ được kết nối với các spa, thẩm mỹ viện để tìm kiếm cơ
                                hội việc làm sau khi hoàn thành khóa học.
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card 4: Chứng chỉ uy tín -->
            <div class="relative" data-x-aos="fade-up" data-aos-delay="800">
                <div
                    class="rounded-[22px] p-5 shadow-lg h-full"
                    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/hero-gradient.jpg') }})"
                >
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div
                                class="w-15 h-15 md:w-[120px] md:h-[120px] bg-cover bg-center bg-no-repeat rounded-full flex items-center justify-center"
                                style="
                                    background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/advantage-item-bg.png') }});
                                "
                            >
                                <img
                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/advantage-4.png') }}"
                                    alt="Chứng chỉ uy tín"
                                    class="w-5 h-6 md:w-10 md:h-12"
                                    width="40"
                                    height="49"
                                />
                            </div>
                        </div>
                        <div class="flex-1">
                            <h3
                                class="text-lg lg:text-xl xl:text-[25px] xl:leading-[1.6] font-sf-pro-bold text-white mb-2"
                            >
                                Chứng chỉ uy tín
                            </h3>
                            <p class="text-white text-base leading-[1.7]">
                                Cấp chứng chỉ chuẩn Bộ Lao động – Thương binh & Xã hội, hành
                                trang vững chắc cho sự nghiệp.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section
    id="khoa-hoc"
    class="course xl:!py-10 bg-cover lg:bg-fill relative overflow-hidden bg-[50%_0%] bg-no-repeat"
    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/course-bg.jpg') }})"
>
    <div class="container !px-4 xl:!px-0 !py-5 md:!py-10 xl:max-w-6xl mx-auto relative z-10">
        <!-- Header -->
        <div class="text-center mb-4 md:mb-8 xl:mb-16" data-x-aos="fade-up">
            <h2
                class="text-lg md:text-2xl lg:text-3xl xl:text-[35px] leading-[1.4] font-sf-pro-bold text-amber-900 mb-4"
            >
                CÁC KHÓA HỌC HIỆN CÓ
            </h2>
        </div>

        <!-- Course Cards Grid -->
        <div
            class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-x-8 gap-y-6 md:gap-y-14 xl:gap-x-8 mb-11"
        >
            @if(isset($courses) && $courses->count()>0)
                @foreach($courses as $course)
                    <a
                        href="{{ route('course.details', $course->slug) }}"
                        class="block max-w-full lg:max-w-[367px] lg:mx-auto xl:mx-0 xl:max-w-none bg-white p-2 rounded-[20px] border border-solid border-[#ffea94] overflow-hidden transform"
                        style="box-shadow: rgb(74, 37, 28) 0px 15px 20px -15px"
                        data-x-aos="fade-up"
                        data-aos-delay="200"
                    >
                        <div class="relative mb-8 md:mb-12">
                            <img
                                src="{{ get_image($course->thumbnail) }}"
                                alt="{{$course->title}}"
                                width="344.648px"
                                height="300.216px"
                                class="w-[344px] h-[300px] object-cover rounded-[20px] mx-auto"
                            />

                            <!-- Course Button -->
                            <div
                                class="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2"
                            >
                                <button
                                    class="bg-[#f9bb00] text-white !px-8 !py-2 rounded-full font-sf-pro-bold text-base md:text-[22px]"
                                >
                                    Khóa học
                                </button>
                            </div>
                        </div>
                        <h3
                            class="text-sm md:text-lg leading-[1.4] mb-4 !px-9 font-sf-pro-bold text-[#875a0b] text-center"
                        >
                            {{$course->title}}
                        </h3>
                    </a>
                @endforeach
            @endif

        </div>

        <!-- View More Button -->
        <div class="text-center" data-x-aos="fade-up" data-aos-delay="800">
            <a href="{{ route('courses') }}"
                class="bg-gradient-to-b from-[#ff9f15] to-[#e93a00] text-base md:text-lg !px-10 !py-4 text-white rounded-full font-sf-pro-bold pulse-scaled"
            >
                XEM THÊM CÁC KHÓA HỌC
            </a>
        </div>
    </div>
</section>

<section
    id="danh-gia"
    class="slider xl:!py-10 bg-cover lg:bg-fill overflow-x-hidden relative bg-[50%_0%] bg-no-repeat"
    style="background-image: url({{ asset('assets/frontend/dieu-thu/assets/images/news-bg.jpg') }})"
>
    <div class="container !px-5 md:!px-10 xl:!px-0 xl:max-w-6xl mx-auto relative z-10">
        <img
            src="{{ asset('assets/frontend/dieu-thu/assets/images/news-bg-left.png') }}"
            class="absolute w-20 h-auto lg:h-[415.211px] lg:w-[472.644px] top-0 left-0 -translate-x-[40%] -translate-y-1/3"
            width="472.644px"
            height="415.211px"
            alt=""
            srcset=""
        />

        <img
            src="{{ asset('assets/frontend/dieu-thu/assets/images/news-bg-right.png') }}"
            class="absolute top-0 right-0 translate-x-[40%]"
            width="1142px"
            height="679.761px"
            alt=""
            srcset=""
        />

        <div class="flex flex-col xl:flex-row xl:gap-10 relative z-[1]">
            <!-- Header -->
            <div class="text-left mb-8 xl:mb-16 w-full xl:w-1/3" data-x-aos="fade-right">
                <h2
                    class="text-center xl:text-left text-lg md:text-2xl lg:text-3xl xl:text-[35px] xl:leading-[1.2] xl:max-w-[400px] font-sf-pro-bold text-amber-900 xl:mb-4"
                >
                    ĐÁNH GIÁ CHẤT LƯỢNG SAU KHI HỌC CỦA HỌC VIÊN
                </h2>

                <div class="hidden xl:flex items-center gap-4">
                    <!-- Arrow left -->
                    <button class="slide-prev w-12 h-12">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="100%"
                            viewBox="0 -960 960 960"
                            width="100%"
                            preserveAspectRatio="none"
                            class=""
                            fill="#000"
                        >
                            <path
                                d="M359-242 120-481l239-239 43 43-166 166h604v60H236l166 166-43 43Z"
                            ></path>
                        </svg>
                    </button>
                    <!-- Arrow right -->
                    <button class="slide-next w-12 h-12">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            height="100%"
                            viewBox="0 -960 960 960"
                            width="100%"
                            preserveAspectRatio="none"
                            class=""
                            fill="#000"
                        >
                            <path
                                d="m560-242-43-42 168-168H160v-60h525L516-681l43-42 241 241-240 240Z"
                            ></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Slider Container -->
            <div class="relative w-full xl:w-2/3" data-x-aos="fade-left">
                <!-- Navigation Arrows -->
                <div class="absolute left-3 top-1/2 transform -translate-y-1/2 z-20">
                    <button
                        id="prevSlide"
                        class="slide-prev w-8 h-8 bg-transparent flex items-center justify-center"
                    >
                        <svg
                            class="w-full h-full text-black"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M15 19l-7-7 7-7"
                            ></path>
                        </svg>
                    </button>
                </div>
                <div class="absolute right-3 top-1/2 transform -translate-y-1/2 z-20">
                    <button
                        id="nextSlide"
                        class="slide-next w-8 h-8 bg-transparent flex items-center justify-center"
                    >
                        <svg
                            class="w-full h-full text-black"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 5l7 7-7 7"
                            ></path>
                        </svg>
                    </button>
                </div>

                <!-- Slider Content -->
                <div class="slider-wrapper overflow-hidden">
                    <div
                        class="slider-track flex transition-transform duration-500 ease-in-out h-full"
                        id="sliderTrack"
                    >
                        <!-- Slide 0 -->
                        <div class="slide w-full flex-shrink-0 h-full">
                            <div
                                class="h-full bg-white rounded-2xl border border-solid border-[#ffea94] p-4 md:p-8 mx-4"
                            >
                                <div
                                    class="flex flex-col md:flex-row items-start md:space-x-6"
                                >
                                    <!-- User Avatar -->
                                    <div
                                        class="flex-shrink-0 flex flex-col items-start md:items-center"
                                    >
                                        <div
                                            class="w-15 h-15 md:w-[126px] md:h-[126px] rounded-full mb-2"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dieu-thu/assets/images/slider-0.jpg') }}"
                                                alt="Trúc Ly"
                                                class="w-full h-full object-cover rounded-full"
                                            />
                                        </div>

                                        <!-- Star Rating -->
                                        <div class="flex items-center mb-4">
                                            <div class="flex space-x-1">
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Content -->
                                    <div class="flex-1">
                                        <h3
                                            class="text-lg xl:leading-[1.6] font-sf-pro-bold text-black"
                                        >
                                            Nguyễn Hương Thảo
                                        </h3>
                                        <p
                                            class="text-sm xl:leading-[1.6] text-[#545454] mb-2 italic"
                                        >
                                            Học Viên K63 Lớp Kỹ Năng Chăm Sóc & Bán Hàng
                                            Ngành Làm Đẹp
                                        </p>

                                        <!-- Review Text -->
                                        <p
                                            class="text-base xl:leading-[1.4] text-black leading-relaxed"
                                        >
                                            “Tôi là Nguyễn Hương Thảo, từng đầu tư hơn 2 tỷ
                                            cho spa nhưng sau 2 năm kinh doanh rơi vào bế
                                            tắc, khách đến nhưng không giữ được, nhân viên
                                            không chốt được đơn. Tôi đã muốn bỏ cuộc. May
                                            mắn được bạn giới thiệu lớp kỹ năng chăm sóc &
                                            bán hàng ngành làm đẹp tại MGK. Sau 5 tháng áp
                                            dụng, doanh số đều, khách quay lại nhiều hơn.
                                            Nói thật, nếu không học, chắc tôi đã đóng cửa
                                            spa lâu rồi.”
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Slide 1 -->
                        <div class="slide w-full flex-shrink-0 h-full">
                            <div
                                class="h-full bg-white rounded-2xl border border-solid border-[#ffea94] p-4 md:p-8 mx-4"
                            >
                                <div
                                    class="flex flex-col md:flex-row items-start md:space-x-6"
                                >
                                    <!-- User Avatar -->
                                    <div
                                        class="flex-shrink-0 flex flex-col items-start md:items-center"
                                    >
                                        <div
                                            class="w-15 h-15 md:w-[126px] md:h-[126px] rounded-full mb-2"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dieu-thu/assets/images/slider-2.jpeg') }}"
                                                alt="Minh Anh"
                                                class="w-full h-full object-cover rounded-full"
                                            />
                                        </div>
                                        <!-- Star Rating -->
                                        <div class="flex items-center mb-4">
                                            <div class="flex space-x-1">
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Content -->
                                    <div class="flex-1">
                                        <h3
                                            class="text-lg xl:leading-[1.6] font-sf-pro-bold text-black"
                                        >
                                            Minh Anh
                                        </h3>
                                        <p
                                            class="text-sm xl:leading-[1.6] text-[#545454] mb-2 italic"
                                        >
                                            Học Viên MGK Lần 8 / Chuyên Gia Thẩm Mỹ
                                        </p>

                                        <!-- Review Text -->
                                        <p
                                            class="text-base xl:leading-[1.4] text-black leading-relaxed"
                                        >
                                            Sau khi hoàn thành khóa học tại MGK, tôi đã có
                                            thể mở spa riêng và phát triển thành công. Kiến
                                            thức và kỹ năng học được rất thực tế và áp dụng
                                            được ngay vào công việc. Cảm ơn MGK đã giúp tôi
                                            thay đổi cuộc sống.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Slide 2 -->
                        <div class="slide w-full flex-shrink-0 h-full">
                            <div
                                class="h-full bg-white rounded-2xl border border-solid border-[#ffea94] p-4 md:p-8 mx-4"
                            >
                                <div
                                    class="flex flex-col md:flex-row items-start md:space-x-6"
                                >
                                    <!-- User Avatar -->
                                    <div
                                        class="flex-shrink-0 flex flex-col items-start md:items-center"
                                    >
                                        <div
                                            class="w-15 h-15 md:w-[126px] md:h-[126px] rounded-full mb-2"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dieu-thu/assets/images/slider-1.jpeg') }}"
                                                alt="Trúc Ly"
                                                class="w-full h-full object-cover rounded-full"
                                            />
                                        </div>

                                        <!-- Star Rating -->
                                        <div class="flex items-center mb-4">
                                            <div class="flex space-x-1">
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                                <img
                                                    src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                    alt="star"
                                                    class="w-5 h-6"
                                                />
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Content -->
                                    <div class="flex-1">
                                        <h3
                                            class="text-lg xl:leading-[1.6] font-sf-pro-bold text-black"
                                        >
                                            Trúc Ly
                                        </h3>
                                        <p
                                            class="text-sm xl:leading-[1.6] text-[#545454] mb-2 italic"
                                        >
                                            Học Viên K60 Lớp KTV Spa Chuẩn 12 Bước
                                        </p>

                                        <!-- Review Text -->
                                        <p
                                            class="text-base xl:leading-[1.4] text-black leading-relaxed"
                                        >
                                            Em vừa tốt nghiệp lớp 12, không đủ điều kiện học
                                            đại học nhưng không từ bỏ ước mơ. Em chọn theo
                                            học nghề spa tại MGK Edu. Nhờ chương trình KTV
                                            Spa chuẩn 12 bước, em tự tin xin việc tại viện
                                            thẩm mỹ lớn ở Sài Gòn với mức thu nhập 12
                                            triệu/tháng.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Slide 3 -->
                        <div class="slide w-full flex-shrink-0 h-full">
                            <div
                                class="h-full bg-white rounded-2xl border border-solid border-[#ffea94] p-4 md:p-8 mx-4"
                            >
                                <div
                                    class="flex flex-col md:flex-row items-start md:space-x-6"
                                >
                                    <!-- User Avatar -->
                                    <div
                                        class="flex-shrink-0 flex flex-col items-start md:items-center"
                                    >
                                        <div
                                            class="w-15 h-15 md:w-[126px] md:h-[126px] rounded-full mb-2"
                                        >
                                            <img
                                                src="{{ asset('assets/frontend/dieu-thu/assets/images/slider-3.jpg') }}"
                                                alt="Thu Hà"
                                                class="w-full h-full object-cover rounded-full"
                                            />
                                        </div>

                                        <!-- Star Rating -->
                                        <div class="flex items-center mb-4">
                                            <div class="flex items-center mb-4">
                                                <div class="flex space-x-1">
                                                    <img
                                                        src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                        alt="star"
                                                        class="w-5 h-6"
                                                    />
                                                    <img
                                                        src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                        alt="star"
                                                        class="w-5 h-6"
                                                    />
                                                    <img
                                                        src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                        alt="star"
                                                        class="w-5 h-6"
                                                    />
                                                    <img
                                                        src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                        alt="star"
                                                        class="w-5 h-6"
                                                    />
                                                    <img
                                                        src="{{ asset('assets/frontend/dieu-thu/assets/images/star.svg') }}"
                                                        alt="star"
                                                        class="w-5 h-6"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Content -->
                                    <div class="flex-1">
                                        <h3
                                            class="text-lg xl:leading-[1.6] font-sf-pro-bold text-black"
                                        >
                                            Thu Hà
                                        </h3>
                                        <p
                                            class="text-sm xl:leading-[1.6] text-[#545454] mb-2 italic"
                                        >
                                            Học Viên MGK Lần 10 / Chủ Spa Thành Công
                                        </p>

                                        <!-- Review Text -->
                                        <p
                                            class="text-base xl:leading-[1.4] text-black leading-relaxed"
                                        >
                                            Tôi đã từ một người hoàn toàn mới trong ngành
                                            làm đẹp trở thành chủ spa có thu nhập ổn định.
                                            MGK không chỉ dạy kỹ thuật mà còn hướng dẫn cách
                                            kinh doanh hiệu quả. Rất biết ơn trung tâm.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container !px-4 xl:!px-0 !py-10 xl:max-w-6xl mx-auto relative z-10">
            <!-- Header -->
            <div class="text-center mb-10" data-x-aos="fade-up">
                <h2
                    class="text-2xl lg:text-3xl xl:text-[35px] xl:leading-[1.2] font-sf-pro-bold text-amber-900"
                >
                    CÁC TIN TỨC & KIẾN THỨC
                </h2>
            </div>

            <!-- News Cards Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                
                @if(isset($blogs) && count($blogs) > 0)
                    @foreach($blogs as $key => $blog)
                        <a
                            href="{{ route('blog.details', $blog->slug) }}"
                            class="block bg-white p-4 rounded-[15px] overflow-hidden transition-shadow duration-300"
                            style="
                                background-image: linear-gradient(
                                    179965deg,
                                    rgb(253, 251, 251),
                                    rgb(255, 240, 206)
                                );
                                box-shadow: rgba(135, 90, 11, 0.4) 0px 0px 10px 0px;
                            "
                            data-x-aos="fade-up"
                            data-aos-delay="{{ ($key + 1) * 100 }}"
                        >
                            <div
                                class="relative h-[218px] border border-solid border-[#d7ad80] rounded-[7px] overflow-hidden"
                            >
                                <img
                                    src="{{ get_image($blog->thumbnail) }}"
                                    alt="Tự tay trắng da"
                                    class="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                                />
                            </div>
                            <div class="!pt-3 lg:!py-3 text-base">
                                <h3
                                    class="leading-[1.4] min-h-[70px] font-sf-pro-bold text-black leading-tight"
                                >
                                    {{ $blog->title }}
                                </h3>
                                <div
                                    class="text-[#f30d0d] leading-[1.2] font-normal mt-auto transition-colors"
                                >
                                    Đọc tiếp <i class="bi bi-arrow-right"></i>
                                </div>
                            </div>
                        </a>
                    @endforeach
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Footer -->
<footer id="lien-he" class="bg-[#49241c] text-white !py-12">
    <div class="container mx-auto !px-10 xl:!px-0 xl:max-w-6xl">
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-8">
            <!-- Company Info Column -->
            <div class="space-y-4 col-span-1 md:col-span-2">
                <!-- Logo -->
                <div class="mb-6">
                    <img
                        src="{{ asset('assets/frontend/dieu-thu/assets/images/footer-logo.png') }}"
                        alt="MGK Logo"
                        class="h-[121px] w-[121px]"
                    />
                </div>

                <!-- Company Details -->
                <div class="text-sm md:text-base leading-[2] space-y-1">
                    <p>Tên doanh nghiệp: Công ty Cổ phần Thương mại Dịch vụ KOVIBE</p>
                    <p>Tên quốc tế: KOVIBE TRADING SERVICE JOINT STOCK COMPANY</p>
                    <p>
                        Địa chỉ: Số 7 Trần Quang Diệu, Phường 14, Quận 3, Thành phố Hồ Chí
                        Minh
                    </p>
                    <p>Mã số doanh nghiệp: 0315529781</p>
                    <p>Ngành nghề đào tạo: Chăm sóc da</p>
                </div>
            </div>

            <!-- Contact Info Column -->
            <div class="space-y-4 xl:mt-20">
                <h3 class="text-lg md:text-xl md:leading-[1.6] font-sf-pro-bold mb-4">
                    LIÊN HỆ
                </h3>
                <div class="text-sm md:text-base leading-[2] space-y-2">
                    <p><strong>Hotline:</strong> 0888021168</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                </div>
            </div>

            <!-- Social Media Column -->
            <div class="space-y-4 xl:mt-20">
                <h3 class="text-lg md:text-xl md:leading-[1.6] font-sf-pro-bold mb-4">
                    KẾT NỐI VỚI CHÚNG TÔI
                </h3>

                <div class="xl:max-h-[140px]">
                    <!-- Social Media Card -->
                    <iframe
                        loading="lazy"
                        src="https://www.facebook.com/plugins/page.php?href=https%3A%2F%2Fwww.facebook.com%2Fthu.thu.26821&amp;tabs=timeline&amp;width=340&amp;height=331&amp;small_header=false&amp;adapt_container_width=true&amp;hide_cover=false&amp;show_facepile=true&amp;appId=1403160169824600"
                        width="100%"
                        height="100%"
                        style="border: none; overflow: hidden"
                        scrolling="no"
                        frameborder="0"
                        allowfullscreen="true"
                        allow="autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share"
                    ></iframe>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Floating Call Button -->
<div class="fixed bottom-6 left-6 z-50">
    <a
        href="tel:0888021168"
        class="h-10 w-10 md:w-20 md:h-20 rounded-full flex items-center justify-center outline outline-[1px] outline-[#ffbea9] animation-pulse"
        style="
            background-image: linear-gradient(
                -94deg,
                rgba(255, 177, 22, 1),
                rgba(255, 52, 0, 1)
            );
        "
    >
        <svg
            class="w-6 h-6 md:w-10 md:h-10 text-white animation-swing"
            fill="currentColor"
            viewBox="0 0 24 24"
        >
            <path
                d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"
            />
        </svg>
    </a>
</div>

@include('partials.modals.register_login')
@endsection

<header id="header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-4 col-md-4 col-lg-2 col-xl-2">
                <div id="logo">
                    <a href="/">
                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/logo.png')}}" alt="Logo">
                    </a>
                </div>
            </div>
            <div class="col-4 col-md-7 col-lg-7 col-xl-7 d-none d-md-block d-lg-block d-md-block">
                <div id="main-menu">
                    <ul>
                        <li><a href="#sec-abouts">Giới thiệu</a></li>
                        <li><a href="#sec-method">Phương pháp</a></li>
                        <li><a href="#sec-review">Cảm nhận học viên</a></li>
                        <li><a href="#sec-author">Tác gi<PERSON></a></li>
                        <li><a href="#sec-faq">FAQs</a></li>
                    </ul>
                </div>
            </div>
            <div class="col-8 col-md-8 col-lg-3 col-xl-3">
                @include("menu-profile-course")
            </div>
        </div>
    </div>
</header>

<main id="main">
    <section class="section sec-banner">
        <div class="banner-container">
            <div class="container">
                <div class="row">
                    <div class="col-12 col-md-7 col-lg-9 col-xl-7">
                        <div class="banner-text">
                            <h3>Chinh Phục Tiếng Anh <br> Nhanh Gấp Bội qua <span
                                    style="color: rgb(255, 233, 92);">Gốc Từ,</span><br><span
                                    style="color: rgb(255, 233, 92);">Câu Chuyện,</span> và <span
                                    style="color: rgb(255, 233, 92);">Hình Ảnh</span><br>
                            </h3>
                            <h4>- Phương pháp học Từ vựng độc quyền tại Đậu Xanh -&nbsp;</h4>
                            <div class="banner-list">
                                <div class="banner-list-left nap">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                         preserveAspectRatio="none" viewBox="0 0 1416.54 1896.08" class=""
                                         fill="rgba(255, 255, 255, 1)">
                                        <use xlink:href="#shape_AncYUuInWN"></use>
                                    </svg>
                                </div>
                                <div class="banner-list-right nap">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                         preserveAspectRatio="none" viewBox="0 0 1416.54 1896.08" class=""
                                         fill="rgba(255, 255, 255, 1)">
                                        <use xlink:href="#shape_AncYUuInWN"></use>
                                    </svg>
                                </div>
                                <ul>
                                    <li>Bổ sung cấp tốc <span style="font-weight: bold;">5000+</span> từ vựng độ khó từ
                                        trung bình
                                    </li>
                                    <li>Ghi Nhớ Lâu Hơn Gấp <span style="font-weight: bold;">5 - 10 lần</span></li>
                                    <li>Hiểu bản chất, giải quyết vấn đề học trước quên sau</li>
                                    <li>Tiết kiệm đáng kể&nbsp;<span style="font-weight: bold;">thời gian</span> ôn tập
                                    </li>
                                    <li><span style="font-weight: bold;">Nền Tảng Vàng</span> Chinh Phục Mọi Kỳ Thi
                                        (IELTS, TOEIC,
                                        VSTEP)
                                    </li>
                                </ul>
                            </div>
                            <div class="banner-plain">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 1792.0013 1896.0833" class=""
                                     fill="rgba(255, 255, 255, 1)">
                                    <path d="M1764 11q33 24 27 64l-256 1536q-5 29-32 45-14 8-31 8-11 0-24-5l-453-185-242 295q-18 23-49 23-13 0-22-4-19-7-30.5-23.5T640 1728v-349l864-1059-1069 925-395-162q-37-14-40-55-2-40 32-59L1696 9q15-9 32-9 20 0 36 11z"></path>
                                </svg>
                            </div>
                            <div class="banner-net">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/net-dut.png')}}" alt="">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="banner-main">
            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/ngoc-anh-nen.png')}}" alt="">
        </div>
        <div class="sec-down">
            <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                 preserveAspectRatio="none" class="" fill="rgba(1, 133, 197, 1)">
                <use xlink:href="#shape_fIYeneYUPl"></use>
            </svg>
        </div>
        <div class="banner-dot1">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                 viewBox="0 0 195.9 196.2" class="" fill="rgba(82, 196, 251, 1.0)">
                <use xlink:href="#shape_cenMemItpV"></use>
            </svg>
        </div>
        <div class="banner-cloud1">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                 viewBox="0 0 249.8 79.6" class="" fill="url(&quot;#SHAPE72_desktop_gradient&quot;)">
                <defs id="SHAPE72_defs">
                    <linearGradient id="SHAPE72_desktop_gradient" gradientTransform="rotate(90)">
                        <stop offset="0%" stop-color="rgba(215, 239, 255, 1)"></stop>
                        <stop offset="100%" stop-color="#70D2FF"></stop>
                    </linearGradient>
                </defs>
                <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
            </svg>
        </div>
        <div class="banner-cloud2">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                 viewBox="0 0 249.8 79.6" class="" fill="url(&quot;#SHAPE79_desktop_gradient&quot;)">
                <defs id="SHAPE79_defs">
                    <linearGradient id="SHAPE79_desktop_gradient" gradientTransform="rotate(90)">
                        <stop offset="0%" stop-color="rgba(255, 255, 255, 1)"></stop>
                        <stop offset="100%" stop-color="#AEE6FF"></stop>
                    </linearGradient>
                </defs>
                <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
            </svg>
        </div>
        <div class="banner-cloud3">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                 viewBox="0 0 249.8 79.6" class="" fill="url(&quot;#SHAPE77_desktop_gradient&quot;)">
                <defs id="SHAPE77_defs">
                    <linearGradient id="SHAPE77_desktop_gradient" gradientTransform="rotate(90)">
                        <stop offset="0%" stop-color="rgba(255, 255, 255, 1)"></stop>
                        <stop offset="100%" stop-color="#AEE6FF"></stop>
                    </linearGradient>
                </defs>
                <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
            </svg>
        </div>
        <div class="banner-mattroi">
            <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 -960 960 960" width="100%"
                 preserveAspectRatio="none" class="" fill="rgba(255, 255, 255, 1)">
                <path d="M450-770v-150h60v150h-60Zm256 106-42-42 106-107 42 43-106 106Zm64 214v-60h150v60H770ZM450-40v-150h60v150h-60ZM253-665 148-770l42-42 106 106-43 41Zm518 517L664-254l41-41 108 104-42 43ZM40-450v-60h150v60H40Zm151 302-43-42 105-105 22 20 22 21-106 106Zm289-92q-100 0-170-70t-70-170q0-100 70-170t170-70q100 0 170 70t70 170q0 100-70 170t-170 70Zm0-60q75 0 127.5-52.5T660-480q0-75-52.5-127.5T480-660q-75 0-127.5 52.5T300-480q0 75 52.5 127.5T480-300Zm0-180Z"></path>
            </svg>
        </div>
        <div class="banner-dot2">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                 viewBox="0 0 195.9 196.2" class="" fill="rgba(24, 158, 222, 1.0)">
                <use xlink:href="#shape_cenMemItpV"></use>
            </svg>
        </div>
    </section><!--sec-banner-->

    <section class="section sec-abouts" id="sec-abouts">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12 col-md-11 col-lg-11 col-xl-11">
                    <div class="author-story">
                        <div class="story-right">Câu chuyện từ tác giả</div>

                        <div class="box-author">
                            <div class="author-left">
                                <div class="row align-items-end">
                                    <div class="col-12 col-md-8 col-lg-8">
                                        <div class="author-content author-right">
                                            <div class="author-faq">
                                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/dau-xanh-24-faq.png')}}" alt="">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-4 col-lg-4">
                                        <div class="author-thumbnail">
                                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/truong.webp')}}" alt="">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-11 col-lg-11 col-xl-11">
                    <div class="about-sleep">
                        <div class="about-group">
                            <h3 class="ladi-headline2">KHÔNG BIẾT BẠN CÓ TỪNG GIỐNG MÌNH ...<br>... TỪNG RẤT SỢ HỌC
                                TIẾNG ANH<br></h3>
                            <div class="ladi-line line-1">
                                <div class="ladi-line-container"></div>
                            </div>
                            <div class="ladi-line line-2">
                                <div class="ladi-line-container"></div>
                            </div>
                        </div>
                        <h3 class="ladi-headline">Hết cấp 3, mình gần như không biết gì ngoài ngữ pháp khô khan và <span
                                style="color: rgb(234, 46, 8);">từ vựng thì cứ học trước quên sau.</span><br></h3>
                        <div class="row align-items-center">
                            <div class="col-12 col-md-3 col-lg-3 col-xl-3">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/ngu-min.png')}}" alt="">
                            </div>
                            <div class="col-12 col-md-9 col-lg-9 col-xl-9">
                                <h3 class="about-sleep-text">Suốt 12 năm học, mỗi lần đến kỳ thi, mình lại cắm đầu cắm
                                    cổ nhồi nhét, học lấy học để – rồi y như rằng lại <span style="font-weight: bold;">quên sạch</span>.
                                    <br>Kết quả? Mình được hẳn <span style="font-weight: bold; color: rgb(234, 46, 8);">3.75 điểm</span>
                                    trong kỳ thi Tốt nghiệp THPT 😎<br>_____________________________________<br></h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-11 col-lg-11 col-xl-11">
                    <div class="about-alert alert-2">
                        <div class="about-text about-text-left">
                            Nhưng mọi thứ thay đổi khi mình biết đến <span
                                style="font-weight: bold; color: rgb(234, 46, 8);">Gốc từ</span> – những <span
                                style="font-weight: bold;">"mảnh ghép cốt lõi"</span> tạo nên hàng ngàn từ vựng tiếng
                            Anh. <br>Tự nhiên mình hiểu từ vựng một cách <span
                                style="font-weight: bold;">logic hơn</span>, và có thể ghi nhớ chúng một cách <span
                                style="font-weight: bold;">dễ dàng.<br></span>--------------------------<br>Thêm vào đó,
                            mình cũng nhận thấy rằng…
                            <br>Khi kết hợp <span style="font-weight: bold; color: rgb(234, 46, 8);">Gốc từ</span> với
                            các phương pháp ghi nhớ thông minh khác - như <span style="font-weight: bold;"><span
                                    style="color: rgb(234, 46, 8);">Logic, Hình ảnh,</span> </span>và <span
                                style="font-weight: bold; color: rgb(234, 46, 8);">Câu chuyện,</span> tốc độ học của
                            chúng ta sẽ có thể <span style="font-weight: bold; color: rgb(234, 46, 8);">tăng lên gấp nhiều lần.</span>
                        </div>
                        <div class="about-thumbnail">
                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/manh-ghep.png')}}" alt="" class="about-icon">
                            <div class="about-svg">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 249.8 79.6" class="" fill="#FF9C20">
                                    <use xlink:href="#shape_KGYQaiKuwp"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="about-alert">
                        <div class="about-thumbnail d-none d-md-block d-lg-block d-xl-block">
                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/mcztm.png')}}" alt="" class="about-icon">
                            <div class="about-cer">
                                <a href="{{ asset('assets/frontend/dau-xanh/assets/images/truong-ielts-20241215094206-lytd2.webp')}}" data-fancybox="gallery">
                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/z4zvm.png')}}" alt="">
                                </a><!--thay-doi-->
                            </div>
                            <div class="about-svg">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 249.8 79.6" class="" fill="#0694D8">
                                    <use xlink:href="#shape_KGYQaiKuwp"></use>
                                </svg>
                            </div>
                        </div><!--them-->
                        <div class="about-text">
                            Nhờ cách học này, mình đã đạt <span style="font-weight: bold; color: rgb(234, 46, 8);">8.0 IELTS,</span>
                            từng tham gia <span style="font-weight: bold;">phiên dịch</span> tại hội nghị Y khoa quốc
                            tế, nhận <span style="font-weight: bold;">học bổng</span> nước ngoài, và quan trọng nhất là
                            <span style="font-weight: bold;">giúp được rất nhiều người khác</span> vượt qua nỗi sợ từ
                            vựng giống mình.
                        </div>
                        <div class="about-thumbnail d-block d-md-none d-lg-none d-xl-none">
                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/mcztm.png')}}" alt="" class="about-icon">
                            <div class="about-cer">
                                <a href="{{ asset('assets/frontend/dau-xanh/assets/images/truong-ielts-20241215094206-lytd2.webp')}}" data-fancybox="gallery">
                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/z4zvm.png')}}" alt="">
                                </a><!--thay-doi-->
                            </div>
                            <div class="about-svg">
                                <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                     preserveAspectRatio="none" viewBox="0 0 249.8 79.6" class="" fill="#0694D8">
                                    <use xlink:href="#shape_KGYQaiKuwp"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-12 col-md-11 col-lg-11 col-xl-11">
                    <div class="abouts-content">Vì vậy, mình đã tạo ra khóa học này ...<br>– không chỉ để bạn học từ
                        vựng, mà còn để giúp bạn thay đổi <span style="font-weight: bold; color: rgb(234, 46, 8);">tư duy</span>
                        và <span style="color: rgb(234, 46, 8); font-weight: bold;">cảm xúc</span> khi học Tiếng Anh:
                        <br>✌️ <span style="font-weight: bold;">Không </span>còn áp lực.
                        <br>✌️ <span style="font-weight: bold;">Không </span>còn chán nản.
                        <br>✌️ Chỉ còn lại sự <span
                            style="font-weight: bold; color: rgb(234, 46, 8);">tò mò, háo hức,</span> và rất nhiều
                        <span style="font-weight: bold; color: rgb(234, 46, 8);">bất ngờ</span> 😉
                        <br>
                        --------------------------------<br>Sau đây là một video ngắn minh họa những điều mình vừa nói:
                        <br>Bạn hãy xem và cảm nhận nha ☺️ ☺️ ☺
                    </div>
                    <div class="abouts-video">
                        <div class="row">
                            <div class="col-12 col-md-12 col-lg-12">
                                <iframe class="iframe-video-preload" data-video-type="youtube"
                                        data-autoplay="true"
                                        style=" width: 100%; height: 575px; top: 0; left: 0;border-radius: 20px"
                                        frameborder="0"
                                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                        allowfullscreen="" referrerpolicy="strict-origin-when-cross-origin"
                                        title="VOCATION"
                                        width="640" height="360"
                                        src="https://www.youtube.com/embed/7lc7zUvfRKk?rel=0&amp;modestbranding=0&amp;playsinline=1&amp;controls=1&amp;enablejsapi=1&amp;origin=https%3A%2F%2Fhoctienganh.dauxanh.edu.vn&amp;widgetid=1&amp;forigin=https%3A%2F%2Fhoctienganh.dauxanh.edu.vn%2Ftienganh&amp;aoriginsup=1&amp;vf=1"></iframe>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 col-md-7 col-mlg-7 col-xl-7">
                            <div class="abouts-desc">
                                Nếu bạn từng mất gốc, từng học mãi không vào, hoặc đang ôn thi gấp,<br>– đây chính là
                                <span
                                    style="font-weight: bold; color: rgb(234, 46, 8);">"lối đi tắt"</span> để bạn
                                giỏi từ vựng
                                mà
                                không cần học vẹt.
                            </div>
                        </div>
                        <div class="col-12 col-md-5 col-mlg-5 col-xl-5">
                            <div class="dx text-center">
                                <div class="register-link">
                                    @if(!$enrollment_status)
                                        <a data-bs-toggle="modal"
                                           data-bs-target="#modal-regis">
                                            <div class="register-link-icon ">
                                                <img
                                                    src="{{ asset('assets/frontend/dau-xanh/assets/images/qar3.png') }}"
                                                    alt="">
                                            </div>
                                            <div class="register-link-title red">
                                                ĐĂNG KÝ NGAY
                                            </div>
                                        </a>
                                    @else
                                        <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}">
                                            <div class="register-link-icon ">
                                                <img
                                                    src="{{ asset('assets/frontend/dau-xanh/assets/images/qar3.png') }}"
                                                    alt="">
                                            </div>
                                            <div class="register-link-title red">
                                                VÀO HỌC NGAY
                                            </div>
                                        </a>
                                    @endif
                                </div>
                                <div class="mui-ten">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                         preserveAspectRatio="none" viewBox="0 0 300 300" fill="rgba(82, 196, 251, 1)"
                                         class="">
                                        <path d="M266.7,83.6c-7.5,0.4-16.8,1.5-27.2,4.4c-4,1.2-8.3,2.6-12.6,4.4c-4.3-3.1-9-5.5-13.7-7.4c-8.2-3.3-16.7-5.1-25.2-6.1c-8.5-1-16.9-1.1-25.2-0.8c-16.7,0.8-32.8,5.2-46.9,12.2c-14.1,6.9-26.2,16.1-36.5,25.9c-10.3,9.8-18.7,20.2-25.8,30.3c-14.2,20.1-23,38.7-28.6,51.8c-1.7,3.9-3,7.4-4.1,10.3l-2.5,1.8l1.9-13.9c3.4-25,2.2-39.1,2.2-39.1s-4.9,13.2-8.3,38.2l-3.8,28l6.9-4.9c-0.1,0.3-0.1,0.4-0.1,0.4c0,0,0.1-0.1,0.2-0.4l2.4-1.7l9.2-6.6l21.9-15.6c20.6-14.7,30-25.2,30-25.2s-13,5.5-33.5,20.2l-21.9,15.6l-3.2,2.3c1.2-2.5,2.6-5.4,4.2-8.7c6.4-12.6,16.4-30.4,31.4-49.2c7.5-9.4,16.3-19,26.6-27.8c10.3-8.8,22.2-16.8,35.5-22.6c13.3-5.8,28-9.1,43.1-9.1c15.4,0,31.3,1.8,44.6,7.6c1.7,0.7,3.3,1.5,4.8,2.4c-2,1.3-4,2.7-6,4.2c-5.5,4.3-10.8,9.6-15,16.2c-4.2,6.6-7.6,14.5-8.2,23.5c-0.3,4.5,0.2,9.3,1.9,14c1.7,4.7,4.9,9.4,9.2,12.6c4.3,3.3,9.8,5.1,14.9,5.3c5,0.3,10.4-0.6,15-2.7c9.3-4.3,15.8-12.1,19.6-20.6c3.8-8.6,5.2-18.1,3.8-27.5c-1.2-7.9-4.5-15.6-9.3-21.9c1.8-0.8,3.5-1.5,5.3-2.1c9.1-3.4,17.4-5.4,24.4-7c13.9-3,22.3-4.5,22.3-6.9C290.2,85.1,281.8,83,266.7,83.6z M229.5,145.8c-2.6,5.7-6.9,10.4-11.7,12.5c-2.4,1.1-4.8,1.5-7.3,1.4c-2.5-0.2-4.4-0.8-6.1-2c-1.7-1.3-2.9-2.9-3.8-5.1c-0.8-2.2-1.2-4.8-1.1-7.5c0.2-5.4,2.3-10.9,5.3-15.8c3-4.9,6.9-9.1,11.2-12.8c2.8-2.4,5.8-4.5,8.8-6.4c4.1,4.9,6.7,10.7,7.5,16.9C233.2,133.4,232.2,140.1,229.5,145.8z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="abouts-cloud cloud1">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                 viewBox="0 0 249.8 79.6" class="" fill="url(&quot;#SHAPE156_desktop_gradient&quot;)">
                <defs id="SHAPE156_defs">
                    <linearGradient id="SHAPE156_desktop_gradient" gradientTransform="rotate(90)">
                        <stop offset="0%" stop-color="rgba(215, 239, 255, 1)"></stop>
                        <stop offset="100%" stop-color="#70D2FF"></stop>
                    </linearGradient>
                </defs>
                <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
            </svg>
        </div>
        <div class="cloud2">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                 viewBox="0 0 249.8 79.6" class="" fill="url(&quot;#SHAPE157_desktop_gradient&quot;)">
                <defs id="SHAPE157_defs">
                    <linearGradient id="SHAPE157_desktop_gradient" gradientTransform="rotate(90)">
                        <stop offset="0%" stop-color="rgba(255, 255, 255, 1)"></stop>
                        <stop offset="100%" stop-color="#AEE6FF"></stop>
                    </linearGradient>
                </defs>
                <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
            </svg>
        </div>
    </section><!--sec-abouts-->

    <section class="section sec-whom" id="sec-whom">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="home-title text-center">
                        <h3 class="font-46" style="color:#fff;">Khóa học này dành cho ai</h3>
                    </div>
                </div>
            </div>
            <div class="row align-items-end">
                <div class="col-12 col-md-9 col-lg-9 col-xl-9">
                    <div class="whom-heading">Khóa học này phù hợp với tất cả những bạn:</div>
                    <div class="whom-group">
                        <div class="whom-check">
                            <i class="fa-sharp fa-regular fa-circle-check"></i>
                        </div>
                        <h3 class="whom-heading">Muốn <span style="color: rgb(234, 46, 8);">học nhanh, nhớ lâu</span> từ
                            vựng nhưng chưa tìm được phương pháp phù hợp.<br></h3>
                        <div class="whom-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 -960 960 960" width="100%"
                                 preserveAspectRatio="none" class="" fill="rgba(1, 133, 197, 1)">
                                <path d="M490.74-349Q557-349 603-391.08T649-493q0-52-33.58-89T534-619q-42 0-72 27.5t-30 66.61q0 16.89 6.5 32.89t19.5 30l44-42q-5-3.5-7.5-8.75T492-524q0-14 11-24t31-10q23 0 39.5 18.5T590-492q0 35.19-28 59.59Q534-408 492-408q-51.2 0-86.6-40.5Q370-489 370-548.58 370-580 381.5-608t33.5-50l-43-43q-30 29-46.5 68.28-16.5 39.29-16.5 82.4 0 84.32 53.01 142.82T490.74-349ZM240-80v-172q-57-52-88.5-121.5T120-520q0-150 105-255t255-105q125 0 221.5 73.5T827-615l55 218q4 14-5 25.5T853-360h-93v140q0 24.75-17.62 42.37Q724.75-160 700-160H600v80h-60v-140h160v-200h114l-45-180q-24-97-105-158.5T480-820q-125 0-212.5 86.5T180-522.46q0 64.42 26.32 122.39Q232.65-342.09 281-297l19 18v199h-60Zm257-370Z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="whom-group">
                        <div class="whom-check">
                            <i class="fa-sharp fa-regular fa-circle-check"></i>
                        </div>
                        <h3 class="whom-heading"><span style="font-weight: bold;">Muốn <span
                                    style="color: rgb(234, 46, 8);">rút ngắn</span> thời gian ôn thi các loại chứng chỉ như TOEIC, VSTEP, IETLS, <span
                                    style="color: rgb(234, 46, 8);">tăng nhanh band điểm.</span></span></h3>
                        <div class="whom-icon">
                            <i class="fa-solid fa-rocket-launch"></i>
                        </div>
                    </div>
                    <div class="whom-group">
                        <div class="whom-check">
                            <i class="fa-sharp fa-regular fa-circle-check"></i>
                        </div>
                        <h3 class="whom-heading"><span style="color: rgb(234, 46, 8);">Thiếu vốn từ vựng,</span> gặp khó
                            khăn khi đọc hiểu văn bản học thuật, và mong muốn nâng cao khả năng lĩnh hội kiến thức bằng
                            Tiếng Anh.</h3>
                        <div class="whom-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                 preserveAspectRatio="none" viewBox="0 0 24 24" class="" fill="rgba(1, 133, 197, 1)">
                                <path d="M11,19V9A2,2 0 0,0 9,7H5V17H9A2,2 0 0,1 11,19M13,9V19A2,2 0 0,1 15,17H19V7H15A2,2 0 0,0 13,9M21,19H15A2,2 0 0,0 13,21H11A2,2 0 0,0 9,19H3V5H9A2,2 0 0,1 11,7H13A2,2 0 0,1 15,5H21V19Z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="whom-group">
                        <div class="whom-check">
                            <i class="fa-sharp fa-regular fa-circle-check"></i>
                        </div>
                        <h3 class="whom-heading">Muốn tiếp cận việc học Tiếng Anh theo 1 cách thức <span
                                style="color: rgb(234, 46, 8);">mới mẻ, thú vị, khoa học, và logic.</span></h3>
                        <div class="whom-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                 preserveAspectRatio="none" viewBox="0 0 1920 1896.0833" class=""
                                 fill="rgba(1, 133, 197, 1)">
                                <path d="M896 896q0-106-75-181t-181-75-181 75-75 181 75 181 181 75 181-75 75-181zm768 512q0-52-38-90t-90-38-90 38-38 90q0 53 37.5 90.5t90.5 37.5 90.5-37.5 37.5-90.5zm0-1024q0-52-38-90t-90-38-90 38-38 90q0 53 37.5 90.5T1536 512t90.5-37.5T1664 384zm-384 421v185q0 10-7 19.5t-16 10.5l-155 24q-11 35-32 76 34 48 90 115 7 10 7 20 0 12-7 19-23 30-82.5 89.5T999 1423q-11 0-21-7l-115-90q-37 19-77 31-11 108-23 155-7 24-30 24H547q-11 0-20-7.5t-10-17.5l-23-153q-34-10-75-31l-118 89q-7 7-20 7-11 0-21-8-144-133-144-160 0-9 7-19 10-14 41-53t47-61q-23-44-35-82l-152-24q-10-1-17-9.5T0 987V802q0-10 7-19.5T23 772l155-24q11-35 32-76-34-48-90-115-7-11-7-20 0-12 7-20 22-30 82-89t79-59q11 0 21 7l115 90q34-18 77-32 11-108 23-154 7-24 30-24h186q11 0 20 7.5t10 17.5l23 153q34 10 75 31l118-89q8-7 20-7 11 0 21 8 144 133 144 160 0 9-7 19-12 16-42 54t-45 60q23 48 34 82l152 23q10 2 17 10.5t7 19.5zm640 533v140q0 16-149 31-12 27-30 52 51 113 51 138 0 4-4 7-122 71-124 71-8 0-46-47t-52-68q-20 2-30 2t-30-2q-14 21-52 68t-46 47q-2 0-124-71-4-3-4-7 0-25 51-138-18-25-30-52-149-15-149-31v-140q0-16 149-31 13-29 30-52-51-113-51-138 0-4 4-7 4-2 35-20t59-34 30-16q8 0 46 46.5t52 67.5q20-2 30-2t30 2q51-71 92-112l6-2q4 0 124 70 4 3 4 7 0 25-51 138 17 23 30 52 149 15 149 31zm0-1024v140q0 16-149 31-12 27-30 52 51 113 51 138 0 4-4 7-122 71-124 71-8 0-46-47t-52-68q-20 2-30 2t-30-2q-14 21-52 68t-46 47q-2 0-124-71-4-3-4-7 0-25 51-138-18-25-30-52-149-15-149-31V314q0-16 149-31 13-29 30-52-51-113-51-138 0-4 4-7 4-2 35-20t59-34 30-16q8 0 46 46.5t52 67.5q20-2 30-2t30 2q51-71 92-112l6-2q4 0 124 70 4 3 4 7 0 25-51 138 17 23 30 52 149 15 149 31z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-3 col-lg-3 col-xl-3">
                    <div class="whomLeft d-block d-lg-none d-xl-none">
                        <div class="whom-run">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                 preserveAspectRatio="none" viewBox="0 0 24 24" class="" fill="rgba(82, 196, 251, 1)">
                                <path d="M7.5,2C5.71,3.15 4.5,5.18 4.5,7.5C4.5,9.82 5.71,11.85 7.53,13C4.46,13 2,10.54 2,7.5A5.5,5.5 0 0,1 7.5,2M19.07,3.5L20.5,4.93L4.93,20.5L3.5,19.07L19.07,3.5M12.89,5.93L11.41,5L9.97,6L10.39,4.3L9,3.24L10.75,3.12L11.33,1.47L12,3.1L13.73,3.13L12.38,4.26L12.89,5.93M9.59,9.54L8.43,8.81L7.31,9.59L7.65,8.27L6.56,7.44L7.92,7.35L8.37,6.06L8.88,7.33L10.24,7.36L9.19,8.23L9.59,9.54M19,13.5A5.5,5.5 0 0,1 13.5,19C12.28,19 11.15,18.6 10.24,17.93L17.93,10.24C18.6,11.15 19,12.28 19,13.5M14.6,20.08L17.37,18.93L17.13,22.28L14.6,20.08M18.93,17.38L20.08,14.61L22.28,17.15L18.93,17.38M20.08,12.42L18.94,9.64L22.28,9.88L20.08,12.42M9.63,18.93L12.4,20.08L9.87,22.27L9.63,18.93Z"></path>
                            </svg>
                        </div>
                        <div class="d-flex justify-content-center position-whome">
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(237, 246, 255, 1)">
                                    <use xlink:href="#shape_AqcMzAAcfQ"></use>
                                </svg>
                            </div>
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(237, 246, 255, 1)">
                                    <use xlink:href="#shape_AqcMzAAcfQ"></use>
                                </svg>
                            </div>
                            <div class="line-down">
                                <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                                     preserveAspectRatio="none" class="" fill="rgba(237, 246, 255, 1)">
                                    <use xlink:href="#shape_AqcMzAAcfQ"></use>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="whom-a"><img src="{{ asset('assets/frontend/dau-xanh/assets/images/ngoc-5.webp')}}" alt=""></div>
                </div>
            </div>
        </div>
    </section><!--sec-whom-->

    <section class="section sec-document" id="sec-document">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="home-title text-center">
                        <h3 class="font-46">Khóa học này sẽ giúp bạn</h3>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-md-6 col-lg-6 col-xl-6">
                    <div class="document-item">
                        <div class="document-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                 preserveAspectRatio="none" viewBox="0 0 24 24" class="" fill="rgb(255, 225, 38)">
                                <use xlink:href="#shape_EvEherdIen"></use>
                            </svg>
                        </div>
                        <div class="document-text">
                            <h4>Ghi nhớ từ vựng dễ hơn gấp 5-10 lần</h4>
                            <p>Phương pháp học được phát triển dựa trên nền tảng <span style="font-weight: bold;">khoa học trí nhớ</span>,
                                giúp từ vựng bám rễ vào bộ nhớ một cách tự nhiên.</p>
                        </div>
                    </div>
                    <div class="document-item">
                        <div class="document-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                 preserveAspectRatio="none" viewBox="0 0 2048 1896.0833" class=""
                                 fill="rgb(255, 225, 38)">
                                <path d="M2048 1536v128H0V128h128v1408h1920zM1920 288v435q0 21-19.5 29.5T1865 745l-121-121-633 633q-10 10-23 10t-23-10l-233-233-416 416-192-192 585-585q10-10 23-10t23 10l233 233 464-464-121-121q-16-16-7.5-35.5T1453 256h435q14 0 23 9t9 23z"></path>
                            </svg>
                        </div>
                        <div class="document-text">
                            <h4>Bứt phá điểm số</h4>
                            <p>Nạp nhanh chóng <span style="font-weight: bold;">5000+ từ vựng</span> có độ khó từ trung
                                bình, thường gặp trong đề TOEIC, VSTEP, IELTS - giúp bạn tự tin <span
                                    style="font-weight: bold;">bứt phá band điểm.</span></p>
                        </div>
                    </div>
                    <div class="document-item">
                        <div class="document-icon">
                            <i class="fa-solid fa-hourglass"></i>
                        </div>
                        <div class="document-text">
                            <h4>Học nhanh, nhớ lâu, tiết kiệm thời gian</h4>
                            <p><b>Rút ngắn đáng kể</b> thời gian ghi nhớ từ vựng, tránh lãng phí nhiều tháng học vẹt mà
                                không hiệu quả.</p>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-6 col-xl-6">
                    <div class="document-item">
                        <div class="document-icon">
                            <i class="fa-solid fa-arrows-rotate"></i>
                        </div>
                        <div class="document-text">
                            <h4>Cập nhật liên tục, học trọn đời</h4>
                            <p>Chỉ cần đăng ký một lần, bạn được truy cập toàn bộ nội dung hiện tại lẫn các bài học mới
                                trong tương lai – <b>không phát sinh chi phí.</b></p>
                        </div>
                    </div>
                    <div class="document-item">
                        <div class="document-icon">
                            <i class="fa-solid fa-heart"></i>
                        </div>
                        <div class="document-text">
                            <h4>Hỗ trợ tận tình</h4>
                            <p>Tất cả những câu hỏi của bạn đều được chúng mình lắng nghe và giải đáp 24/7.</p>
                        </div>
                    </div>

                    <div class="dx document-dx">
                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/dx-ngoi-ghe.png')}}" alt="">
                        <div class="register-link">
                            @if(!$enrollment_status)
                                <a data-bs-toggle="modal"
                                   data-bs-target="#modal-regis">
                                    <div class="register-link-icon ">
                                        <img
                                            src="{{ asset('assets/frontend/dau-xanh/assets/images/qar3.png') }}"
                                            alt="">
                                    </div>
                                    <div class="register-link-title red">
                                        ĐĂNG KÝ NGAY
                                    </div>
                                </a>
                            @else
                                <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}">
                                    <div class="register-link-icon ">
                                        <img
                                            src="{{ asset('assets/frontend/dau-xanh/assets/images/qar3.png') }}"
                                            alt="">
                                    </div>
                                    <div class="register-link-title red">
                                        VÀO HỌC NGAY
                                    </div>
                                </a>
                            @endif
                        </div>
                        <div class="document-muiten">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                 preserveAspectRatio="none" viewBox="0 0 300 300" fill="rgba(1, 116, 172, 1)" class="">
                                <path d="M289.8,140.2c-0.1-1.3,0.1-2.7-0.4-4c-1.1,0-2-1-2.6-2.4c-0.7,0.1-1.5-0.5-2.4-1.2c-1.2,0.8-2.6,0.5-3.8,1.6c-1.7,3-4.1,1.9-6,3.2c-4-0.6-7.8-0.5-11.7-0.8c-1.2-0.7-2.2-0.4-3.3-0.7c-0.6-0.5-1.1-0.4-1.6,0c-1.2-1.2-2.1-0.1-3.3-0.7c-0.9,0.4-2-0.7-2.9-0.5c-3.5-1-5.4-3.5-8.9-4.2c0.1-0.2,0.2-0.5-0.1-0.6c-2.1,0.6-3.2,1.3-4.5,3.3c0.1,0.1,0.2,0.2,0.4,0.2c-1.6,1.8-0.5,3.2-0.8,4.8c-0.1,0.1-0.2,0.2-0.2,0.4c-0.3,0.4-0.5,0.7-0.8,1.1c-1.9,1.1-1.4,3.5-1,5.2c-0.1,1.1-0.3,3.3,1.5,3.2c-0.2,2.7,3.7,3.7,5.6,3.5c1,0.2,1.9,0.3,2.9,0.5c0.1,0.1,0.2,0.2,0.4,0.3c0.5,0.1,1.1,0.2,1.6,0c0.2,0.2,0.5,0.3,0.7,0.5c0.3-0.5,0.9,0.1,1.2-0.2c2.1,1.3,3.8,0.3,6,2.1c1.1,1.8-2.4,1.1-3.4,2.4c-4.1,0.9-8.2,2.5-12.2,3.6c-1.4,0.3-2.7,0.7-4,1.3c1.1,0.1,2.1-0.2,3-0.7c-1,0.5-2,0.8-3,0.7l0,0c-0.1,0-0.1,0-0.2,0c0,0,0,0.1-0.1,0.1c-0.3,0.1-0.5,0.2-0.8,0.4c-1.5,0.1-3.1,0.1-4.5,1.1c-0.6-0.5-1.1-0.4-1.6,0c-1.5,0-2.9,0.2-4.3,0.7c-0.2-0.1-0.5-0.2-0.6,0.1c-0.9-0.7-1.4,0.2-2.2,0.1c-1.1-0.8-1.9,0.1-2.9-0.5c-2.2-0.4-4.4-1.1-6.5-1.4c-0.1-0.9-2-2.2-2.8-2c0-0.9-1.7-1.6-2-2.5c-2.8-1.9-1.9-4.5-3.9-6.6c-0.3-0.5-0.4-1,0-1.6c-2.5-2.4,0.1-5.7,0.7-8.6c-0.7-1.7,0.4-3.6,0-5.3c0.5-0.9,0.7-1.8,0.2-2.5c0.1-0.1,0.2-0.2,0.3-0.4c-0.3-0.7-1.2-1.3-0.1-2.2c-0.6-0.4-1.2-0.8-1.8-1.3c-1.3-2.5-3.9-3.2-6.3-4c-1.1-0.9-1.9,0.1-3-1.1c-0.1,0.1-0.2,0.2-0.3,0.4c-0.5-0.1-1.1-0.2-1.6-0.1c-1.6-1.1-2.8-0.7-4.1-0.2c-8.9-0.7-17.3,1.5-25.4,5.5c-0.3-0.1-0.6,0-0.9,0.5c-1.1-0.6-2,0.6-2.9,1.1c-0.1,0.1-0.2,0.2-0.3,0.4c-0.6-0.3-1,0.5-1.5,0.6c-0.1,0.1-0.2,0.2-0.3,0.4c-10.6,5.3-17.2,14.6-27,23.7c-0.1,0.1-0.2,0.2-0.3,0.4c-1.5,0.7-2.8,2.3-4.2,2.9c-0.2,0.2-0.3,0.5-0.5,0.7c-10.2,6.8-21.5,7.7-33.4,4.7c-0.1-0.1-0.2-0.2-0.4-0.3c-4.8-0.9-6.4-3.6-9.3-5c0.3-1.3-0.6-2.4-1.8-3.5c0.7-0.7,0.9-1.4,0.9-2c-0.6-0.6-1.1-0.2-1.7-0.7c0.2-0.5,0.8-1,0.4-1.3c-0.1-0.1-0.2-0.2-0.4-0.3c0.1-0.1,0.2-0.2,0.3-0.4c-0.4-0.4,0-0.8,0.4-1.3c-0.5-0.4-0.9-0.4-1.3-0.4c1-1-0.2-1.6-0.4-2.4c0.4-1.5-0.4-2.9-0.7-4.3c0.1-0.1,0.2-0.2,0.3-0.4c0.1-1.2-0.1-2.3-0.3-3.4c-0.6-0.5-0.4-1.2,0.3-1.9c-1.5-0.9-0.7-0.7,0-1.6c-1-0.6-0.8-1.3-0.1-2.2c-0.2-0.2-0.5-0.3-0.7-0.5c0.4-0.6,0.9-1.2,0.1-1.6c0.3-0.4,0.5-0.7,0.8-1.1c-0.4-0.4-1.1-0.7-0.8-1.1c0.2-0.2,0.3-0.5,0.5-0.7c0.5-1.7,0.1-3.1-1.1-4.5c-0.6-0.4,0.5-1.1,0-1.6c0.4-1.4-0.6-2.6-1-3.9c-1.3-0.5-0.6-1.4-1.9-1.9c-0.4-0.2-0.2-0.6-0.5-0.9c-0.1-0.9-1.6-1.6-2.6-2.4c0.1-1.5-2-1.1-2.8-2c-0.3,0.4-0.5,0.7-0.8,1.1c-1.8-0.8-3.1,1.2-5.1-0.4c-0.5,1.4-1.3,0.7-2,1.3c-0.6,0.7-1.5,0-2.1,0.7c-8,1.1-13.9,3.8-21.3,7.4c-1.4,0.2-2.7,1.1-4.1,1.9c-9.3,6.6-23.4,9.3-22.4,21.6c0.3,3.7,1.4,8.1,2.9,11.7c0.5,0.4,0.9,0.4,1.3,0.4c0,0.8-0.6,1.8-0.2,2.5c1.4-0.8,1.7-0.4,1.4,1c1.3-1.1,1.2-1.7,2.1-2.2c1-0.9,0.7-0.5,1.3-1.8c0.2,0.1,0.5,0.2,0.6-0.1c2.1-1.8,4.3-2.8,6.4-4.5c3.9-2.3,7.7-5,11.7-6.7c0.1-0.1,0.2-0.2,0.3-0.4c2.5-1.2,5.1-1.8,7.5-3.8c2.1,0.2,4-0.9,6-1.6c0.6,0.3,1-0.5,1.5-0.6c5.4-0.8,13.1-4.9,16.8,1.2c-0.3,0.5-0.7,1-1,1.4c1.7,1.1-0.2,1.4,1.1,2.9c0.1,0.1,0.2,0.2,0.4,0.2c-0.9,1.3,0.8,3.2-1,3.6c0.9,0.6,0.7,1.3,1.3,2c0.2,0.2,0.4,0.4,0.1,0.6c-0.5,1.8,0,3.4,0.2,5c-1,2.1,1,3.6,0.9,5.5c0.1,0.1,0.2,0.2,0.4,0.3c0.4,1.7,1.1,3.4,1.2,5.1c0,1.8,1.1,3.4,1.8,5c4,4.5,6.1,6,12.1,8.6c0.5-0.3,1-0.5,1.5-0.6c1.1,0.8,2,1,2.9,0.5c3.6,1.5,7,1.7,10.4,2c0.2,0.1,0.5,0.3,0.6-0.1c7.7,0.4,15.2-3.7,21.4-6.7c0.1-0.1,0.2-0.2,0.3-0.4c4.3-1.7,7.1-4.3,10.7-7.4c0.3-0.5,0.7-0.9,1-1.4c0.3-0.4,0.6-0.2,0.9-0.5c0.5-0.7,1-1.4,1.5-2.1c1.3-1.4,3.5-2.6,5.4-5.3c0.3-0.4,0.6-0.2,0.9-0.5c0.8-0.7,1.7-1.3,2.4-2.6c0.2-0.2,0.3-0.5,0.5-0.7c1.8-1.1,3.4-3.6,5.2-4.3c0.3-0.5,0.7-0.9,1-1.4c12.4-5,24.8-10.6,38.4-8.7c1.3,1.1,2.4,0.5,3.7,1.6c-0.4,2.6,0.3,5,0.6,7.4c-0.3,0.5,0.1,0.8-0.4,1.3c-0.5,3-0.8,6,0.8,8.6c0.9,1.5,1.1,3.2,2,4.7c0.3,1,1,1.9,1.7,2.8c0.9,0.9,1.5,1.8,2.3,2.7c-0.6,0.7,1.2,1.1,1.5,1.6c4.8,4.7,11.8,6.9,16.7,6c0.2,0.2,0.5,0.3,0.7,0.5c3.2-0.4,6.3-0.8,9.5-1.3c0,0,0,0,0,0c0,0,0,0,0,0l0,0c0,0,0,0,0,0c0,0,0,0,0.1,0h0c0,0,0,0,0,0s0,0,0,0c2.1-0.4,4.2-0.8,6.3-1.4c0.1,1.4-0.1,2.7-0.9,4.2c-0.8,1.1,0.4,1.8-1.1,3c2,1.8,0,6.6,3.7,5.3c1.8,0.7,4-1.4,4.5-3.3c0.2,0.1,0.6,0,0.3-0.4c3.6-4.1,8.6-10.6,11.9-13c0.2-0.4,0.6-0.2,0.9-0.5c0.6,0.3,1-0.5,1.5-0.6c7.1-1.4,7.9-7.4,14.5-10.6c0.7-1.4,1.9,0,2.6-1.4c2.8-0.9,4.4-1,7.1-4.6c2.8-1.1,4.2-4,5.3-7.5c0.6,0.4,0.7-1.7,1.8-2.5c-1.7-0.3,0.6-1.4-0.3-1.8c0.2-0.5,0.8-1,0.4-1.3C290,140.6,290.1,140.4,289.8,140.2z M116.7,172.7c0,0-0.1-0.1-0.1-0.1C116.6,172.6,116.6,172.7,116.7,172.7z M117.5,173.7C117.5,173.7,117.5,173.7,117.5,173.7C117.5,173.7,117.5,173.7,117.5,173.7C117.5,173.7,117.5,173.7,117.5,173.7z M117.3,173.5c0.2-0.7-0.5-1.3-0.1-2C116.9,172.2,117.6,172.8,117.3,173.5z M142.6,154.6c0.3,0,0.7,0,1.1-0.1C143.3,154.6,143,154.7,142.6,154.6z M145.1,154.2c-0.2-0.4-0.4-0.7-0.7-1.1C144.6,153.5,144.8,153.8,145.1,154.2L145.1,154.2z M145.1,154.2L145.1,154.2C145.1,154.2,145.1,154.2,145.1,154.2L145.1,154.2z M146.4,154.3c0.1,0,0.2,0.1,0.3,0.1C146.6,154.3,146.5,154.3,146.4,154.3z M145.7,154.2c0.1,0,0.1,0,0.2,0C145.8,154.2,145.8,154.2,145.7,154.2z M145.1,154.2c0.4-1,0.9-0.9,1.5-1C146,153.3,145.5,153.2,145.1,154.2z M117.9,174.6c0,0.1,0.1,0.2,0.1,0.3C117.9,174.8,117.9,174.7,117.9,174.6z M118.2,177c0-0.1,0-0.2,0-0.3C118.2,176.7,118.2,176.8,118.2,177z M118.1,175.6c0,0.1,0,0.2,0.1,0.3C118.2,175.8,118.1,175.7,118.1,175.6z M117.5,173.7c2,0.5,1.4,1.3,2.2,1.8C118.8,175,119.4,174.2,117.5,173.7z M120.1,175.7c-0.2-0.1-0.3-0.1-0.5-0.2C119.7,175.6,119.9,175.6,120.1,175.7z M120.2,175.7c0.2,0.1,0.5,0.1,0.9,0.1C120.7,175.8,120.4,175.8,120.2,175.7z M147,154.5c0.2,0.1,0.5,0.3,0.7,0.5C147.5,154.8,147.3,154.7,147,154.5z M235.6,163.3C235.6,163.3,235.6,163.3,235.6,163.3C235.6,163.3,235.6,163.3,235.6,163.3z M231.6,176.7c0,0-0.1-0.1-0.2-0.1C231.5,176.6,231.6,176.6,231.6,176.7z M231.3,174.9c-0.1-0.1-0.2-0.2-0.3-0.3C231.1,174.7,231.2,174.8,231.3,174.9z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section><!--sec-document-->

    <section class="section sec-method" id="sec-method">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="home-title text-center">
                        <h3 class="font-46">Phương pháp được áp dụng<br>trong khóa học</h3>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <nav class="tab-method">
                        <div class="nav nav-tabs mb-3" id="nav-tab" role="tablist">
                            <button class="nav-link active" id="nav-method-1-tab" data-bs-toggle="tab"
                                    data-bs-target="#method-1" type="button" role="tab" aria-controls="nav-home"
                                    aria-selected="true">Gốc từ
                            </button>
                            <button class="nav-link" id="nav-method-2-tab" data-bs-toggle="tab"
                                    data-bs-target="#method-2" type="button" role="tab" aria-controls="nav-profile"
                                    aria-selected="false">Câu chuyện
                            </button>
                            <button class="nav-link" id="nav-method-3-tab" data-bs-toggle="tab"
                                    data-bs-target="#method-3" type="button" role="tab" aria-controls="nav-contact"
                                    aria-selected="false">Logic
                            </button>
                            <button class="nav-link" id="nav-method-4-tab" data-bs-toggle="tab"
                                    data-bs-target="#method-4" type="button" role="tab" aria-controls="nav-contact"
                                    aria-selected="false">Tranh vẽ sáng tạo
                            </button>
                        </div>
                    </nav>
                    <div class="tab-content p-3 border tab-method-panel" id="nav-tabContent">
                        <div class="tab-pane fade active show" id="method-1" role="tabpanel"
                             aria-labelledby="nav-home-tab">
                            <div class="row row-0 align-items-center">
                                <div class="col-12 col-md-6 col-lg-6 col-xl-6 pd-0">
                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/goc-tu.jpg')}}" alt="">
                                </div>
                                <div class="col-12 col-md-6 col-lg-6 col-xl-6 pd-0">
                                    <div class="method-body"><h3>Gốc từ</h3></div>
                                    <div class="method-text"><span
                                            style="font-weight: bold;"><span
                                                style="color: rgb(255, 105, 1);">Trên</span></span> <span
                                            style="color: rgb(255, 105, 1); font-weight: bold;">60%</span> từ vựng
                                        Tiếng Anh được cấu tạo từ các gốc từ tiếng Hy Lạp và La Mã cổ đại.<br><br>👉
                                        👉 Nhờ đó, hiểu về Gốc từ sẽ giúp bạn hiểu <span
                                            style="font-weight: bold; color: rgb(255, 105, 1);">bản ch</span><span
                                            style="font-weight: bold; color: rgb(255, 105, 1);">ất</span> của từ
                                        vựng, dễ dàng<span
                                            style="font-weight: bold; color: rgb(255, 105, 1);"> mở rộng </span>vốn
                                        từ, và <span
                                            style="font-weight: bold; color: rgb(255, 105, 1);">đoán nghĩa </span>cả
                                        với từ chưa gặp bao giờ.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="method-2" role="tabpanel" aria-labelledby="nav-profile-tab">
                            <div class="row row-0 align-items-center">
                                <div class="col-12 col-md-6 col-lg-6 col-xl-6 pd-0">
                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/cauchuyen.jpg')}}" alt="">
                                </div>
                                <div class="col-12 col-md-6 col-lg-6 col-xl-6 pd-0">
                                    <div class="method-body"><h3>CÂU CHUYỆN</h3></div>
                                    <div class="method-text">Gợi <span
                                            style="font-weight: bold; color: rgb(255, 105, 1);">cảm xúc</span> và ngữ
                                        cảnh thực tế, giúp việc ghi nhớ trở nên tự nhiên, thú vị và gần gũi hơn.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="method-3" role="tabpanel" aria-labelledby="nav-contact-tab">
                            <div class="row row-0 align-items-center">
                                <div class="col-12 col-md-6 col-lg-6 col-xl-6 pd-0">
                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/tab1.jpg')}}" alt="">
                                </div>
                                <div class="col-12 col-md-6 col-lg-6 col-xl-6 pd-0">
                                    <div class="method-body"><h3>Logic</h3></div>
                                    <div class="method-text">Tạo sự <span
                                            style="font-weight: bold; color: rgb(255, 105, 1);">kết nối chặt chẽ</span>
                                        giữa các kiến thức, giúp bạn học có hệ thống, nhớ lâu và dễ lần tìm lại được
                                        thông tin.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="method-4" role="tabpanel" aria-labelledby="nav-contact-tab">
                            <div class="row row-0 align-items-center">
                                <div class="col-12 col-md-6 col-lg-6 col-xl-6 pd-0">
                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/tab4.jpg')}}" alt="">
                                </div>
                                <div class="col-12 col-md-6 col-lg-6 col-xl-6 pd-0">
                                    <div class="method-body"><h3>TRANH VẼ</h3></div>
                                    <div class="method-text">Những tranh vẽ, hình ảnh sáng tạo, cute và hài hước sẽ giúp
                                        kích hoạt tối đa khả năng <span
                                            style="font-weight: bold; color: rgb(255, 105, 1);">tái tạo</span> thông
                                        tin của bộ não.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section><!--sec-sec-method-->

    <section class="section sec-course" id="sec-course">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="home-title text-center">
                        <h3 class="font-42" style="color:#fff">
                            <span style="color: rgb(6, 148, 216);">Bứt phá Tiếng Anh</span><br>
                            <span style="color: rgb(28, 0, 194);">Nhận ngay ưu đãi!</span>
                        </h3>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-md-6 col-lg-6 col-xl-6">
                    <div class="accordion faq-accordion" id="accordionFaq2">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2-heading-1">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq2-1" aria-expanded="true" aria-controls="faq2-1">
                                    Phần 1: Tổng quan khóa học
                                </button>
                            </h2>
                            <div id="faq2-1" class="accordion-collapse collapse show" aria-labelledby="faq2-1"
                                 data-bs-parent="#accordionFaq2">
                                <div class="accordion-body">
                                    <video class="iframe-video-preload no-pause"
                                           data-video-type="direct" data-autoplay="true"
                                           style="width: 100%; height: 100%; top: 0; left: 0; object-fit: cover;"
                                           preload="auto" controlslist="nodownload" loop="" autoplay="" playsinline=""
                                           webkit-playsinline="" muted=""
                                           src="{{ asset('assets/frontend/dau-xanh/assets/images/01-20250401025443-m0gns.mp4')}}"></video>
                                    <p>✔️ Những kiến thức bạn chắc chắn phải biết về Tiền tố, Gốc từ, và Hậu tố - 3
                                        thành phần chính cấu tạo nên rất nhiều từ vựng.</p>
                                    <p>✔️ Sức mạnh của Hình ảnh trong tăng cường khả năng ghi nhớ.</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2-heading-2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq2-2" aria-expanded="false" aria-controls="faq2-2">
                                    Phần 2: Tiếng Anh không hề nhàm chán
                                </button>
                            </h2>
                            <div id="faq2-2" class="accordion-collapse collapse" aria-labelledby="faq2-2"
                                 data-bs-parent="#accordionFaq2">
                                <div class="accordion-body">
                                    <video class="iframe-video-preload no-pause"
                                           data-video-type="direct" data-autoplay="true"
                                           style="width: 100%; height: 100%; top: 0; left: 0; object-fit: cover;"
                                           preload="auto" controlslist="nodownload" loop="" autoplay="" playsinline=""
                                           webkit-playsinline="" muted=""
                                           src="{{ asset('assets/frontend/dau-xanh/assets/images/than-thoai-20250406014137-1wx2l.mp4')}}"></video>
                                    <div>✔️ Một phần khởi động nhỏ để giúp bạn hiểu rằng: Tiếng Anh không hề khô khan,
                                        mà chính là nguồn gốc của tri thức 🌱
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2-heading-3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq2-3" aria-expanded="false" aria-controls="faq2-3">
                                    Phần 3: Tiền tố - Yếu tố quan trọng bạn thường bỏ qua
                                </button>
                            </h2>
                            <div id="faq2-3" class="accordion-collapse collapse" aria-labelledby="faq2-3"
                                 data-bs-parent="#accordionFaq2">
                                <div class="accordion-body">
                                    <video class="iframe-video-preload no-pause"
                                           data-video-type="direct" data-autoplay="true"
                                           style="width: 100%; height: 100%; top: 0; left: 0; object-fit: cover;"
                                           preload="auto" controlslist="nodownload" loop="" autoplay="" playsinline=""
                                           webkit-playsinline="" muted=""
                                           src="{{ asset('assets/frontend/dau-xanh/assets/images/vocation-sao-chep-20250402151032-jo9sb.mp4')}}"></video>
                                    <p>✔️ Tiền tố có thể giúp bạn suy luận và hiểu hơn ý nghĩa của từ vựng như thế
                                        nào?</p>
                                    <p>✔️ Cách mà Tiền tố kết hợp với Gốc từ để mở rộng, tạo nên vô vàn từ vựng</p>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2-heading-4">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq2-4" aria-expanded="false" aria-controls="faq2-4">
                                    Phần 4: Học Gốc từ, học từ Gốc
                                </button>
                            </h2>
                            <div id="faq2-4" class="accordion-collapse collapse" aria-labelledby="faq2-4"
                                 data-bs-parent="#accordionFaq2">
                                <div class="accordion-body">
                                    <video class="iframe-video-preload no-pause"
                                           data-video-type="direct" data-autoplay="true"
                                           style="width: 100%; height: 100%; top: 0; left: 0; object-fit: cover;"
                                           preload="auto" controlslist="nodownload" loop="" autoplay="" playsinline=""
                                           webkit-playsinline="" muted=""
                                           src="{{ asset('assets/frontend/dau-xanh/assets/images/good-tu-20250406020756-jpldq.mp4')}}"></video>
                                    <div>✔️ Gốc từ chính là yếu tố then chốt giúp bạn thực sự HIỂU 1 từ vựng. Trong các
                                        bài giảng phần này, mình sẽ phân tích cho bạn tất cả những Gốc từ quan trọng, và
                                        những từ vựng được cấu thành từ những gốc từ đó.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2-heading-5">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq2-5" aria-expanded="false" aria-controls="faq2-5">
                                    Phần 5: Thực chiến & Áp dụng
                                </button>
                            </h2>
                            <div id="faq2-5" class="accordion-collapse collapse" aria-labelledby="faq2-5"
                                 data-bs-parent="#accordionFaq2">
                                <div class="accordion-body">
                                    <video class="iframe-video-preload no-pause"
                                           data-video-type="direct" data-autoplay="true"
                                           style="width: 100%; height: 100%; top: 0; left: 0; object-fit: cover;"
                                           preload="auto" controlslist="nodownload" loop="" autoplay="" playsinline=""
                                           webkit-playsinline="" muted=""
                                           src="{{ asset('assets/frontend/dau-xanh/assets/images/thuc-chien.mp4')}}"></video>
                                    ✔️ Áp dụng kiến thức đã học để xử lý các bài đọc trong đề thi IELTS, VSTEP. Đây sẽ
                                    là lúc bạn thực sự hiểu được những kiển thức đã học có sức mạnh to lớn như thế nào
                                    trong quá trình bạn đọc và làm bài.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2-heading-6">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq2-6" aria-expanded="false" aria-controls="faq2-6">
                                    Phần 6: Từ vựng cơ bản
                                </button>
                            </h2>
                            <div id="faq2-6" class="accordion-collapse collapse" aria-labelledby="faq2-6"
                                 data-bs-parent="#accordionFaq2">
                                <div class="accordion-body">
                                    <video class="iframe-video-preload no-pause"
                                           data-video-type="direct" data-autoplay="true"
                                           style="width: 100%; height: 100%; top: 0; left: 0; object-fit: cover;"
                                           preload="auto" controlslist="nodownload" loop="" autoplay="" playsinline=""
                                           webkit-playsinline="" muted=""
                                           src="{{ asset('assets/frontend/dau-xanh/assets/images/ecommerce.mp4')}}"></video>
                                    <div>✔️ Khóa học đang mở rộng dần tới các từ vựng cơ bản, thông qua những video gần
                                        gũi,
                                        thân thuộc với đời sống.
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq2-heading-7">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq2-7" aria-expanded="false" aria-controls="faq2-7">
                                    Quan trọng: BÀI TẬP ÔN LUYỆN
                                </button>
                            </h2>
                            <div id="faq2-7" class="accordion-collapse collapse" aria-labelledby="faq2-7"
                                 data-bs-parent="#accordionFaq2">
                                <div class="accordion-body">
                                    <video class="iframe-video-preload no-pause"
                                           data-video-type="direct" data-autoplay="true"
                                           style="width: 100%; height: 100%; top: 0; left: 0; object-fit: cover;"
                                           preload="auto" controlslist="nodownload" loop="" autoplay="" playsinline=""
                                           webkit-playsinline="" muted=""
                                           src="{{ asset('assets/frontend/dau-xanh/assets/images/ehrhz.mp4')}}"></video>
                                    <div>✔️ Làm Test hay Flashcard ôn tập để củng cố lại kiến
                                        thức sau mỗi bài học chắc chắn là điều không thể thiếu, bạn hãy hoàn thành đầy
                                        đủ nhé 😉&nbsp;<br></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-6 col-xl-6">
                    <div class="box-register register-2">
                        <div class="box-register-content">
                            <div class="box-register-head">
                                <p class="flex-text" style="color:rgb(255, 225, 38);font-size: 18px">Học phí gốc:
                                    <ins>{{number_format($course_details->price)}} VNĐ</ins>
                                </p>
                                <p class="text-small text-uppercase">Ưu đãi theo từng giai đoạn:</p>
                            </div>
                            @if($course_details->discount_flag==1)
                                <div class="box-register-content">
                                    <div class="box-register-body">
                                        <h3 style="font-size: 18px;font-weight: bold;text-align: center">HIỆN TẠI:</h3>
                                        <div class="register-down">
                                       <span style="font-weight: bold;"><span
                                               style="color: rgb(224, 41, 41); font-size: 22px;">Giảm {{ceil($salePrice)}}%</span> </span><span
                                                style="font-size: 20px;">– còn</span> <span
                                                style="font-weight: bold; color: rgb(1, 5, 245);font-size: 22px;">{{number_format($discountPrice)}}đ</span><br>
                                        </div>
                                        @if(isset($price_next) && isset($next_date_up_price) && $next_date_up_price && $price_next)

                                            <p style="text-align: center;margin: 15px 0 0 0;font-size: 16px">
                                                <i> <span
                                                        style="color: rgb(28, 0, 194); font-weight: normal;">Mức giá sẽ tăng lên:</span>
                                                    <span style="color: rgb(255, 69, 69);">{{number_format($price_next)}}đ </span>
                                                    <span
                                                        style="color: rgb(28, 0, 194);"><span
                                                            style="font-weight: normal;">từ ngày:</span> </span><span
                                                        style="color: rgb(255, 69, 69);">{{$next_date_up_price}} </span></i>
                                            </p>
                                        @endif
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="box-register-content register-2 register-3">
                            <div class="box-register-heading">ƯU ĐÃI ĐẶC BIỆT:
                                <div class="heading-bookmark">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                         preserveAspectRatio="none" viewBox="0 0 1397.9638 1896.0833" class=""
                                         fill="rgb(255, 187, 32)">
                                        <path d="M1164 128q23 0 44 9 33 13 52.5 41t19.5 62v1289q0 34-19.5 62t-52.5 41q-19 8-44 8-48 0-83-32l-441-424-441 424q-36 33-83 33-23 0-44-9-33-13-52.5-41T0 1529V240q0-34 19.5-62T72 137q21-9 44-9h1048z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="box-register-body">
                                <div class="code-heading">🎉 Tặng thêm voucher <span
                                        style="color: rgb(197, 29, 29);"><span
                                            style="text-decoration-line: underline; font-weight: bold;">100k</span><span
                                            style="font-style: italic;"> </span></span>cho <span
                                        style="color: rgb(197, 29, 29); text-decoration-line: underline; font-weight: bold;">10 bạn</span>
                                    đăng ký SỚM NHẤT trong hôm nay:
                                </div>
                                <div class="register-code">
                                    <div class="register-head">
                                        <div class="register-head-bg">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                 preserveAspectRatio="none" viewBox="0 0 1536 1896.0833" class=""
                                                 fill="url(&quot;#SHAPE117_desktop_gradient&quot;)">
                                                <defs id="SHAPE117_defs">
                                                    <linearGradient id="SHAPE117_desktop_gradient"
                                                                    gradientTransform="rotate(0)">
                                                        <stop offset="0%" stop-color="rgba(255, 80, 26, 1.0)"></stop>
                                                        <stop offset="100%" stop-color="rgba(255, 219, 1, 1)"></stop>
                                                    </linearGradient>
                                                </defs>
                                                <path d="M1376 896l138 135q30 28 20 70-12 41-52 51l-188 48 53 186q12 41-19 70-29 31-70 19l-186-53-48 188q-10 40-51 52-12 2-19 2-31 0-51-22l-135-138-135 138q-28 30-70 20-41-11-51-52l-48-188-186 53q-41 12-70-19-31-29-19-70l53-186-188-48q-40-10-52-51-10-42 20-70l138-135L22 761q-30-28-20-70 12-41 52-51l188-48-53-186q-12-41 19-70 29-31 70-19l186 53 48-188q10-41 51-51 41-12 70 19l135 139 135-139q29-30 70-19 41 10 51 51l48 188 186-53q41-12 70 19 31 29 19 70l-53 186 188 48q40 10 52 51 10 42-20 70z"></path>
                                            </svg>
                                            <div class="register-head-text">Khi nhập<br>mã:</div>
                                        </div>
                                    </div>
                                    <div class="register-copy">
                                        <div class="register-text">{{$course_details->coupon_code}}</div>
                                        <div class="register-button">Sao chép</div>
                                    </div>
                                </div>
                                <div class="register-countdown" id="countdown2" data-time="{{\Carbon\Carbon::createFromFormat("d/m/Y",$next_date_up_price)->format("Y-m-d")}}">
                                    <div>00</div>
                                    <div>00</div>
                                    <div>00</div>
                                    <div>00</div>
                                </div>
                            </div>
                        </div>

                        <div class="register-button">
                            @include("module_button_by_now")
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section><!--sec-course-->

    <section class="section sec-review" id="sec-review">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="home-title text-center">
                        <h3 class="font-46" style="color:#fff"><span style="color: rgb(255, 225, 38);">Cảm nhận</span>
                            của học viên về khóa học</h3>
                    </div>
                </div>
            </div>

            <div class="top-review">
                <div class="row align-items-end">
                    <div class="col-12 col-md-5 col-lg-5 col-xl-5">
                        <div class="top-review-author">
                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/dau-xanh-24.png')}}" alt="">
                            <div class="top-review-info">
                                Từ những bạn học sinh, sinh<br>viên, đến người đi làm...
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-md-7 col-lg-7 col-xl-7">
                        <div class="top-review-author-info"><h4>Bạn Bùi Thu Giang</h4>
                            <p>Nghiên cứu sinh Thạc sĩ - Đại Học Nhân dân Trung Quốc</p></div>
                        <div class="top-review-images">
                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/thu-giang-chinh.png')}}" alt="">
                        </div>
                    </div>
                </div>
            </div>

            <div class="review-body">
                <div class="row align-items-end">
                    <div class="col-6 col-md-3 col-lg-3 col-xl-3 d-block d-md-none d-lg-none">
                        <div class="review-item">
                            <div class="review-images review-desktop">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/review3.webp')}}" alt="">
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3 col-lg-3 col-xl-3 d-block d-md-none d-lg-none">
                        <div class="review-item">
                            <div class="review-author">
                                <h4>Bạn Đức Bình</h4>
                                <p>- SV Học viện Công nghệ Bưu chính Viễn thông</p>
                            </div>
                            <div class="review-images">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/review2.webp')}}" alt="">
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3 col-lg-3 col-xl-3">
                        <div class="review-item">
                            <div class="review-author">
                                <h4>Bạn Hữu Nam</h4>
                                <p>- Sinh viên Đại học FPT</p>
                            </div>
                            <div class="review-images">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/review1.webp')}}" alt="">
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3 col-lg-3 col-xl-3 d-none d-md-block d-lg-block">
                        <div class="review-item">
                            <div class="review-author">
                                <h4>Bạn Đức Bình</h4>
                                <p>- SV Học viện Công nghệ Bưu chính Viễn thông</p>
                            </div>
                            <div class="review-images">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/review2.webp')}}" alt="">
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3 col-lg-3 col-xl-3 d-none d-md-block d-lg-block">
                        <div class="review-item">
                            <div class="review-images review-desktop">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/review3.webp')}}" alt="">
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-md-3 col-lg-3 col-xl-3">
                        <div class="review-item">
                            <div class="review-author">
                                <h4>Bạn Minh Tâm</h4>
                                <p>- SV Đại học Ngoại ngữ, ĐHQG Hà Nội</p>
                            </div>
                            <div class="review-images">
                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/review4.webp')}}" alt="">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="review-slide">
                <div class="home-title2"><h3>Xem thêm những Feedback khác</h3></div>

                <div class="swiper-container">
                    <div class="swiper-review swiper">
                        <div class="swiper-wrapper">
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images"><img src="{{ asset('assets/frontend/dau-xanh/assets/images/4-kim-quyen.webp')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Kim Quyên</h4>
                                        <p>Nhân viên văn phòng</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/6-nu.webp')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Hồng Nụ</h4>
                                        <p>Cựu sinh viên Đại học Y Hà Nội</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/7-quan.webp')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Anh Quân</h4>
                                        <p>Bác sĩ Tai Mũi Họng, bv Đại học Y Hải Phòng</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/5-kr.webp')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Kim Oanh</h4>
                                        <p>Sinh viên Đại học Y Dược Hải Phòng</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/9-viet-anh.webp')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Việt Anh</h4>
                                        <p>Sinh viên Đại học Quốc gia Hà Nội</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/16-thanh-tai.jpg')}}" alt=""></div>
                                    <div class="sd-author"><h4>Anh Thành Tài</h4>
                                        <p>Sĩ quan Quân đội</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/21-loi.jpg')}}" alt=""></div>
                                    <div class="sd-author"><h4>Anh Lợi Trần</h4>
                                        <p>Doanh nhân</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/23-pham-xuan-duc.jpg')}}" alt=""></div>
                                    <div class="sd-author"><h4>Anh Phạm Xuân Đức</h4>
                                        <p>Viện Khoa học Thủy lợi Việt Nam</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/14-nam.jpg')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Hữu Nam</h4>
                                        <p>Sinh viên Đại học FPT</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/17-manh.jpg')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Tiến Mạnh</h4>
                                        <p>Sinh viên Đại học</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/19-hoang-uyen.jpg')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Hoàng Uyên</h4>
                                        <p>Sinh viên đại học</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/20-trumpt.jpg')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Thành Trung</h4>
                                        <p>Sinh viên đại học</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/3-lien.jpg')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Bích Liên</h4>
                                        <p>Bác sĩ Gây mê Hồi sức, bệnh viện Bạch Mai</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/14-tuyet-anh.webp')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Tuyết Anh</h4>
                                        <p>Sinh viên Học viện Ngân hàng</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/5-tuyet-anh.jpg')}}" alt="">
                                    </div>
                                    <div class="sd-author"><h4>Bạn Tuyết Anh</h4>
                                        <p>Sinh viên Học viện Ngân hàng</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/tuan-minh.jpg')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Tuấn Minh</h4>
                                        <p>Học Sinh THPT</p></div>
                                </div>
                            </div>
                            <div class="swiper-slide">
                                <div class="sd-item">
                                    <div class="sd-images">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/10-thanh.jpg')}}" alt=""></div>
                                    <div class="sd-author"><h4>Bạn Thành Tài</h4>
                                        <p>Sinh viên Đại học Y Dược Cần Thơ</p></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-nav">
                        <div class="swiper-button-prev swiper-review-prev"><span></span></div>
                        <div class="swiper-button-next swiper-review-next"><span></span></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="sec-down">
            <svg xmlns="http://www.w3.org/2000/svg" height="100%" viewBox="0 0 960 960" width="100%"
                 preserveAspectRatio="none" class="" fill="rgba(1, 133, 197, 1)">
                <use xlink:href="#shape_fIYeneYUPl"></use>
            </svg>
        </div>
    </section><!--sec-review-->

    <section class="section sec-author" id="sec-author">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="home-title text-center">
                        <h3 class="font-46">Tác Giả Khóa Học</h3>
                    </div>
                </div>
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="box-author">
                        <div class="row align-items-end">
                            <div class="col-12 col-md-9 col-lg-9">
                                <div class="author-left author-lf-1">
                                    <div class="row align-items-end">
                                        <div class="col-12 col-md-4 col-lg-4">
                                            <div class="author-thumbnail">
                                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/ngoc-nho.png')}}" alt="">
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-8 col-lg-8">
                                            <div class="author-content">
                                                <span style="font-weight: bold;">Nguyễn Thị Minh Ngọc</span><br>✨ <span
                                                    style="font-weight: bold;">Giải Nhất</span> Tuần cuộc thi Đường lên
                                                đỉnh
                                                Olympia năm thứ 15.<br>✨ Tốt nghiệp chuyên ngành Bác sĩ Đa Khoa <br>&nbsp;
                                                &nbsp; &nbsp;- Đại học Y Hà Nội.<br>✨ Chứng chỉ IELTS 7.5&nbsp;<br><span
                                                    style="font-weight: bold;">✨Giáo viên IELTS</span> tại Edupia IELTS.<br>
                                            </div>
                                            <div class="author-cloud2 d-block d-md-block d-lg-block d-xl-block">
                                                <div class="author-mini d-md-block d-lg-none">
                                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/ngoc-min2.jpg')}}" alt="">
                                                </div>
                                                <div class="cloud d-md-block d-lg-none">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                         preserveAspectRatio="none" viewBox="0 0 249.8 79.6" class=""
                                                         fill="rgba(6, 148, 216, 1)">
                                                        <use xlink:href="#shape_KGYQaiKuwp"></use>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-3 col-lg-3">
                                <div class="author-cloud">
                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/khoai2.png')}}" alt="">
                                    <div class="author-mini d-none d-md-block d-lg-block">
                                        <img src="{{ asset('assets/frontend/dau-xanh/assets/images/ngoc-min2.jpg')}}" alt="">
                                    </div>
                                    <div class="cloud d-none d-md-block d-lg-block">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                             preserveAspectRatio="none" viewBox="0 0 249.8 79.6" class=""
                                             fill="rgba(6, 148, 216, 1)">
                                            <use xlink:href="#shape_KGYQaiKuwp"></use>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-12 col-lg-12 mt-100">
                    <div class="box-author">
                        <div class="row align-items-end">
                            <div class="col-12 col-md-3 col-lg-3">
                                <div class="author-cloud text-right">
                                    <img src="{{ asset('assets/frontend/dau-xanh/assets/images/r_rqu.png')}}" alt="">
                                </div>
                            </div>
                            <div class="col-12 col-md-9 col-lg-9">
                                <div class="author-left authorRight">
                                    <div class="row align-items-end">
                                        <div class="col-12 col-md-8 col-lg-8">
                                            <div class="author-content author-right">
                                                <span style="font-weight: bold;">Phạm Văn Trường</span><br>✨
                                                Tốt nghiệp chuyên ngành Bác Sĩ Đa Khoa - Đại học Y Hà Nội.<br>✨
                                                Chứng chỉ <span style="font-weight: bold;">IELTS 8.0</span><br>✨
                                                Admin Fanpage và kênh Youtube <span
                                                    style="font-weight: bold;">Đậu Xanh.</span><br>
                                                <div class="author-social">
                                                    <div class="social-item">
                                                        <a href="https://www.facebook.com/dauxanh.82">
                                                            <div class="social-icon">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="100%"
                                                                     height="100%" preserveAspectRatio="none"
                                                                     viewBox="0 0 1536 1896.0833" class=""
                                                                     fill="rgba(255, 255, 255, 1)">
                                                                    <path d="M1248 128q119 0 203.5 84.5T1536 416v960q0 119-84.5 203.5T1248 1664h-188v-595h199l30-232h-229V689q0-56 23.5-84t91.5-28l122-1V369q-63-9-178-9-136 0-217.5 80T820 666v171H620v232h200v595H288q-119 0-203.5-84.5T0 1376V416q0-119 84.5-203.5T288 128h960z"></path>
                                                                </svg>
                                                            </div>
                                                            <div class="social-content">
                                                                <h3>108,2k+<br>Người theo dõi<br></h3>
                                                            </div>
                                                        </a>
                                                    </div>
                                                    <div class="social-item">
                                                        <a href="https://www.youtube.com/@dauxanh.82">
                                                            <div class="social-icon">
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="100%"
                                                                     height="100%" preserveAspectRatio="none"
                                                                     viewBox="0 0 1792 1896.08" class=""
                                                                     fill="rgba(255, 255, 255, 1)">
                                                                    <use xlink:href="#shape_NgkhYfyuyy"></use>
                                                                </svg>
                                                            </div>
                                                            <div class="social-content">
                                                                <h3>34,5k+<br>Người theo dõi<br></h3>
                                                            </div>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-4 col-lg-4">
                                            <div class="author-thumbnail">
                                                <img src="{{ asset('assets/frontend/dau-xanh/assets/images/truong.webp')}}" alt="">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section><!--sec-author-->

    <section class="section sec-register" id="sec-register">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-6 col-lg-6 col-xl-6">
                    <div class="register-left">
                        <div class="home-title text-center not-line">
                            <h3 class="font-42 text-uppercase" style="color:#fff;">biến Tiếng Anh<br>trở thành <strong
                                    style="color: rgb(255, 225, 38);">trợ thủ đắc lực</strong><br>trên
                                hành trình của bạn!</h3>
                        </div>
                        <div class="register-desc">
                            Hãy trang bị cho bản thân một phương pháp học tập thông minh, tiết kiệm thời gian, nhanh
                            chóng
                            làm chủ Tiếng Anh và yêu thích Tiếng Anh hơn mỗi ngày!
                        </div>
                        <div class="register-dx">
                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/dau-xanh.webp')}}" alt="">
                        </div>
                    </div>
                </div>
                <div class="col-12 col-md-6 col-lg-6 col-xl-6">
                    <div class="box-register">
                        <div class="box-register-content">
                            <div class="box-register-head">
                                <p>Khóa học đang được</p>
                                <p class="text-uppercase">GIẢM GIÁ ĐẶC BIỆT CHỈ CÒN:
                                    <ins>{{number_format($discountPrice)}}đ</ins>
                                </p>
                                @if(isset($next_date_up_price) && $next_date_up_price)
                                    <p class="text-small">(Ưu đãi được áp dụng tới trước ngày <span
                                            style="color: rgb(255, 233, 76);">{{$next_date_up_price}}</span>)</p>
                                @endif
                            </div>
                            <div class="box-register-body">
                                <h3>ƯU TIÊN ĐĂNG KÝ NGAY HÔM NAY</h3>
                                <div class="register-code">
                                    <div class="register-head">
                                        <div class="register-head-bg">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                                 preserveAspectRatio="none" viewBox="0 0 1536 1896.0833" class=""
                                                 fill="url(&quot;#SHAPE117_desktop_gradient&quot;)">
                                                <defs id="SHAPE117_defs">
                                                    <linearGradient id="SHAPE117_desktop_gradient"
                                                                    gradientTransform="rotate(0)">
                                                        <stop offset="0%" stop-color="rgba(255, 80, 26, 1.0)"></stop>
                                                        <stop offset="100%" stop-color="rgba(255, 219, 1, 1)"></stop>
                                                    </linearGradient>
                                                </defs>
                                                <path d="M1376 896l138 135q30 28 20 70-12 41-52 51l-188 48 53 186q12 41-19 70-29 31-70 19l-186-53-48 188q-10 40-51 52-12 2-19 2-31 0-51-22l-135-138-135 138q-28 30-70 20-41-11-51-52l-48-188-186 53q-41 12-70-19-31-29-19-70l53-186-188-48q-40-10-52-51-10-42 20-70l138-135L22 761q-30-28-20-70 12-41 52-51l188-48-53-186q-12-41 19-70 29-31 70-19l186 53 48-188q10-41 51-51 41-12 70 19l135 139 135-139q29-30 70-19 41 10 51 51l48 188 186-53q41-12 70 19 31 29 19 70l-53 186 188 48q40 10 52 51 10 42-20 70z"></path>
                                            </svg>
                                            <div class="register-head-text">Khi nhập<br>mã:</div>
                                        </div>
                                    </div>
                                    <div class="register-copy">
                                        <div class="register-text">{{$course_details->coupon_code}}</div>
                                        <div class="register-button">Sao chép</div>
                                    </div>
                                </div>
                                <div class="register-countdown" id="countdown" data-time="{{\Carbon\Carbon::createFromFormat("d/m/Y",$next_date_up_price)->format("Y-m-d")}}">
                                    <div>00</div>
                                    <div>00</div>
                                    <div>00</div>
                                    <div>00</div>
                                </div>
                            </div>
                        </div>
                        <div class="register-button">
                            @include("module_button_by_now")
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="thumb-cloud">
            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none"
                 viewBox="0 0 249.8 79.6" class="" fill="url(&quot;#SHAPE118_desktop_gradient&quot;)">
                <defs id="SHAPE118_defs">
                    <linearGradient id="SHAPE118_desktop_gradient" gradientTransform="rotate(90)">
                        <stop offset="0%" stop-color="rgba(255, 255, 255, 1)"></stop>
                        <stop offset="100%" stop-color="#AEE6FF"></stop>
                    </linearGradient>
                </defs>
                <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
            </svg>
        </div>
        <div class="thumb-computer">
            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/may-tinh.png')}}" alt="">
        </div>
        <div class="thumb-clock">
            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/clock.png')}}" alt="">
        </div>
    </section><!--sec-register-->

    <section class="section sec-faq" id="sec-faq">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="home-title text-center">
                        <h3 class="text-uppercase">Câu hỏi thường gặp</h3>
                        <div class="sub_title text-uppercase">(FAQ)</div>
                    </div>
                </div>

                <div class="col-12 col-md-12 col-lg-12">
                    <div class="accordion faq-accordion" id="accordionFaq">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq-heading-1">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq-1" aria-expanded="false" aria-controls="faq-1">
                                    1. Khóa học này khác gì so với các App học từ vựng?
                                </button>
                            </h2>
                            <div id="faq-1" class="accordion-collapse collapse" aria-labelledby="faq-1"
                                 data-bs-parent="#accordionFaq">
                                <div class="accordion-body">
                                    <ul>
                                        <li class="">Khóa học của chúng mình <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">K</span><span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">HÔN</span><span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">G H</span><span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">Ề GIỐ</span><span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">NG</span> các App học
                                            Từ vựng.
                                        </li>
                                        <li class="">▶️ Các App từ vựng chủ yếu giúp bạn ôn tập theo <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">cá</span><span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">c</span><span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">h</span><span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">&nbsp;l</span><span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">ặp lại.
</span></li>
                                        <li class="">👉 Trong khi đó, bằng việc phối hợp <span
                                                style="color: rgb(255, 92, 1);"><span style="font-weight: bold;">nhiều phương pháp ghi nhớ</span> </span>mạnh
                                            mẽ hơn, chúng mình sẽ đánh ngay vào bước<span
                                                style="color: rgb(255, 92, 1);"> <span style="font-weight: bold;">MÃ HÓA THÔNG TIN.</span></span>
                                            Đây là 1 bước rất quan trọng trong quá trình ghi nhớ, mà nếu bạn xử lý tốt,
                                            kiến thức mới sẽ được <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">"gắn chặt"</span>
                                            hơn vào bộ nhớ của bạn.
                                        </li>
                                        <li class="">✔️ Ngoài ra, nội dung khóa học của chúng mình dựa trên <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">SỰ SÁNG TẠO</span>
                                            rất nhiều, điều mà các App từ vựng hiện tại chưa thể làm được.
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq-heading-2">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq-2" aria-expanded="false" aria-controls="faq-2">
                                    2. Tại sao khóa học lại ở dạng video dựng sẵn?
                                </button>
                            </h2>
                            <div id="faq-2" class="accordion-collapse collapse" aria-labelledby="faq-2"
                                 data-bs-parent="#accordionFaq">
                                <div class="accordion-body">
                                    <ul>
                                        <li class="">Vì cách làm này:
                                        </li>
                                        <li class="">✔️ Giúp chúng mình <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">tổng hợp, logic, sử dụng tranh vẽ</span>
                                            để truyền tải kiến thức một cách <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">chi tiết, cô đọng, và chỉn chu</span>
                                            nhất. Bạn học lúc nào cũng được, xem lại bao nhiêu lần cũng không giới hạn.
                                        </li>
                                        <li class="">✔️ Dễ dàng <span
                                                style="color: rgb(255, 92, 1); font-weight: bold;">cập nhật, bổ sung</span>
                                            bài giảng mới dựa trên phản hồi của học viên.
                                        </li>
                                        <li class="">✔️ Giữ mức giá cực dễ tiếp cận – chỉ bằng khoảng <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">5 buổi học</span>
                                            trung tâm, nhưng lượng kiến thức thì <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">nhiều hơn gấp bội.</span>
                                        </li>
                                        <li class="">
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq-heading-3">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq-3" aria-expanded="false" aria-controls="faq-3">
                                    3. Mình mất gốc có nên đăng ký khóa học không?
                                </button>
                            </h2>
                            <div id="faq-3" class="accordion-collapse collapse" aria-labelledby="faq-3"
                                 data-bs-parent="#accordionFaq">
                                <div class="accordion-body">
                                    <ul>
                                        <li class="">Mất gốc với mỗi bạn có thể khác nhau một chút.
                                        </li>
                                        <li class="">Khóa học của chúng mình tập trung giúp bạn ghi nhớ những từ vựng có
                                            <span style="font-weight: bold; color: rgb(255, 92, 1);">độ khó từ trung bình</span>,
                                            những từ vựng mà nếu bạn học theo cách truyền thống thì sẽ <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">rất mất thời gian.</span>
                                        </li>
                                        <li class=""><span
                                                style="font-weight: bold; color: rgb(255, 92, 1);"><br></span></li>
                                        <li class="">👉 Do vậy, sẽ là tốt nhất nếu bạn đăng ký khóa học khi đã nắm vững
                                            <span style="font-weight: bold; color: rgb(255, 92, 1);">một số kiến thức căn bản</span>
                                            trong Tiếng Anh, như:
                                        </li>
                                        <li class="">● Ngữ pháp cơ bản (6 thì, câu khẳng định, phủ định, so sánh,...)
                                        </li>
                                        <li class="">● Từ vựng cơ bản (đại từ, số đếm, màu sắc, hoạt động hàng ngày,
                                            thời tiết,...)
                                        </li>
                                        <li class=""><br></li>
                                        <li class="">✅ Tuy nhiên, nếu bạn muốn thử trải nghiệm việc học Tiếng Anh theo 1
                                            hướng tiếp cận <span style="font-weight: bold; color: rgb(255, 92, 1);">mới, thú vị, khác biệt,</span>
                                            bạn vẫn có thể đăng ký khóa học nhé. Mình tin rằng một <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">niềm cảm hứng</span>
                                            ban đầu cũng rất quan trọng, và khóa học của chúng mình hoàn toàn có thể làm
                                            được điều đó.
                                        </li>
                                        <li class="">
                                        </li>
                                        <li class="">
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="faq-heading-4">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#faq-4" aria-expanded="false" aria-controls="faq-4">
                                    4. Đây là khóa học với bộ video dựng sẵn, thì sau khi đăng ký mình có được hỗ trợ
                                    trong quá trình học không?
                                </button>
                            </h2>
                            <div id="faq-4" class="accordion-collapse collapse" aria-labelledby="faq-4"
                                 data-bs-parent="#accordionFaq">
                                <div class="accordion-body">
                                    <ul>
                                        <li class="">Sau khi đăng ký tham gia khóa học, bạn hãy tham gia <span
                                                style="font-style: italic; text-decoration-line: underline;">nhóm Facebook dành cho học viên.</span>
                                        </li>
                                        <li class="">Nếu bạn có thắc mắc hoặc cần hỗ trợ bất kỳ điều gì liên quan đến
                                            khóa học, bạn hãy post bài trực tiếp lên nhóm, hoặc nhắn tin cho các admin.
                                        </li>
                                        <li class="">✅ Chúng mình luôn sẵn sàng giải đáp <span
                                                style="font-weight: bold; color: rgb(255, 92, 1);">24/7.</span></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--sec-fad-->
        </div>
    </section>
</main>

<footer id="footer">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12 col-md-5 col-ld-5 col-xl-5 mt-footer">
                <div class="footer-top">
                    <div class="footer-logo">
                        <a href="/">
                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/logo.png')}}" alt="Logo">
                        </a>
                        <h3 class="company">Đậu Xanh Edu</h3>
                    </div>
                    <div class="footer-address">
                        <p class="address-item">
                            <a href="mailto:<EMAIL>">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                     width="100%" height="100%" viewBox="0 0 24 24" fill="rgba(255,255,255,1)">
                                    <path d="M20,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6A2,2 0 0,0 20,4M20,18H4V8L12,13L20,8V18M20,6L12,11L4,6V6H20V6Z"></path>
                                </svg>
                                Email: <EMAIL>
                            </a>
                        </p>
                        <p class="address-item">
                            <a href="/">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                                     width="100%" height="100%" viewBox="0 0 24 24" fill="rgba(255,255,255,1)">
                                    <path d="M16.36,14C16.44,13.34 16.5,12.68 16.5,12C16.5,11.32 16.44,10.66 16.36,10H19.74C19.9,10.64 20,11.31 20,12C20,12.69 19.9,13.36 19.74,14M14.59,19.56C15.19,18.45 15.65,17.25 15.97,16H18.92C17.96,17.65 16.43,18.93 14.59,19.56M14.34,14H9.66C9.56,13.34 9.5,12.68 9.5,12C9.5,11.32 9.56,10.65 9.66,10H14.34C14.43,10.65 14.5,11.32 14.5,12C14.5,12.68 14.43,13.34 14.34,14M12,19.96C11.17,18.76 10.5,17.43 10.09,16H13.91C13.5,17.43 12.83,18.76 12,19.96M8,8H5.08C6.03,6.34 7.57,5.06 9.4,4.44C8.8,5.55 8.35,6.75 8,8M5.08,16H8C8.35,17.25 8.8,18.45 9.4,19.56C7.57,18.93 6.03,17.65 5.08,16M4.26,14C4.1,13.36 4,12.69 4,12C4,11.31 4.1,10.64 4.26,10H7.64C7.56,10.66 7.5,11.32 7.5,12C7.5,12.68 7.56,13.34 7.64,14M12,4.03C12.83,5.23 13.5,6.57 13.91,8H10.09C10.5,6.57 11.17,5.23 12,4.03M18.92,8H15.97C15.65,6.75 15.19,5.55 14.59,4.44C16.43,5.07 17.96,6.34 18.92,8M12,2C6.47,2 2,6.5 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"></path>
                                </svg>
                                Website: http://dauxanh.edu.vn
                            </a>
                        </p>
                    </div>
                    <div class="footer-social">
                        <a href="https://facebook.com/dauxanh.82" target="_blank"
                           rel="nofollow">
                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                 preserveAspectRatio="none" viewBox="0 0 24 24" class="" fill="rgba(255, 255, 255, 1)">
                                <path d="M19,4V7H17A1,1 0 0,0 16,8V10H19V13H16V20H13V13H11V10H13V7.5C13,5.56 14.57,4 16.5,4M20,2H4A2,2 0 0,0 2,4V20A2,2 0 0,0 4,22H20A2,2 0 0,0 22,20V4C22,2.89 21.1,2 20,2Z"></path>
                            </svg>
                        </a>
                        <a href="https://youtube.com/@dauxanh.82" target="_blank"
                           rel="nofollow">

                            <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"
                                 preserveAspectRatio="none" viewBox="0 0 1792 1896.08" class=""
                                 fill="rgba(255, 255, 255, 1)">
                                <use xlink:href="#shape_NgkhYfyuyy"></use>
                            </svg>
                        </a>
                    </div>
                    <div class="footer-dmca">
                        <a href="">
                            <img src="{{ asset('assets/frontend/dau-xanh/assets/images/dmca.png')}}" alt="">
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-5 col-ld-5 col-xl-5 mt-footer">
                <div class="footer-top">
                    <div id="fb-root"></div>
                    <script async defer crossorigin="anonymous"
                            src="https://connect.facebook.net/vi_VN/sdk.js#xfbml=1&version=v22.0&appId=811114293101209"></script>
                    <div class="fb-page" data-href="https://www.facebook.com/dauxanh.82" data-tabs="timeline"
                         data-width="" data-height="" data-small-header="false" data-adapt-container-width="true"
                         data-hide-cover="false" data-show-facepile="true">
                        <blockquote cite="https://www.facebook.com/dauxanh.82" class="fb-xfbml-parse-ignore"><a
                                href="https://www.facebook.com/dauxanh.82">Đậu Xanh</a></blockquote>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-10 col-lg-10 col-xl-10">
                <div class="footer-copyright">© 2024 Toàn bộ bản quyền thuộc www.dauxanh.edu.vn</div>
            </div>
        </div>
    </div>
</footer>

<div id="button-contact-vr">
    <div id="gom-all-in-one">
        <div id="fanpage-vr" class="button-contact">
            <div class="phone-vr">
                <div class="phone-vr-circle-fill"></div>
                <div class="phone-vr-img-circle">
                    <a target="_blank" href="https://m.me/106866188274988">
                        <img alt="fanpage" src="{{ asset('assets/frontend/dau-xanh/assets/images/messenger.svg') }}">
                    </a>
                </div>
            </div>
        </div>

        <div id="zalo-vr" class="button-contact">
            <div class="phone-vr">
                <div class="phone-vr-circle-fill"></div>
                <div class="phone-vr-img-circle">
                    @auth
                        <a target="_blank" href="{{ route('course.player', ['slug' => $course_details->slug]) }}">
                            <img alt="Zalo" src="{{ asset('assets/frontend/dau-xanh/assets/images/meo.png') }}">
                        </a>
                    @else
                        <a href="#" data-bs-toggle="modal" data-bs-target="#modal-auth">
                            <img alt="Zalo" src="{{ asset('assets/frontend/dau-xanh/assets/images/meo.png') }}">
                        </a>
                    @endauth
                </div>
            </div>
            <!-- Typing text box -->
            <div id="zalo-typing-box" class="typing-text-box">
                <div class="typing-content">
                    <span id="typing-text"></span>
                    <span class="typing-cursor">|</span>
                </div>
            </div>
        </div>
    </div>
</div><!--button-contact-vr-->

<svg xmlns="http://www.w3.org/2000/svg"
     style="width: 0px; height: 0px; position: absolute; overflow: hidden; display: none;">
    <symbol id="shape_GSLbTPEFHC" viewBox="0 0 1792 1896.0833">
        <path d="M1280 896q0-37-30-54L738 522q-31-20-65-2-33 18-33 56v640q0 38 33 56 16 8 31 8 20 0 34-10l512-320q30-17 30-54zm512 0q0 96-1 150t-8.5 136.5T1760 1330q-16 73-69 123t-124 58q-222 25-671 25t-671-25q-71-8-124.5-58T31 1330q-14-65-21.5-147.5T1 1046 0 896t1-150 8.5-136.5T32 462q16-73 69-123t124-58q222-25 671-25t671 25q71 8 124.5 58t69.5 123q14 65 21.5 147.5T1791 746t1 150z"></path>
    </symbol>
    <symbol id="shape_FJzyvAwBqU" viewBox="0 0 24 24">
        <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"></path>
    </symbol>
    <symbol id="shape_oSKzgXuELm" viewBox="0 0 408.7 408.7">
        <polygon fill="#fff" points="163.5 296.3 286.1 204.3 163.5 112.4 163.5 296.3"></polygon>
        <path d="M204.3,0C91.5,0,0,91.5,0,204.3S91.5,408.7,204.3,408.7s204.3-91.5,204.3-204.3S316.7,0,204.3,0ZM163.5,296.3V112.4l122.6,91.9Z"
              transform="translate(0 0)"></path>
    </symbol>
    <symbol id="shape_AncYUuInWN" viewBox="0 0 1416.5353 1896.0833">
        <path d="M1404 1385q0 117-79 196t-196 79q-135 0-235-100L117 784Q4 669 4 513q0-159 110-270t269-111q158 0 273 113l605 606q10 10 10 22 0 16-30.5 46.5T1194 950q-13 0-23-10L565 333q-79-77-181-77-106 0-179 75t-73 181q0 105 76 181l776 777q63 63 145 63 64 0 106-42t42-106q0-82-63-145L633 659q-26-24-60-24-29 0-48 19t-19 48q0 32 25 59l410 410q10 10 10 22 0 16-31 47t-47 31q-12 0-22-10L441 851q-63-61-63-149 0-82 57-139t139-57q88 0 149 63l581 581q100 98 100 235z"></path>
    </symbol>
    <symbol id="shape_cenMemItpV" viewBox="0 0 195.9 196.2">
        <path d="M52.9,5.2a2.9,2.9,0,1,1,2.9,2.9h0A2.9,2.9,0,0,1,52.9,5.2ZM72.6,8.6a3.4,3.4,0,1,0-3.4-3.4,3.4,3.4,0,0,0,3.4,3.4h0Zm16.9,1.8a5.2,5.2,0,1,0-5.2-5.2,5.2,5.2,0,0,0,5.2,5.2h0Zm16.9-.2a5.1,5.1,0,1,0-5.1-5.1,5.1,5.1,0,0,0,5.1,5.1h0Zm16.9-1.5a3.5,3.5,0,1,0-3.5-3.5,3.5,3.5,0,0,0,3.5,3.5h0Zm16.9-.7a2.9,2.9,0,1,0-2.9-2.9,2.9,2.9,0,0,0,2.9,2.9h0ZM38.8,26.5a4.4,4.4,0,1,0-4.4-4.4h0A4.4,4.4,0,0,0,38.8,26.5Zm16.9,1.2a5.7,5.7,0,1,0-5.7-5.7h0a5.7,5.7,0,0,0,5.7,5.7Zm16.9.8a6.5,6.5,0,1,0-6.5-6.5A6.5,6.5,0,0,0,72.6,28.6Zm16.9.8a7.3,7.3,0,1,0-7.3-7.3,7.3,7.3,0,0,0,7.3,7.3Zm16.9-.2a7.1,7.1,0,1,0-7.1-7.1,7.1,7.1,0,0,0,7.1,7.1h0Zm16.9-.6a6.5,6.5,0,1,0-6.5-6.5,6.5,6.5,0,0,0,6.5,6.5Zm16.9-.8a5.7,5.7,0,1,0-5.7-5.7h0a5.7,5.7,0,0,0,5.7,5.7Zm16.9-1.2a4.4,4.4,0,1,0-4.4-4.4h0a4.4,4.4,0,0,0,4.4,4.4h0ZM22,43.4A4.4,4.4,0,1,0,17.5,39h0A4.4,4.4,0,0,0,22,43.4ZM38.8,45a6,6,0,1,0-6-6h0a6,6,0,0,0,6,6h0Zm16.9,1.2A7.2,7.2,0,1,0,48.5,39,7.2,7.2,0,0,0,55.7,46.2Zm84.4,0A7.2,7.2,0,1,0,133,39,7.2,7.2,0,0,0,140.2,46.2Zm16.9-2.6a4.6,4.6,0,1,0-4.6-4.6,4.6,4.6,0,0,0,4.6,4.6h0Zm16.9-.2a4.4,4.4,0,1,0-4.4-4.4,4.4,4.4,0,0,0,4.4,4.4h0ZM5.1,58.7a2.9,2.9,0,1,0-2.9-2.9A2.9,2.9,0,0,0,5.1,58.7ZM22,61.6a5.7,5.7,0,1,0-5.7-5.7h0A5.7,5.7,0,0,0,22,61.6Zm16.9,1.5a7.2,7.2,0,1,0-7.2-7.2A7.2,7.2,0,0,0,38.8,63.1Zm118.2,0a7.2,7.2,0,1,0-7.2-7.2A7.2,7.2,0,0,0,157.1,63.1ZM174,61.6a5.7,5.7,0,1,0-5.7-5.7h0A5.7,5.7,0,0,0,174,61.6Zm16.9-2.8a2.9,2.9,0,1,0-2.9-2.9A2.9,2.9,0,0,0,190.8,58.7ZM5.1,76.2a3.4,3.4,0,1,0-3.4-3.4h0A3.4,3.4,0,0,0,5.1,76.2ZM22,79.3a6.5,6.5,0,1,0-6.5-6.5A6.5,6.5,0,0,0,22,79.3Zm152,0a6.5,6.5,0,1,0-6.5-6.5A6.5,6.5,0,0,0,174,79.3Zm16.9-3.1a3.4,3.4,0,1,0-3.4-3.4,3.4,3.4,0,0,0,3.4,3.4ZM5.1,84.6a5.1,5.1,0,1,0,5.1,5.1h0a5.1,5.1,0,0,0-5-5.1h0Zm16.9-2A7.1,7.1,0,1,0,29,89.7,7.1,7.1,0,0,0,22,82.6h0ZM159.3,98.1a8.8,8.8,0,0,1-2.1,17.2,8.1,8.1,0,1,1-7.9,10.6,9.5,9.5,0,0,1-6.1,6.5,8.5,8.5,0,1,1-10.9,10.9,9.5,9.5,0,0,1-6.5,6.1,8.1,8.1,0,1,1-10.6,7.9,8.8,8.8,0,0,1-17,2.5,9,9,0,0,1-17.4-1.3,8.1,8.1,0,1,1-10.5-9.2,9.5,9.5,0,0,1-6.5-6.1,8.5,8.5,0,1,1-10.9-10.9,9.5,9.5,0,0,1-6.1-6.5,8.1,8.1,0,1,1-7.9-10.6,8.8,8.8,0,0,1-2.1-17.2,8.8,8.8,0,0,1,2.1-17.2,8.1,8.1,0,1,1,7.9-10.6,9.5,9.5,0,0,1,6.1-6.5A8.5,8.5,0,1,1,63.7,52.9a9.5,9.5,0,0,1,6.5-6.1,8.1,8.1,0,1,1,10.5-9.1,9,9,0,0,1,17.4-1.3,8.8,8.8,0,0,1,17.1,2.5,8.1,8.1,0,1,1,10.6,7.9,9.5,9.5,0,0,1,6.5,6.1,8.5,8.5,0,1,1,10.9,10.9,9.5,9.5,0,0,1,6.1,6.5A8.1,8.1,0,1,1,157.3,81a8.8,8.8,0,0,1,2.1,17.1ZM50.8,115.4a10,10,0,0,1-4.3-4.7,8.8,8.8,0,0,1-7.5,4.6,8.1,8.1,0,0,1,7.6,5.7A9.5,9.5,0,0,1,50.8,115.4Zm-4.3-12.9a10.1,10.1,0,0,1,3.7-4.3,10.1,10.1,0,0,1-3.7-4.3,8.8,8.8,0,0,1-5.4,4.3A8.8,8.8,0,0,1,46.6,102.4Zm4.3-21.5a9.5,9.5,0,0,1-4.2-5.6A8.1,8.1,0,0,1,39,80.9a8.8,8.8,0,0,1,7.5,4.6A10.1,10.1,0,0,1,50.8,80.9Zm16.6,51.6a10.5,10.5,0,0,1-3.8-3.8,9.5,9.5,0,0,1-4.9,3.8,8.5,8.5,0,0,1,4.9,4.9,9.5,9.5,0,0,1,3.8-4.9Zm0-68.7a9.5,9.5,0,0,1-3.8-4.9,8.5,8.5,0,0,1-4.9,4.9,9.5,9.5,0,0,1,4.9,3.8,10.4,10.4,0,0,1,3.8-3.8Zm17.5,85.7a10.3,10.3,0,0,1-4.3-4.1,9.5,9.5,0,0,1-5.5,4,8.1,8.1,0,0,1,5.6,6.4A9,9,0,0,1,84.9,149.5Zm0-102.8a9,9,0,0,1-4.2-6.3,8.2,8.2,0,0,1-5.6,6.4,9.5,9.5,0,0,1,5.5,4,10.3,10.3,0,0,1,4.3-4.1Zm17.4,102.8a10.1,10.1,0,0,1-4.2-3.5,10.3,10.3,0,0,1-3.9,3.5,9,9,0,0,1,3.9,5A8.8,8.8,0,0,1,102.3,149.5Zm0-102.8a8.8,8.8,0,0,1-4.2-5,9,9,0,0,1-3.9,5,10.3,10.3,0,0,1,3.9,3.5,10.1,10.1,0,0,1,4.2-3.5Zm18.6,102.7a9.5,9.5,0,0,1-5.6-4.2,10.1,10.1,0,0,1-4.7,4.3,8.7,8.7,0,0,1,4.6,7.5,8.1,8.1,0,0,1,5.7-7.6Zm0-102.7a8.1,8.1,0,0,1-5.7-7.6,8.7,8.7,0,0,1-4.6,7.5,10.1,10.1,0,0,1,4.7,4.3,9.5,9.5,0,0,1,5.6-4.2Zm16.4,85.6a9.5,9.5,0,0,1-4.9-3.8,10.5,10.5,0,0,1-3.8,3.8,9.5,9.5,0,0,1,3.8,4.9,8.5,8.5,0,0,1,4.9-4.9Zm0-68.6a8.5,8.5,0,0,1-4.9-4.9,9.4,9.4,0,0,1-3.8,4.9,10.4,10.4,0,0,1,3.8,3.8,9.5,9.5,0,0,1,4.9-3.7Zm19.7,51.5a8.8,8.8,0,0,1-7.5-4.6,10.1,10.1,0,0,1-4.3,4.7,9.5,9.5,0,0,1,4.2,5.6,8.1,8.1,0,0,1,7.6-5.7Zm-7.5-12.9a8.8,8.8,0,0,1,5.4-4.3,8.8,8.8,0,0,1-5.4-4.3,10.1,10.1,0,0,1-3.7,4.3,10.1,10.1,0,0,1,3.7,4.3Zm7.5-21.5a8.1,8.1,0,0,1-7.6-5.7,9.5,9.5,0,0,1-4.2,5.6,10.1,10.1,0,0,1,4.3,4.7,8.8,8.8,0,0,1,7.6-4.6Zm17.1,1.7a7.1,7.1,0,1,0,7.1,7.1h0a7.1,7.1,0,0,0-7.1-7.1Zm16.9,12.1a5.1,5.1,0,1,0-5.1-5.1h0a5.1,5.1,0,0,0,5.1,5.1h0ZM5.1,101.5a5.1,5.1,0,1,0,5.1,5.1h0A5.1,5.1,0,0,0,5.1,101.5Zm16.9-2a7.1,7.1,0,1,0,7.1,7.1A7.1,7.1,0,0,0,22,99.5Zm152,0a7.1,7.1,0,1,0,7.1,7.1h0A7.1,7.1,0,0,0,174,99.5Zm16.9,2a5.1,5.1,0,1,0,5.1,5.1,5.1,5.1,0,0,0-5.1-5.1ZM5.1,120a3.4,3.4,0,1,0,3.4,3.5h0A3.4,3.4,0,0,0,5.1,120ZM22,116.9a6.5,6.5,0,1,0,6.5,6.5A6.5,6.5,0,0,0,22,116.9Zm152,0a6.5,6.5,0,1,0,6.5,6.5A6.5,6.5,0,0,0,174,116.9Zm16.9,3.1a3.4,3.4,0,1,0,3.4,3.4h0a3.4,3.4,0,0,0-3.4-3.4ZM5.1,137.5a2.9,2.9,0,1,0,2.9,2.9h0A2.9,2.9,0,0,0,5.1,137.5ZM22,134.7a5.7,5.7,0,1,0,5.7,5.7h0a5.7,5.7,0,0,0-5.7-5.7Zm16.9-1.5a7.2,7.2,0,1,0,7.2,7.2A7.2,7.2,0,0,0,38.8,133.1Zm118.2,0a7.2,7.2,0,1,0,7.2,7.2A7.2,7.2,0,0,0,157.1,133.1Zm16.9,1.5a5.7,5.7,0,1,0,5.7,5.7h0a5.7,5.7,0,0,0-5.7-5.7Zm16.9,2.8a2.9,2.9,0,1,0,2.9,2.9h0A2.9,2.9,0,0,0,190.8,137.5ZM22,152.8a4.4,4.4,0,1,0,4.4,4.4h0a4.4,4.4,0,0,0-4.4-4.4Zm16.9-1.6a6,6,0,1,0,6,6h0a6,6,0,0,0-6-6ZM55.7,150a7.2,7.2,0,1,0,7.2,7.2,7.2,7.2,0,0,0-7.2-7.2Zm84.4,0a7.2,7.2,0,1,0,7.2,7.2,7.2,7.2,0,0,0-7.2-7.2Zm16.9,1.2a6,6,0,1,0,6,6h0a6,6,0,0,0-6-6Zm16.9,1.6a4.4,4.4,0,1,0,4.4,4.4h0A4.4,4.4,0,0,0,174,152.8ZM38.8,169.7a4.4,4.4,0,1,0,4.4,4.4,4.4,4.4,0,0,0-4.4-4.4Zm16.9-1.2a5.7,5.7,0,1,0,5.7,5.7h0A5.7,5.7,0,0,0,55.7,168.5Zm16.9-.8a6.5,6.5,0,1,0,6.5,6.5,6.5,6.5,0,0,0-6.5-6.5h0Zm16.9-.8a7.3,7.3,0,1,0,7.3,7.3,7.3,7.3,0,0,0-7.3-7.3h0Zm16.9.2a7.1,7.1,0,1,0,7.1,7.1,7.1,7.1,0,0,0-7.1-7.1h0Zm16.9.6a6.5,6.5,0,1,0,6.5,6.5,6.5,6.5,0,0,0-6.5-6.5h0Zm16.9.8a5.7,5.7,0,1,0,5.7,5.7h0A5.7,5.7,0,0,0,140.2,168.5Zm16.9,1.2a4.4,4.4,0,1,0,4.4,4.4h0a4.4,4.4,0,0,0-4.4-4.4ZM55.7,188.1a2.9,2.9,0,1,0,0,5.7h0a3.3,3.3,0,0,0,2.9-2.9,2.9,2.9,0,0,0-2.9-2.9h0Zm16.9-.6a3.4,3.4,0,1,0,3.4,3.4h0a3.4,3.4,0,0,0-3.4-3.4Zm16.9-1.8a5.2,5.2,0,1,0,5.2,5.2,5.2,5.2,0,0,0-5.2-5.2Zm16.9.2a5.1,5.1,0,1,0,5.1,5.1s0,0,0,0A5.1,5.1,0,0,0,106.4,185.9Zm16.9,1.6a3.4,3.4,0,1,0,3.4,3.4h0A3.4,3.4,0,0,0,123.3,187.6Zm16.9.6A2.9,2.9,0,1,0,143,191,2.9,2.9,0,0,0,140.2,188.1Z"></path>
    </symbol>
    <symbol id="shape_tyTUFKuNSb" viewBox="0 0 422.41 34.48">
        <path d="M3.49,7c7.6,0,11.49,5.67,16,12.23C24.4,26.37,30,34.48,41.25,34.48S58.09,26.37,63,19.21C67.51,12.65,71.41,7,79,7s11.5,5.67,16,12.23c4.91,7.16,10.47,15.27,21.75,15.27s16.84-8.11,21.76-15.27C143,12.65,146.93,7,154.53,7s11.5,5.67,16,12.23c4.91,7.16,10.48,15.27,21.75,15.27s16.85-8.11,21.76-15.27C218.55,12.65,222.44,7,230,7s11.5,5.67,16,12.23c4.91,7.16,10.48,15.27,21.76,15.27s16.85-8.11,21.76-15.27C294.08,12.65,298,7,305.58,7s11.5,5.67,16,12.23c4.92,7.16,10.48,15.27,21.76,15.27s16.85-8.11,21.77-15.27c4.51-6.56,8.41-12.23,16-12.23s11.5,5.67,16,12.23c4.91,7.16,10.48,15.27,21.77,15.27a3.49,3.49,0,1,0,0-7c-7.62,0-11.51-5.67-16-12.24C398,8.11,392.41,0,381.13,0s-16.86,8.11-21.77,15.26c-4.51,6.57-8.41,12.24-16,12.24s-11.5-5.67-16-12.24C322.42,8.11,316.86,0,305.58,0s-16.85,8.11-21.76,15.26c-4.51,6.57-8.41,12.24-16,12.24s-11.5-5.67-16-12.24C246.89,8.11,241.32,0,230,0S213.2,8.11,208.29,15.26c-4.5,6.57-8.39,12.24-16,12.24s-11.49-5.67-16-12.24C171.38,8.11,165.81,0,154.53,0s-16.84,8.11-21.76,15.26c-4.5,6.57-8.4,12.24-16,12.24s-11.49-5.67-16-12.24C95.86,8.11,90.29,0,79,0S62.17,8.11,57.25,15.26c-4.5,6.57-8.4,12.24-16,12.24s-11.5-5.67-16-12.24C20.33,8.11,14.77,0,3.49,0a3.49,3.49,0,0,0,0,7Z"></path>
    </symbol>
    <symbol id="shape_EvEherdIen" viewBox="0 0 24 24">
        <path d="M2.81,14.12L5.64,11.29L8.17,10.79C11.39,6.41 17.55,4.22 19.78,4.22C19.78,6.45 17.59,12.61 13.21,15.83L12.71,18.36L9.88,21.19L9.17,17.66C7.76,17.66 7.76,17.66 7.05,16.95C6.34,16.24 6.34,16.24 6.34,14.83L2.81,14.12M5.64,16.95L7.05,18.36L4.39,21.03H2.97V19.61L5.64,16.95M4.22,15.54L5.46,15.71L3,18.16V16.74L4.22,15.54M8.29,18.54L8.46,19.78L7.26,21H5.84L8.29,18.54M13,9.5A1.5,1.5 0 0,0 11.5,11A1.5,1.5 0 0,0 13,12.5A1.5,1.5 0 0,0 14.5,11A1.5,1.5 0 0,0 13,9.5Z"></path>
    </symbol>
    <symbol id="shape_nTBRzxbPRh" viewBox="0 -960 960 960">
        <path d="m421-298 283-283-46-45-237 237-120-120-45 45 165 166Zm59 218q-82 0-155-31.5t-127.5-86Q143-252 111.5-325T80-480q0-83 31.5-156t86-127Q252-817 325-848.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 82-31.5 155T763-197.5q-54 54.5-127 86T480-80Zm0-60q142 0 241-99.5T820-480q0-142-99-241t-241-99q-141 0-240.5 99T140-480q0 141 99.5 240.5T480-140Zm0-340Z"></path>
    </symbol>
    <symbol id="shape_KGYQaiKuwp" viewBox="0 0 249.8 79.6">
        <path d="M249.8,79.6c-1.7-42.9-28.8-46.2-48.7-40-3.1,1-7.2.9-7.6-3.4-1.6-17.3-10.6-28.8-27.7-31S139.2,14.6,131,28.1c-7-9.9-13.1-19.3-24.4-24.4C81.1-7.8,57.5,9.3,51.4,28.6c-2.7,8.5-4.4,12.7-15.6,9.9C13.8,32.9-.2,52,0,79.6"></path>
    </symbol>
    <symbol id="shape_fIYeneYUPl" viewBox="0 -960 960 960">
        <path d="M480-200 240-440l42-42 198 198 198-198 42 42-240 240Zm0-253L240-693l42-42 198 198 198-198 42 42-240 240Z"></path>
    </symbol>
    <symbol id="shape_SUjxAEJmoP" viewBox="0 0 24 24">
        <path d="M5,16L3,5L8.5,12L12,5L15.5,12L21,5L19,16H5M19,19A1,1 0 0,1 18,20H6A1,1 0 0,1 5,19V18H19V19Z"></path>
    </symbol>
    <symbol id="shape_NgkhYfyuyy" viewBox="0 0 1792 1896.0833">
        <path d="M1280 896q0-37-30-54L738 522q-31-20-65-2-33 18-33 56v640q0 38 33 56 16 8 31 8 20 0 34-10l512-320q30-17 30-54zm512 0q0 96-1 150t-8.5 136.5T1760 1330q-16 73-69 123t-124 58q-222 25-671 25t-671-25q-71-8-124.5-58T31 1330q-14-65-21.5-147.5T1 1046 0 896t1-150 8.5-136.5T32 462q16-73 69-123t124-58q222-25 671-25t671 25q71 8 124.5 58t69.5 123q14 65 21.5 147.5T1791 746t1 150z"></path>
    </symbol>
    <symbol id="shape_AqcMzAAcfQ" viewBox="0 -960 960 960"><path d="M480-200 240-440l42-42 198 198 198-198 42 42-240 240Zm0-253L240-693l42-42 198 198 198-198 42 42-240 240Z"></path></symbol>
</svg>
{{--Theem modal regis, login, thanh toan--}}
@include("module_payment_course")

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tìm tất cả các nút "Sao chép"
    const copyButtons = document.querySelectorAll('.register-button');

    copyButtons.forEach(function(button) {
        // Kiểm tra nếu nội dung nút là "Sao chép"
        if (button.textContent.trim() === 'Sao chép') {
            button.addEventListener('click', function() {
                // Tìm element cha chứa class register-copy
                const registerCopy = button.closest('.register-copy');
                if (registerCopy) {
                    // Tìm element với class register-text trong cùng container
                    const registerText = registerCopy.querySelector('.register-text');
                    if (registerText) {
                        const textToCopy = registerText.textContent.trim();

                        // Sao chép vào clipboard
                        if (navigator.clipboard && window.isSecureContext) {
                            // Sử dụng modern clipboard API
                            navigator.clipboard.writeText(textToCopy).then(function() {
                                showCopySuccess(button);
                            }).catch(function(err) {
                                console.error('Lỗi khi sao chép: ', err);
                                fallbackCopyTextToClipboard(textToCopy, button);
                            });
                        } else {
                            // Fallback cho các trình duyệt cũ
                            fallbackCopyTextToClipboard(textToCopy, button);
                        }
                    }
                }
            });
        }
    });

    // Hàm fallback cho các trình duyệt cũ
    function fallbackCopyTextToClipboard(text, button) {
        const textArea = document.createElement("textarea");
        textArea.value = text;

        // Ẩn textarea
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";
        textArea.style.opacity = "0";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess(button);
            } else {
                console.error('Fallback: Không thể sao chép text');
                showCopyError(button);
            }
        } catch (err) {
            console.error('Fallback: Lỗi khi sao chép', err);
            showCopyError(button);
        }

        document.body.removeChild(textArea);
    }

    // Hiển thị thông báo thành công
    function showCopySuccess(button) {
        const originalText = button.textContent;
        button.textContent = 'Đã sao chép!';
        button.style.background = '#28a745';
        button.style.color = '#fff';

        // Khôi phục trạng thái ban đầu sau 2 giây
        setTimeout(function() {
            button.textContent = originalText;
            button.style.background = '';
            button.style.color = '';
        }, 2000);
    }

    // Hiển thị thông báo lỗi
    function showCopyError(button) {
        const originalText = button.textContent;
        button.textContent = 'Lỗi sao chép!';
        button.style.background = '#dc3545';
        button.style.color = '#fff';

        // Khôi phục trạng thái ban đầu sau 2 giây
        setTimeout(function() {
            button.textContent = originalText;
            button.style.background = '';
            button.style.color = '';
        }, 2000);
    }
});

// Typing effect for Zalo VR
document.addEventListener('DOMContentLoaded', function() {
    const typingText = document.getElementById('typing-text');
    const typingBox = document.getElementById('zalo-typing-box');
    const typingContent = typingBox.querySelector('.typing-content');

    // Hai dòng text demo - khác nhau tùy theo trạng thái đăng nhập
    const isAuthenticated = {{ auth()->check() ? 'true' : 'false' }};
    const messages = isAuthenticated ? [
        'Học thử miễn phí',
        'Bấm vào đây'
    ] : [
        'Học thử miễn phí',
        'Đăng ký tài khoản tại đây'
    ];

    let currentMessageIndex = 0;
    let currentCharIndex = 0;
    let isTyping = true;
    let isDeleting = false;

    // Function to update box width based on text content
    function updateBoxWidth() {
        // Create a temporary element to measure text width
        const tempSpan = document.createElement('span');
        tempSpan.style.visibility = 'hidden';
        tempSpan.style.position = 'absolute';
        tempSpan.style.fontSize = '14px';
        tempSpan.style.fontWeight = '500';
        tempSpan.style.whiteSpace = 'nowrap';
        tempSpan.textContent = typingText.textContent + '|'; // Include cursor
        document.body.appendChild(tempSpan);

        const textWidth = tempSpan.offsetWidth;
        document.body.removeChild(tempSpan);

        // Set minimum width and add padding
        const minWidth = 50;
        const padding = 32; // 16px left + 16px right
        const newWidth = Math.max(minWidth, textWidth + padding);

        typingBox.style.width = newWidth + 'px';
    }

    function typeWriter() {
        const currentMessage = messages[currentMessageIndex];

        if (isTyping && !isDeleting) {
            // Typing effect
            if (currentCharIndex < currentMessage.length) {
                typingText.textContent = currentMessage.substring(0, currentCharIndex + 1);
                updateBoxWidth(); // Update box width after each character
                currentCharIndex++;
                setTimeout(typeWriter, 100); // Tốc độ typing
            } else {
                // Pause before deleting
                isTyping = false;
                setTimeout(() => {
                    isDeleting = true;
                    typeWriter();
                }, 2000); // Dừng 2 giây trước khi xóa
            }
        } else if (isDeleting) {
            // Deleting effect
            if (currentCharIndex > 0) {
                typingText.textContent = currentMessage.substring(0, currentCharIndex - 1);
                updateBoxWidth(); // Update box width after each character deletion
                currentCharIndex--;
                setTimeout(typeWriter, 50); // Tốc độ xóa nhanh hơn
            } else {
                // Switch to next message
                isDeleting = false;
                isTyping = true;
                currentMessageIndex = (currentMessageIndex + 1) % messages.length;
                setTimeout(typeWriter, 500); // Pause before typing next message
            }
        }
    }

    // Start typing effect when page loads
    setTimeout(() => {
        typingBox.classList.add('show');
        typeWriter();
    }, 1000);

    // Show/hide on hover
    const zaloVr = document.getElementById('zalo-vr');
    zaloVr.addEventListener('mouseenter', function() {
        typingBox.classList.add('show');
    });

    zaloVr.addEventListener('mouseleave', function() {
        // Keep showing for better UX, comment out if you want to hide on mouse leave
        // typingBox.classList.remove('show');
    });
});
</script>
<style>
    #zalo-vr img {
        max-width: 40px !important;
        max-height: 40px !important;
    }

    /* Typing text box styling */
    .typing-text-box {
        position: absolute;
        right: 70px;
        top: 50%;
        transform: translateY(-50%);
        background: linear-gradient(135deg, #0084ff, #00c6ff);
        color: white;
        padding: 12px 16px;
        border-radius: 20px;
        box-shadow: 0 4px 15px rgba(0, 132, 255, 0.3);
        min-width: 50px;
        max-width: 280px;
        width: auto;
        opacity: 0;
        visibility: hidden;
        transition: all 0.2s ease;
        z-index: 1000;
        overflow: hidden;
    }

    .typing-text-box::before {
        content: '';
        position: absolute;
        right: -8px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 8px solid #0084ff;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
    }

    .typing-text-box.show {
        opacity: 1;
        visibility: visible;
    }

    .typing-content {
        font-size: 14px;
        line-height: 1.4;
        font-weight: 500;
        white-space: nowrap;
        display: inline-block;
    }

    .typing-cursor {
        animation: blink 1s infinite;
        font-weight: bold;
    }

    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
    }

    #zalo-vr {
        position: relative;
    }

    #zalo-vr:hover .typing-text-box {
        opacity: 1;
        visibility: visible;
    }
</style>

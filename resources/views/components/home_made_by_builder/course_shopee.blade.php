<link rel="stylesheet" href="{{ asset('assets/frontend/course-shopee/assets/css/common.css') }}"/>
<link rel="stylesheet" href="{{ asset('assets/frontend/course-shopee/assets/css/styles.css') }}"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
<style>
    .modules-section .important-badge {
        border: 1px solid #ffa688;
        background: linear-gradient(270deg, #E91E63 0%, #9C27B0 100%);
    }
</style>
@push('js')
    <script src="{{ asset('assets/frontend/course-shopee/assets/js/source.js') }}"></script>
    <script src="{{ asset('assets/frontend/course-shopee/assets/js/script.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <script src="{{ asset('assets/frontend/default/js/vietqr.js') }}"></script>
@endpush
<style>
    #qrcode img {
        width: 100%;
    }

    #cancel_coupon,
    .login-link {
        cursor: pointer;
    }
</style>

<header class="header fixed-top">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center">
            <a class="navbar-brand" href="{{ route('home') }}">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/logo.png') }}" alt="topID" width="111"
                     height="44"/>
            </a>

            <div class="header-nav">
                <nav>
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link" href="#gioi-thieu">GIỚI THIỆU</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#khoa-hoc">KHÓA HỌC</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#tinh-nang">TÍNH NĂNG</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#hoc-vien">HỌC VIÊN</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#hoi-dap">HỎI ĐÁP</a>
                        </li>
                    </ul>
                </nav>
            </div>

            <div class="d-flex align-items-center gap-3">

                @include('auth.profile')
            </div>
        </div>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" href="#">GIỚI THIỆU</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">KHÓA HỌC</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">TÍNH NĂNG</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">HỌC VIÊN</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#">HỎI ĐÁP</a>
                </li>
            </ul>
        </div>
    </div>
</header>

<section class="hero-section" id="gioi-thieu">
    <div class="container">
        <div class="row hero-content g-0">
            <div class="col-md-7">
                <div class="course-label text-h2">[ KHÓA HỌC ]</div>
                <h1 class="course-title builder-editable" builder-identity="1">BÁN HÀNG SHOPEE</h1>

                <div class="blue-box">
                    <div class="blue-box-title">LÀM CHỦ BÍ KÍP</div>
                    <div class="orders-per-day">1.000 đơn/ngày</div>
                </div>

                <div class="white-box">
                    <div class="white-box-title text-h4 gradient-2">
                        LÀM CHỦ SHOPEE CHỈ SAU
                        <span class="days-badge lineryellow">48 NGÀY</span>
                    </div>
                    <p class="white-box-description text-text18">
                        Lộ trình thực chiến, cầm tay chỉ việc, không lý thuyết suông!
                    </p>
                </div>

                <div class="action-buttons">
                    @if ($enrollment_status)
                        <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                           class="text-center start-course-btn">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}"
                                 style="filter: invert(1);"/>
                            VÀO HỌC NGAY!</a>
                    @else
                        <button class="buy-course-btn text-text16 text-color-white" type="button" data-bs-toggle="modal"
                                data-bs-target="#modal-regis">
                        <span class="buy-course-btn-icon">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}"
                                 alt="Crown" width="22" height="22"/>
                        </span>
                            MUA KHÓA HỌC
                        </button>
                        @if(Auth()->check())
                            <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}"
                               class="try-free-btn text-color-white">
                        <span class="try-free-btn-icon">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/coin.svg') }}" alt="Try"
                                 width="22" height="22"/>
                        </span>
                                <span>HỌC THỬ MIỄN PHÍ</span>
                            </a>
                        @else
                            <button class="try-free-btn text-color-white" type="button" data-bs-toggle="modal"
                                    data-bs-target="#modal-auth">
                        <span class="try-free-btn-icon">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/coin.svg') }}" alt="Try"
                                 width="22" height="22"/>
                        </span>
                                <span>HỌC THỬ MIỄN PHÍ</span>
                            </button>
                        @endif
                    @endif
                </div>
            </div>

            <div class="col-md-5 expert-image">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/hero-sect-avatar.png') }}" alt="Expert"
                     class="img-fluid"/>
            </div>
        </div>
    </div>
</section>

<section class="problem-section">
    <div class="container">
        <div class="problem-header text-center">
            <div class="problem-title text-h5">Vấn đề nào</div>
            <h2 class="problem-heading text-h2">Bạn đang gặp phải?</h2>
        </div>

        <div class="problems-wrapper position-relative">
            <div class="person-thinking">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/problems-sect-person-thinking.png') }}"
                     alt="Person thinking" width="447.041" height="636.961" class="img-fluid"/>
            </div>

            <div class="problem-item problem-item-1 left-item">
                <div class="warning-icon">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/warning.svg') }}" alt="Warning"
                         width="38" height="38"/>
                </div>
                <h3 class="problem-item-title text-h6">
                    Không biết bắt đầu Shopee từ đâu
                </h3>
                <p class="problem-item-text text-text16">
                    Không biết xác định sản phẩm win, tối ưu SEO?
                </p>
            </div>

            <div class="problem-item problem-item-2 left-item">
                <div class="warning-icon">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/warning.svg') }}" alt="Warning"
                         width="38" height="38"/>
                </div>
                <h3 class="problem-item-title text-h6">
                    Bị hàng ngàn shop lớn "đè bẹp"
                </h3>
                <p class="problem-item-text text-text16">
                    Không thể tìm ra phương án cạnh tranh hiệu quả?
                </p>
            </div>

            <div class="problem-item problem-item-3 problem-item-small left-item">
                <div class="warning-icon">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/warning.svg') }}" alt="Warning"
                         width="38" height="38"/>
                </div>
                <h3 class="problem-item-title text-h6">Thiếu người dẫn đường</h3>
                <p class="problem-item-text text-text16">
                    Tự mò mẫm mà mãi lạc lối?
                </p>
            </div>

            <div class="problem-item problem-item-4 right-item">
                <div class="warning-icon">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/warning.svg') }}" alt="Warning"
                         width="38" height="38"/>
                </div>
                <h3 class="problem-item-title text-h6">Gian hàng lm lm</h3>
                <p class="problem-item-text text-text16">
                    Không ai ghé thăm gian hàng dù đã đổ tiền quảng cáo?
                </p>
            </div>

            <div class="problem-item problem-item-5 right-item">
                <div class="warning-icon">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/warning.svg') }}" alt="Warning"
                         width="38" height="38"/>
                </div>
                <h3 class="problem-item-title text-h6">Chạy Shopee Ads phí tiền</h3>
                <p class="problem-item-text text-text16">
                    Không hiệu quả, khách xem xong rồi lặng lẽ rời đi?
                </p>
            </div>
        </div>
    </div>
</section>

<section class="course-section" id="khoa-hoc">
    <div class="container">
        <div class="course-section-header-wrapper">
            <div class="course-section-header text-center">
                <h2 class="course-section-title">KHÓA HỌC</h2>
                <p class="course-section-subtitle">BÁN HÀNG SHOPEE</p>
                <div class="blue-box-title text-h3">
                    Giải Pháp Tăng Doanh Thu Shopee
                </div>
                <div class="badge-from-zero text-h3">
                    <span>Từ Con Số 0</span>
                </div>
                <p class="course-section-description text-h6">
                    Khóa Học Bán Hàng Shopee này sẽ giúp bạn:
                </p>
            </div>

            <div class="course-avatar-wrapper">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/course-sect-avatar.png') }}"
                     alt="Course Avatar" class="img-fluid" width="558" height="459"/>
            </div>
        </div>

        <div class="course-benefits-wrapper">
            <div class="benefit-card">
                <h3 class="benefit-title text-h6">Xây dựng lộ trình rõ ràng</h3>
                <p class="benefit-text text-text14">
                    Từ mở gian hàng, chọn sản phẩm, đến tối ưu quảng cáo – không bỏ
                    sót bước nào.
                </p>
            </div>

            <div class="benefit-card">
                <h3 class="benefit-title text-h6">
                    Thực hành 100% trên gian hàng của bạn
                </h3>
                <p class="benefit-text text-text14">
                    Áp dụng ngay trong khóa học, được hướng dẫn từng bước.
                </p>
            </div>
            <div class="benefit-card">
                <h3 class="benefit-title text-h6">
                    Tiết kiệm chi phí và thời gian
                </h3>
                <p class="benefit-text text-text14">
                    Không cần tốn hàng chục triệu thử sai, làm đúng ngay từ đầu.
                </p>
            </div>

            <div class="benefit-card">
                <h3 class="benefit-title text-h6">Hỗ trợ lâu dài</h3>
                <p class="benefit-text text-text14">
                    Tham gia nhóm Zalo độc quyền, được hỗ trợ 1-1 sau khóa học để bạn
                    tự tin tăng doanh thu Shopee.
                </p>
            </div>

            <div class="benefit-card">
                <h3 class="benefit-title text-h6">Làm chủ Shopee Ads</h3>
                <p class="benefit-text text-text14">
                    Học cách chạy Ads đúng - chuẩn, học cách tối ưu và quản lý Ads.
                </p>
            </div>

            <div class="benefit-card">
                <h3 class="benefit-title text-h6">Kinh nghiệm xương máu</h3>
                <p class="benefit-text text-text14">
                    Các bí quyết và kinh nghiệm xác định thị trường, sản phẩm, phân
                    tích đối thủ cạnh tranh và nội tại doanh nghiệp!
                </p>
            </div>
        </div>
    </div>
</section>

<section class="modules-section py-5" id="tinh-nang">
    <div class="container">
        <div class="modules-section-header">
            <div class="modules-section-header-title">
                <h2 class="modules-section-header-subtitle-text">
                    Học Gì Trong Khóa Học
                </h2>
                <p class="modules-section-header-title-text">
                    Bán Hàng Shopee Này?
                </p>
            </div>

            <div class="modules-section-header-subtitle text-h4">
                Làm chủ Shopee chỉ sau 45 ngày
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 accordion modules-list accordion-flush" id="course-modules-accordion">
                @foreach ($processed_sections as $key => $section_data)
                    <div class="module-item accordion-item">
                        <div class="module-header accordion-header d-flex justify-content-between align-items-center">
                            <div data-bs-toggle="collapse" data-bs-target="#course-module-content-{{ $section_data['section']->id }}"
                                 aria-expanded="{{ $key == 0 ? 'true' : 'false' }}"
                                 aria-controls="course-module-content-{{ $section_data['section']->id }}">
                                <div class="module-title">
                                    <h3 class="text-h6 mb-0">{{ ucfirst($section_data['section']->title) }}</h3>
                                    <div class="module-details">
                                        <span class="video-count text-text14">
                                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/video-icon.svg') }}" alt="Video"/>
                                            <span> Số lượng video: <strong>{{ $section_data['video_count'] }}</strong> </span>
                                        </span>
                                        <span class="duration text-text14">
                                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/clock-icon.svg') }}" alt="Clock"/>
                                            <span> Thời lượng: <strong>{{ $section_data['formatted_duration'] }}</strong> </span>
                                        </span>
                                    </div>
                                </div>
                                <div class="toggle-btn">
                                    <img class="toggle-btn-icon toggle-btn-icon-collapse" src="{{ asset('assets/frontend/course-shopee/assets/icons/minus.svg') }}" alt="Collapse"/>
                                    <img class="toggle-btn-icon toggle-btn-icon-expand" src="{{ asset('assets/frontend/course-shopee/assets/icons/plus.svg') }}" alt="Expand"/>
                                </div>
                            </div>
                        </div>
                        <div class="module-content accordion-collapse collapse @if($key == 0) show @endif"
                             id="course-module-content-{{ $section_data['section']->id }}" data-bs-parent="#course-modules-accordion">
                            <div class="accordion-body">
                                @foreach ($section_data['lessons'] as $lesson)
                                    <div class="lesson-item">
                                        @if(Auth()->check())
                                            <a href="{{ route('course.player', ['slug' => $course_details->slug, 'id' => $lesson->id]) }}" class="lesson-link">
                                        @else
                                            <a href="javascript:void(0);" data-bs-toggle="modal" data-bs-target="#modal-auth" class="lesson-link">
                                        @endif
                                                <p class="text-text14 lesson-item-title">
                                                    @if($lesson->hide_title == 1)
                                                        <span class="text-white">Tiêu đề bài học đã bị ẩn</span>
                                                    @else
                                                        {{ $lesson->title }}
                                                    @endif
                                                </p>
                                                <div class="d-flex align-items-center">
                                                    @if($lesson->paid_lesson)
                                                        <span class="badge premium-trial-badge">
                                                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}" width="12.941px" height="12.941px" alt="Coin"/>
                                                            <span>PRO</span>
                                                        </span>
                                                    @elseif($lesson->trial_lesson)
                                                        <span class="badge pro-trial-badge">
                                                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}" width="12.941px" height="12.941px" alt="Coin"/>
                                                            <span>{{get_phrase('PRO TRIAL')}}</span>
                                                        </span>
                                                    @else
                                                        <span class="badge free-badge">
                                                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/coin.svg') }}" width="11" height="11" alt="Coin"/>
                                                            <span>{{get_phrase('FREE')}}</span>
                                                        </span>
                                                    @endif
                                                    @if($lesson->is_important)
                                                        <span class="badge important-badge">
                                                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/important.svg') }}" width="11" height="11" alt="IMPORTANT"/>
                                                            <span>{{get_phrase('IMPORTANT')}}</span>
                                                        </span>
                                                    @endif
                                                    @php $duration = $section_data['lesson_durations'][$lesson->id]; @endphp
                                                    @if($duration != '00:00:00')
                                                        <span class="duration-small">
                                                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/clock.svg') }}" alt="Clock"/>
                                                            <strong>{{ $duration }}</strong>
                                                        </span>
                                                    @endif
                                                </div>
                                            </a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="col-md-6">
                <div class="registration-card">
                    <div class="course-info">
                        <div class="d-flex align-items-center">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/calendar.svg') }}" alt="Calendar" class="course-info-icon" width="33px" height="35px"/>
                            <div>
                                <p class="text-text16">Thời điểm ưu đãi:</p>
                                <p class="course-time text-color-alert text-h6">
                                    @if($has_price_trends)
                                        <span class="highlight-text">{{ $current_stage }}</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="divider"></div>
                        <div class="d-flex align-items-center">
                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/zoom.png') }}" width="48px" height="48px" alt="Zoom" class="course-info-icon"/>
                            <div class="course-platform">
                                <p class="text-text16">Học online qua:</p>
                                <p class="text-h6">
                                    @if($course_details->level == 'videoandzoom')
                                        Zoom & Video
                                    @elseif($course_details->level == 'video')
                                        Video
                                    @elseif($course_details->level == 'ebook')
                                        Ebook
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="registration-section">
                        <div class="registration-section-wrapper">
                            <h3 class="registration-section-title">ĐĂNG KÝ NGAY – NHẬN ƯU ĐÃI</h3>

                            @if($has_price_trends)
                                <div class="countdown-section text-center" id="countdown-presell" style="display: none">
                                    <p class="text-text16">Đừng bỏ lỡ cơ hội còn:</p>
                                    <div class="countdown-timer" data-countdown="{{ $countdown_date }}">
                                        <div class="timer-box time-day">
                                            <span class="timer-box-number">00</span>
                                            <span class="timer-box-label">Ngày</span>
                                        </div>
                                        <div class="timer-box time-hour">
                                            <span class="timer-box-number">00</span>
                                            <span class="timer-box-label">Giờ</span>
                                        </div>
                                        <div class="timer-box time-minute">
                                            <span class="timer-box-number">00</span>
                                            <span class="timer-box-label">Phút</span>
                                        </div>
                                        <div class="timer-box time-second">
                                            <span class="timer-box-number">00</span>
                                            <span class="timer-box-label">Giây</span>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <div class="pricing-tiers">
                                @if($has_price_trends)
                                    @foreach($processed_tiers as $tier)
                                        <div class="tier-item d-flex align-items-start {{ $tier['is_active'] ? 'active' : '' }}" data-targetDate="{{ $tier['start_date'] }}">
                                            <span class="tier-number text-h6 bg-secondary">{{ $tier['index'] }}</span>
                                            <div class="tier-item-tail"></div>
                                            <div class="tier-item-content">
                                                <div class="tier-badge text-h6 text-color-alert {{ $tier['badge_class'] }} {{ $tier['is_active'] ? 'active' : '' }}">
                                                    {{ $tier['badge_text'] }}:
                                                    @if(!empty($tier['formatted_date']))
                                                        ( {{ $tier['formatted_date'] }} )
                                                    @endif
                                                </div>
                                                @if($tier['is_last_tier'])
                                                    <div class="tier-item-price price-regular">
                                                        Học phí sẽ trở về:
                                                        <strong class="text-color-secondary">
                                                            {{ $tier['formatted_price'] }} VNĐ
                                                        </strong>
                                                    </div>
                                                @else
                                                    <div class="tier-item-price price-discount">
                                                        <strong class="text-color-alert">
                                                            Giảm {{ $tier['discount_percent'] }}% </strong>
                                                        còn
                                                        <strong class="text-color-secondary">
                                                            {{ $tier['formatted_price'] }} VNĐ
                                                        </strong>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <!-- Fixed price display -->
                                    <div class="fixed-price-container">
                                        <div class="discount-badge">
                                            <span>GIẢM {{ $discount_percent }}%</span>
                                        </div>
                                        <div class="price-display">
                                            <div class="original-price">
                                                <span class="price-label">Giá gốc:</span>
                                                <span class="price-value">{{ $formatted_original_price }} VNĐ</span>
                                            </div>
                                            <div class="sale-price">
                                                <span class="price-label">Giá ưu đãi:</span>
                                                <span class="price-value">{{ $formatted_sale_price }} VNĐ</span>
                                            </div>
                                        </div>
                                        <div class="savings">
                                            <span class="savings-text">Tiết kiệm:</span>
                                            <span class="savings-value">{{ $savings }} VNĐ</span>
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <div class="bonus-section">
                                <p class="bonus-section-title mb-2">
                                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/gift.svg') }}" alt="Gift" width="17px" height="17px"/>
                                    <span> Quà tặng kèm: </span>
                                </p>
                                <ul class="bonus-list">
                                    <li>Ebook "Xây dựng thương hiệu cá nhân trên Shopee" (trị giá 499k)</li>
                                    <li>Nhóm hỗ trợ riêng trên Facebook/Zalo: Hỏi-đáp 24/7 để tăng tỷ lệ chốt mua khóa học</li>
                                </ul>
                            </div>
                        </div>

                        <div class="d-flex action-buttons">
                            @if ($enrollment_status)
                                <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}" class="text-center start-course-btn">
                                    <img src="{{ asset('assets/frontend/course-shopee/assets/icons/video-play.svg') }}" style="filter: invert(1);"/>
                                    VÀO HỌC NGAY!
                                </a>
                            @else
                                <button class="buy-course-btn text-color-white" type="button" data-bs-toggle="modal" data-bs-target="#modal-regis">
                                    <span class="buy-course-btn-icon">
                                        <img src="{{ asset('assets/frontend/course-shopee/assets/icons/crown-icon.svg') }}" alt="Crown" width="22" height="22"/>
                                    </span>
                                    <span>MUA KHÓA HỌC</span>
                                </button>
                                @if(Auth()->check())
                                    <a href="{{ route('course.player', ['slug' => $course_details->slug]) }}" class="try-free-btn text-color-white">
                                        <span class="try-free-btn-icon">
                                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/coin.svg') }}" alt="Try" width="22" height="22"/>
                                        </span>
                                        <span>HỌC THỬ MIỄN PHÍ</span>
                                    </a>
                                @else
                                    <button class="try-free-btn text-color-white" type="button" data-bs-toggle="modal" data-bs-target="#modal-auth">
                                        <span class="try-free-btn-icon">
                                            <img src="{{ asset('assets/frontend/course-shopee/assets/icons/coin.svg') }}" alt="Try" width="22" height="22"/>
                                        </span>
                                        <span>HỌC THỬ MIỄN PHÍ</span>
                                    </button>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<div class="target-section">
    <div class="container">
        <div class="text-center mb-lg-5 mb-3">
            <div class="course-badge">Khoá học bán hàng shopee này</div>
            <h1 class="section-title txt-gradient-orange">Dành cho những ai?</h1>
        </div>

        <div class="row">
            <!-- Nhân viên văn phòng -->
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card user-card">
                    <img
                        src="{{ asset('assets/frontend/course-shopee/assets/images/users/img-nhan-vien-van-phong-min.png') }}"
                        alt="Nhân viên văn phòng" class="card-img-top"/>
                    <div class="card-body">
                        <h5 class="user-type txt-gradient-orange">
                            Nhân viên văn phòng:
                        </h5>
                        <p class="user-description">
                            Khao khát làm thêm trên Shopee để tăng thu nhập, cần lộ trình
                            rõ ràng.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Chủ shop nhỏ -->
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card user-card">
                    <img
                        src="{{ asset('assets/frontend/course-shopee/assets/images/users/img-chu-shop-nho-1-min.png') }}"
                        alt="Chủ shop nhỏ" class="card-img-top"/>
                    <div class="card-body">
                        <h5 class="user-type txt-gradient-orange">Chủ shop nhỏ:</h5>
                        <p class="user-description">
                            Đã có shop nhưng doanh thu èo uột, muốn bứt phá vượt đối thủ.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Chủ shop thất bại -->
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card user-card">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/images/users/img-me-bim-sua-min.png') }}"
                         alt="Chủ shop thất bại" class="card-img-top"/>
                    <div class="card-body">
                        <h5 class="user-type txt-gradient-orange">
                            Chủ shop thất bại:
                        </h5>
                        <p class="user-description">
                            Gian hàng ế ẩm, quảng cáo không hiệu quả, cần cách hồi sinh
                            doanh thu.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Mẹ bỉm sữa -->
            <div class="col-md-6 col-lg-3 mb-3">
                <div class="card user-card">
                    <img
                        src="{{ asset('assets/frontend/course-shopee/assets/images/users/img-chu-shop-nho-2-min.png') }}"
                        alt="Mẹ bỉm sữa" class="card-img-top"/>
                    <div class="card-body">
                        <h5 class="user-type txt-gradient-orange">Mẹ bỉm sữa:</h5>
                        <p class="user-description">
                            Muốn kiếm tiền online nhưng thiếu thời gian và kinh nghiệm,
                            học 2h/tuần là đủ!
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Expert Section -->
<div class="expert-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-6">
                <h1 class="expert-name">TRẦN MINH ĐỨC</h1>
                <h3 class="expert-title">Chuyên gia thực chiến</h3>

                <div class="expert-quote-box">
                    <div class="expert-quote">
                        <div class="expert-quote-icon mb-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="35" height="29" viewBox="0 0 35 29"
                                 fill="none">
                                <path
                                    d="M33.25 28.626L34.6912 25.5618C33.3186 24.8574 32.0147 24.0826 30.7794 23.2373C29.5441 22.3216 28.549 21.1241 27.7941 19.6448C27.0392 18.0952 26.6618 16.0876 26.6618 13.6222C27.0735 13.6926 27.4853 13.7631 27.8971 13.8335C28.2402 13.904 28.5833 13.9392 28.9265 13.9392C30.5735 13.9392 32.0147 13.3757 33.25 12.2486C34.4167 11.1216 35 9.60711 35 7.70522C35 5.87377 34.3824 4.25365 33.1471 2.84484C31.8431 1.3656 29.9902 0.625977 27.5882 0.625977C25.5294 0.625977 23.6078 1.3656 21.8235 2.84484C19.9706 4.25365 19.0441 6.89516 19.0441 10.7694C19.0441 13.587 19.5588 16.0172 20.5882 18.0599C21.549 20.1027 22.8186 21.8285 24.397 23.2373C25.9069 24.6461 27.451 25.8084 29.0294 26.7241C30.6078 27.5694 32.0147 28.2033 33.25 28.626ZM14.2059 28.626L15.6471 25.5618C14.2745 24.8574 12.9706 24.0826 11.7353 23.2373C10.5 22.3216 9.50491 21.1241 8.75001 19.6448C7.99511 18.0952 7.61765 16.0876 7.61765 13.6222C8.02942 13.6926 8.44119 13.7631 8.85295 13.8335C9.19609 13.904 9.53922 13.9392 9.88236 13.9392C11.5294 13.9392 12.9706 13.3757 14.2059 12.2486C15.3726 11.1216 15.9559 9.60711 15.9559 7.70522C15.9559 5.87377 15.3383 4.25365 14.103 2.84484C12.799 1.3656 10.9461 0.625977 8.54413 0.625977C6.4853 0.625977 4.56373 1.3656 2.77942 2.84484C0.926472 4.25365 0 6.89516 0 10.7694C0 13.587 0.514706 16.0172 1.54412 18.0599C2.5049 20.1027 3.77452 21.8285 5.35295 23.2373C6.86275 24.6461 8.40687 25.8084 9.9853 26.7241C11.5637 27.5694 12.9706 28.2033 14.2059 28.626Z"
                                    fill="url(#paint0_linear_5454_97)"/>
                                <defs>
                                    <linearGradient id="paint0_linear_5454_97" x1="18.5294" y1="0.625977" x2="18.5294"
                                                    y2="28.626" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="white"/>
                                        <stop offset="1" stop-color="white" stop-opacity="0"/>
                                    </linearGradient>
                                </defs>
                            </svg>
                        </div>
                        Khoá học này là tất cả những gì tôi học được, tôi ở đây để chia
                        sẻ tất cả không giữ lại bất kì bí quyết nào để bạn có thể đi con
                        đường nhanh hơn tôi từng đi!
                    </div>

                    <div class="teacher-box">
                        <img src="{{ asset('assets/frontend/course-shopee/assets/images/giangvien.png') }}"
                             alt="teacher"/>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <h2 class="mb-4 achievement-title">Hành trình đáng kinh ngạc!</h2>

                <div class="achievement-badge w-100 mb-3">
                    <div class="fw-bold ach-title">Doanh thu cá nhân 10 tỷ/năm</div>
                    <div class="achievement-text">
                        Từ một người không biết gì về Shopee, tôi tự xây gian hàng đạt
                        doanh thu khủng chỉ sau 2 năm kiên trì thử nghiệm.
                    </div>
                </div>

                <div class="achievement-badge w-100 mb-3">
                    <div class="fw-bold ach-title">
                        Đào tạo 3.000 học viên thành công
                    </div>
                    <div class="achievement-text">
                        Từ một người không biết gì về Shopee, tôi tự xây gian hàng đạt
                        doanh thu khủng chỉ sau 2 năm kiên trì thử nghiệm.
                    </div>
                </div>

                <div class="achievement-badge w-100 mb-3">
                    <div class="fw-bold ach-title">Báo chí công nhận</div>
                    <div class="achievement-text">
                        Được Báo Doanh Nghiệp gọi là "Biểu tượng thành công Shopee" nhờ
                        cách tiếp cận thực chiến, không lý thuyết suông
                    </div>
                </div>

                <div class="achievement-badge w-100 mb-3">
                    <div class="fw-bold ach-title">
                        Người tiên phong tối ưu Shopee
                    </div>
                    <div class="achievement-text">
                        Tôi là một trong những người đầu tiên tại Việt Nam nghiên cứu và
                        áp dụng SEO Shopee, giúp hàng trăm gian hàng lên top tìm kiếm.
                    </div>
                </div>

                <div class="achievement-badge w-100">
                    <div class="fw-bold ach-title">Hỗ trợ cộng đồng</div>
                    <div class="achievement-text">
                        Tặng miễn phí hàng chục workshop, giúp người bán hàng vượt qua
                        khó khăn ban đầu để bứt phá doanh thu.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<section class="brand-section">
    <div class="container">
        <div class="brand-list justify-content-center align-items-center">
            <div class="text-center brand-item">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/brand-1.png') }}" alt="Brand 1"
                     class="img-fluid" width="75" height="25"/>
            </div>
            <div class="text-center brand-item">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/brand-2.png') }}" alt="Brand 2"
                     class="img-fluid" width="102" height="34"/>
            </div>
            <div class="text-center brand-item">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/brand-3.png') }}" alt="Brand 3"
                     class="img-fluid" width="92" height="31"/>
            </div>
            <div class="text-center brand-item">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/brand-4.png') }}" alt="Brand 4"
                     class="img-fluid" width="99" height="33"/>
            </div>
            <div class="text-center brand-item">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/brand-5.png') }}" alt="Brand 5"
                     class="img-fluid" width="128" height="43"/>
            </div>
            <div class="text-center brand-item">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/brand-6.png') }}" alt="Brand 6"
                     class="img-fluid" width="112" height="37"/>
            </div>
            <div class="text-center brand-item">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/brand-7.png') }}" alt="Brand 7"
                     class="img-fluid" width="113" height="38"/>
            </div>
            <div class="text-center brand-item">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/brand-8.png') }}" alt="Brand 8"
                     class="img-fluid" width="126" height="33"/>
            </div>
        </div>
    </div>
</section>

<!-- feedback section -->
<div class="feedback-section pb-5" id="hoc-vien">
    <div class="container">
        <div class="feedback-title txt-gradient-orange">3,000+</div>
        <div class="feed-title-2">Học Viên Đã Thành Công</div>

        <div class="feed-wrapper">
            <div class="feed-imgs">
                <div class="feed-img">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/images/feedback/feedback-1.png') }}"
                         alt="Feedback 1"/>
                </div>
                <div class="feed-img">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/images/feedback/feedback-1.png') }}"
                         alt="Feedback 1"/>
                </div>
                <div class="feed-img">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/images/feedback/feedback-1.png') }}"
                         alt="Feedback 1"/>
                </div>
                <div class="feed-img">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/images/feedback/feedback-1.png') }}"
                         alt="Feedback 1"/>
                </div>
                <div class="feed-img">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/images/feedback/feedback-1.png') }}"
                         alt="Feedback 1"/>
                </div>
                <div class="feed-img">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/images/feedback/feedback-1.png') }}"
                         alt="Feedback 1"/>
                </div>
                <div class="feed-img">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/images/feedback/feedback-1.png') }}"
                         alt="Feedback 1"/>
                </div>
                <div class="feed-img">
                    <img src="{{ asset('assets/frontend/course-shopee/assets/images/feedback/feedback-1.png') }}"
                         alt="Feedback 1"/>
                </div>
            </div>
            <div class="d-flex justify-content-center mt-4">
                <div class="box-phh">
                    <span>Xem thêm phản hồi từ học viên</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
                        <path d="M5.56395 9.29272L8.89728 12.6261L12.2306 9.29272" stroke="white" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M5.56395 4.62598L8.89728 7.95931L12.2306 4.62598" stroke="white" stroke-width="2"
                              stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- FAQ Section -->
<div class="faq-section" id="hoi-dap">
    <div class="container">
        <div class="faq-badge">FAQ Về Khoá Học</div>
        <h2 class="faq-title">Giải đáp mọi thắc mắc</h2>

        <div class="row justify-content-center">
            <div class="col-sm-12 col-lg-6">
                <!-- FAQ 1 (Open) -->
                <div class="faq-card active">
                    <div class="faq-question">
                        <div>
                            Khoá học bán hàng Shopee này có phù hợp với người mới bắt đầu
                            không?
                        </div>
                        <div class="faq-icon">−</div>
                    </div>
                    <div class="faq-answer">
                        Hoàn toàn phù hợp! Dành cho cả người chưa từng bán hàng trên
                        Shopee. Tôi hướng dẫn những kinh nghiệm xương máu để bạn đi đúng
                        ngay từ đầu!
                    </div>
                </div>

                <!-- FAQ 3 -->
                <div class="faq-card">
                    <div class="faq-question">
                        <div>
                            Khoá học bán hàng Shopee này có phù hợp với người mới bắt đầu
                            không?
                        </div>
                        <div class="faq-icon">+</div>
                    </div>
                    <div class="faq-answer">
                        Hoàn toàn phù hợp! Dành cho cả người chưa từng bán hàng trên
                        Shopee.
                    </div>
                </div>

                <!-- FAQ 5 -->
                <div class="faq-card">
                    <div class="faq-question">
                        <div>
                            Khoá học bán hàng Shopee này có phù hợp với người mới bắt đầu
                            không?
                        </div>
                        <div class="faq-icon">+</div>
                    </div>
                    <div class="faq-answer">
                        Hoàn toàn phù hợp! Dành cho cả người chưa từng bán hàng trên
                        Shopee.
                    </div>
                </div>
            </div>

            <div class="col-sm-12 col-lg-6">
                <!-- FAQ 2 -->
                <div class="faq-card">
                    <div class="faq-question">
                        <div>
                            Khoá học bán hàng Shopee này có phù hợp với người mới bắt đầu
                            không?
                        </div>
                        <div class="faq-icon">+</div>
                    </div>
                    <div class="faq-answer">
                        Hoàn toàn phù hợp! Dành cho cả người chưa từng bán hàng trên
                        Shopee.
                    </div>
                </div>

                <!-- FAQ 4 -->
                <div class="faq-card">
                    <div class="faq-question">
                        <div>
                            Khoá học bán hàng Shopee này có phù hợp với người mới bắt đầu
                            không?
                        </div>
                        <div class="faq-icon">+</div>
                    </div>
                    <div class="faq-answer">
                        Hoàn toàn phù hợp! Dành cho cả người chưa từng bán hàng trên
                        Shopee.
                    </div>
                </div>

                <!-- FAQ 6 -->
                <div class="faq-card">
                    <div class="faq-question">
                        <div>
                            Khoá học bán hàng Shopee này có phù hợp với người mới bắt đầu
                            không?
                        </div>
                        <div class="faq-icon">+</div>
                    </div>
                    <div class="faq-answer">
                        Hoàn toàn phù hợp! Dành cho cả người chưa từng bán hàng trên
                        Shopee.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="footer-app">
    <div class="container">
        <div class="wrap-footer">
            <div class="logo-footer">
                <img src="{{ asset('assets/frontend/course-shopee/assets/images/logo-foot.png') }}" alt="logo-footer"/>
            </div>
            <div class="footer-app-content">KHÓA HỌC BÁN HÀNG SHOPEE</div>
            <div class="footer-call">
                <a href="tel:0123456789" class="footer-call-btn">
                    <div class="call-icon">
                        <span class="bi bi-telephone"></span>
                    </div>
                    <span class="call-number">0123 456 789</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- modal regis -->
@include('partials.modals.checkout_payment',["offline_payment"=>$offline_payment])

<!--  modal login -->
@include('partials.modals.register_login')
<script>
    $(document).ready(function() {
        // Optimized countdown timer functionality
        function updateCountdown() {
            const countdownElement = $(".countdown-timer");
            if (!countdownElement.length) return;

            const targetDate = new Date(countdownElement.data("countdown"));
            const now = new Date();
            const difference = targetDate - now;

            if (difference <= 0) {
                // Countdown has ended
                appendTimerBox("00", "00", "00", "00");
                $("#countdown-presell").hide();
                return;
            }

            $("#countdown-presell").show();

            // Calculate time units
            const days = Math.floor(difference / (1000 * 60 * 60 * 24));
            const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((difference % (1000 * 60)) / 1000);

            // Format with leading zeros
            const formattedDays = days < 10 ? "0" + days : days;
            const formattedHours = hours < 10 ? "0" + hours : hours;
            const formattedMinutes = minutes < 10 ? "0" + minutes : minutes;
            const formattedSeconds = seconds < 10 ? "0" + seconds : seconds;

            // Update countdown display
            appendTimerBox(formattedDays, formattedHours, formattedMinutes, formattedSeconds);
        }

        function appendTimerBox(days, hours, minutes, seconds) {
            const countdownElement = $(".countdown-timer");
            countdownElement.html(
                `<div class='timer-box time-day'>
                <span class='timer-box-number'>${days}</span>
                <span class='timer-box-label'>Ngày</span>
                </div><div class='timer-box time-hour'>
                <span class='timer-box-number'>${hours}</span>
                <span class='timer-box-label'>Giờ</span>
                </div><div class='timer-box time-minute'>
                <span class='timer-box-number'>${minutes}</span>
                <span class='timer-box-label'>Phút</span>
                </div><div class='timer-box time-second'>
                <span class='timer-box-number'>${seconds}</span>
                <span class='timer-box-label'>Giây</span>
                </div>`
            );
        }

        // Update tier activation status based on dates
        function updateTierStatus() {
            const now = new Date();
            let activeFound = false;

            // Check each tier item to determine active status
            $(".tier-item").each(function() {
                const targetDateStr = $(this).data("targetdate");
                if (targetDateStr) {
                    const targetDate = new Date(targetDateStr);
                    if (now >= targetDate) {
                        $(this).addClass("active");
                        activeFound = true;
                    } else {
                        $(this).removeClass("active");
                    }
                }
            });

            // If no tier is active, activate the first one
            if (!activeFound) {
                $(".tier-item:first").addClass("active");
            }
        }

        // Initialize countdown and update every second
        updateCountdown();
        const countdownInterval = setInterval(updateCountdown, 1000);

        // Initialize tier status
        updateTierStatus();
    });
</script>
<style>

    .modules-section .countdown-section {
        border-radius: 15px;
        background: linear-gradient(180deg, #fff0f0 0%, #fed 100%);
        width: fit-content;
        margin: 13px auto 0;
        padding: 10px 30px;
    }

    .modules-section .countdown-section > p {
        color: #333;
        margin-bottom: 2px;
        font-size: 18px;
        font-weight: 600;
    }

    .modules-section .countdown-timer {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
        color: #ef7318;
        text-align: center;
        font-variant-numeric: lining-nums proportional-nums;
        font-family: Inter, sans-serif;
        font-size: 32px;
        font-style: normal;
        font-weight: 800;
        line-height: 28px; /* 87.5% */
        letter-spacing: 1.92px;
        gap: 15px;
        margin-top: 10px;
    }

    .countdown-timer .timer-box {
        display: flex;
        flex-direction: column;
        border: 2px solid #ef7318;
        padding: 10px 6px;
        border-radius: 7px;
        gap: 4px;
        color: #ef7318;
        width: 66px;
    }

    .countdown-timer .timer-box .timer-box-label {
        font-size: 16px;
        text-transform: uppercase;
    }

    .elearning-course .course-header .nav-link.active svg * {
        fill: #eb2805 !important;
    }

    /* Styles for fixed price display */
    .fixed-price-container {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        padding: 25px;
        margin: 20px 0;
        position: relative;
        overflow: hidden;
        border: 2px solid #ff6600;
    }

    .discount-badge {
        position: absolute;
        top: 0;
        right: 0;
        background-color: #ff6600;
        color: #fff;
        padding: 8px 15px;
        font-weight: bold;
        font-size: 16px;
        border-bottom-left-radius: 12px;
        box-shadow: -2px 2px 5px rgba(0, 0, 0, 0.1);
    }

    .price-display {
        display: flex;
        flex-direction: column;
        gap: 15px;
        margin: 15px 0;
    }

    .original-price, .sale-price {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .original-price .price-label {
        font-size: 18px;
        color: #666;
    }

    .original-price .price-value {
        font-size: 20px;
        color: #888;
        text-decoration: line-through;
        position: relative;
    }

    .sale-price .price-label {
        font-size: 20px;
        font-weight: bold;
        color: #333;
    }

    .sale-price .price-value {
        font-size: 26px;
        font-weight: bold;
        color: #ff6600;
    }

    .savings {
        margin-top: 20px;
        background-color: #f8f9fa;
        padding: 12px;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-left: 4px solid #ff6600;
    }

    .savings-text {
        font-size: 16px;
        color: #555;
    }

    .savings-value {
        font-size: 18px;
        font-weight: bold;
        color: #28a745;
    }

    @media (max-width: 768px) {
        .price-display {
            gap: 10px;
        }

        .original-price .price-label,
        .sale-price .price-label {
            font-size: 16px;
        }

        .original-price .price-value {
            font-size: 18px;
        }

        .sale-price .price-value {
            font-size: 22px;
        }

        .savings {
            padding: 10px;
        }

        .savings-text,
        .savings-value {
            font-size: 15px;
        }
    }
</style>
